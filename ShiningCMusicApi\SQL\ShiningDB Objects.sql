USE [Shining]
GO

/****** Object:  Table [dbo].[Clients]    Script Date: 10/05/2025 2:21:23 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Clients](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[ClientId] [nvarchar](200) NOT NULL,
	[ClientSecret] [nvarchar](2000) NOT NULL,
	[AccessTokenLifetime] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
UNIQUE NONCLUSTERED 
(
	[ClientId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[ClientGrantTypes]    Script Date: 10/05/2025 2:21:33 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ClientGrantTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[ClientId] [nvarchar](200) NOT NULL,
	[GrantType] [nvarchar](200) NOT NULL
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ClientGrantTypes]  WITH CHECK ADD  CONSTRAINT [FK_ClientGrantTypes_ClientId] FOREIGN KEY([ClientId])
REFERENCES [dbo].[Clients] ([ClientId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ClientGrantTypes] CHECK CONSTRAINT [FK_ClientGrantTypes_ClientId]
GO

/****** Object:  Table [dbo].[ClientScopes]    Script Date: 10/05/2025 2:21:28 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ClientScopes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[ClientId] [nvarchar](200) NOT NULL,
	[Scope] [nvarchar](200) NOT NULL
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ClientScopes]  WITH CHECK ADD  CONSTRAINT [FK_ClientScopes_ClientId] FOREIGN KEY([ClientId])
REFERENCES [dbo].[Clients] ([ClientId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ClientScopes] CHECK CONSTRAINT [FK_ClientScopes_ClientId]
GO

--Add Auth Clients
INSERT INTO Clients (ClientId, ClientSecret, AccessTokenLifetime)
VALUES 
  ('wasm_client', 'AE6qbzhQ08kW', 3600),
  ('admin', 'WpujhG4r3h0K', 3600)

INSERT INTO ClientScopes (ClientId, Scope)
VALUES
  ('wasm_client', 'ShiningCMusicApi'),
  ('admin', 'ShiningCMusicApi')

INSERT INTO ClientGrantTypes (ClientId, GrantType)
VALUES
  ('wasm_client', 'client_credentials'),
  ('admin', 'client_credentials')


/****** Object:  Table [dbo].[Applications]    Script Date: 25/05/2025 2:03:59 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[EmailTemplates](
	[Name] [nvarchar](50) NOT NULL,
	[CcEmailAddresses] [nvarchar](256) NULL,
	[BccEmailAddresses] [nvarchar](256) NULL,
	[Subject] [nvarchar](500) NULL,
	[BodyText] [nvarchar](max) NULL,
	[BodyHtml] [nvarchar](max) NULL,
 CONSTRAINT [PK_EmailTemplates] PRIMARY KEY CLUSTERED
(
	[Name] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO


CREATE TABLE [dbo].[EmailAttachments](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[TemplateName] [nvarchar](50) NOT NULL,
	[AttachmentName] [nvarchar](256) NOT NULL,
	[AttachmentPath] [nvarchar](512) NOT NULL,
 CONSTRAINT [PK_EmailAttachments] PRIMARY KEY CLUSTERED
(
	[ID] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Tutors]    Script Date: 22/06/2025 8:09:49 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Tutors](
	[TutorId] [int] IDENTITY(1,1) NOT NULL,
	[TutorName] [nvarchar](50) NULL,
	[Email] [nvarchar](250) NULL,
	[CreatedUTC] [datetime] NOT NULL,
	[UpdatedUTC] [datetime] NULL,
	[IsArchived] [bit] NOT NULL,
	[LoginName] [nvarchar](20) NULL,
	[Password] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[TutorId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Tutors] ADD  CONSTRAINT [DF_tutors_CreatedUTC]  DEFAULT (getutcdate()) FOR [CreatedUTC]
GO

ALTER TABLE [dbo].[Tutors] ADD  CONSTRAINT [DF_tutors_IsArchived]  DEFAULT ((0)) FOR [IsArchived]
GO

/****** Object:  Table [dbo].[Students]    Script Date: 22/06/2025 8:09:45 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Students](
	[StudentId] [int] IDENTITY(1,1) NOT NULL,
	[StudentName] [nvarchar](50) NULL,
	[Email] [nvarchar](250) NULL,
	[CreatedUTC] [datetime] NOT NULL,
	[UpdatedUTC] [datetime] NULL,
	[IsArchived] [bit] NOT NULL,
	[TutorID] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[StudentId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Students] ADD  CONSTRAINT [DF_students_CreatedUTC]  DEFAULT (getutcdate()) FOR [CreatedUTC]
GO

ALTER TABLE [dbo].[Students] ADD  CONSTRAINT [DF_students_IsArchived]  DEFAULT ((0)) FOR [IsArchived]
GO