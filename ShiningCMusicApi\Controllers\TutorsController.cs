using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TutorsController : ControllerBase
    {
        private readonly ILessonService _lessonService;
        private readonly ILogger<TutorsController> _logger;

        public TutorsController(ILessonService lessonService, ILogger<TutorsController> logger)
        {
            _lessonService = lessonService;
            _logger = logger;
        }

        // GET: api/tutors
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Tutor>>> GetTutors()
        {
            try
            {
                var tutors = await _lessonService.GetTutorsAsync();
                return Ok(tutors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tutors");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
