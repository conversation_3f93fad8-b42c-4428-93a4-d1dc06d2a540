C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\blazor.boot.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\blazor.webassembly.js
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.js
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.js.map
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.native.js
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.native.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.runtime.js
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.runtime.js.map
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\emcc-props.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_CJK.dat
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_EFIGS.dat
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_no_CJK.dat
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Authorization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.Forms.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.Web.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.WebAssembly.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Metadata.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.CSharp.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Abstractions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Binder.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.FileExtensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Json.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.DependencyInjection.Abstractions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.DependencyInjection.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileProviders.Abstractions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileProviders.Physical.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileSystemGlobbing.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Logging.Abstractions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Logging.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Options.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.JSInterop.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.JSInterop.WebAssembly.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.VisualBasic.Core.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.VisualBasic.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Win32.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Win32.Registry.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\mscorlib.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\netstandard.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicApp.pdb
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicApp.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.AppContext.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Buffers.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Concurrent.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Immutable.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.NonGeneric.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Specialized.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.Annotations.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.DataAnnotations.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.EventBasedAsync.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.TypeConverter.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Configuration.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Console.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Core.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.Common.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.DataSetExtensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Contracts.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Debug.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.DiagnosticSource.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.FileVersionInfo.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Process.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.StackTrace.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.TextWriterTraceListener.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Tools.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.TraceSource.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Tracing.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Drawing.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Drawing.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Dynamic.Runtime.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Formats.Asn1.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Formats.Tar.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.Calendars.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.Brotli.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.FileSystem.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.ZipFile.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.AccessControl.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.DriveInfo.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.Watcher.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.IsolatedStorage.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.MemoryMappedFiles.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipelines.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipes.AccessControl.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipes.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.UnmanagedMemoryStream.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Expressions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Parallel.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Queryable.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Memory.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Http.Json.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Http.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.HttpListener.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Mail.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.NameResolution.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.NetworkInformation.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Ping.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Quic.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Requests.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Security.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.ServicePoint.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Sockets.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebClient.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebHeaderCollection.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebProxy.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebSockets.Client.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebSockets.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Numerics.Vectors.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Numerics.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ObjectModel.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.CoreLib.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.DataContractSerialization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Uri.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Xml.Linq.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Xml.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.DispatchProxy.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.ILGeneration.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.Lightweight.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Metadata.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.TypeExtensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.Reader.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.ResourceManager.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.Writer.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.CompilerServices.Unsafe.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.CompilerServices.VisualC.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Handles.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.JavaScript.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.RuntimeInformation.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Intrinsics.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Loader.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Numerics.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Formatters.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Json.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Xml.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.AccessControl.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Claims.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Algorithms.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Cng.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Csp.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Encoding.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.OpenSsl.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.X509Certificates.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Principal.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Principal.Windows.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.SecureString.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ServiceModel.Web.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ServiceProcess.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.CodePages.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encodings.Web.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Json.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.RegularExpressions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Channels.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Overlapped.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Dataflow.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Parallel.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Thread.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.ThreadPool.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Timer.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Transactions.Local.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Transactions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ValueTuple.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Web.HttpUtility.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Web.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Windows.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.Linq.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.ReaderWriter.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.Serialization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XDocument.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XmlDocument.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XmlSerializer.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XPath.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XPath.XDocument.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\WindowsBase.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Mail.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Xml.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Win32.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Numerics.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Sockets.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.ILGeneration.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.CoreLib.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.js.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebClient.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Http.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.Web.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Metadata.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Process.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Core.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipes.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Parallel.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XDocument.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.DataAnnotations.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileSystemGlobbing.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.FileSystem.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.Lightweight.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.ThreadPool.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.DataSetExtensions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.NameResolution.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Formats.Tar.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.TypeExtensions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.RuntimeInformation.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\blazor.webassembly.js.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.UnmanagedMemoryStream.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Security.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Tools.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Ping.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.TraceSource.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Specialized.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Loader.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.Extensions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebHeaderCollection.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ServiceModel.Web.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Requests.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Parallel.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Concurrent.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_CJK.dat.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.native.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.WebAssembly.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.CompilerServices.Unsafe.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.JSInterop.WebAssembly.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.OpenSsl.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Extensions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ValueTuple.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.ReaderWriter.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Tracing.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicApp.pdb.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.ResourceManager.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.TypeConverter.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.Common.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Formats.Asn1.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Web.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.EventBasedAsync.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.X509Certificates.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Json.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.Watcher.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XmlSerializer.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XPath.XDocument.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Authorization.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Transactions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipelines.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.VisualBasic.Core.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Drawing.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.FileExtensions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.native.js.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.CodePages.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.Forms.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.DiagnosticSource.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.CompilerServices.VisualC.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Claims.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Csp.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.StackTrace.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.IsolatedStorage.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.NetworkInformation.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.Serialization.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Configuration.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Timer.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ObjectModel.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\emcc-props.json.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_EFIGS.dat.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.RegularExpressions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\netstandard.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.Writer.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_no_CJK.dat.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Formatters.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Cng.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Channels.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Json.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Principal.Windows.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Http.Json.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Numerics.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Metadata.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Xml.Linq.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.DriveInfo.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Overlapped.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Extensions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.JSInterop.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Dynamic.Runtime.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.Reader.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Json.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Contracts.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.runtime.js.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.js.map.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.Annotations.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Dataflow.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebSockets.Client.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Handles.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Queryable.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.VisualBasic.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.HttpListener.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Principal.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Intrinsics.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Encoding.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Xml.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileProviders.Physical.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.Linq.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.AppContext.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.CSharp.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encodings.Web.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Uri.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Abstractions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Binder.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Algorithms.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Logging.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Memory.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebProxy.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.ZipFile.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Buffers.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Logging.Abstractions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileProviders.Abstractions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebSockets.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Windows.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.Brotli.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Quic.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Options.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.FileVersionInfo.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Primitives.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XPath.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.DispatchProxy.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\blazor.boot.json.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.DataContractSerialization.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XmlDocument.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ServiceProcess.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.runtime.js.map.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.JavaScript.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.DependencyInjection.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Immutable.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.TextWriterTraceListener.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Expressions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Thread.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.NonGeneric.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Extensions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.AccessControl.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Debug.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\mscorlib.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicApp.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\WindowsBase.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.AccessControl.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipes.AccessControl.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Win32.Registry.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.SecureString.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Transactions.Local.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.MemoryMappedFiles.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.ServicePoint.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Console.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.Extensions.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Web.HttpUtility.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Drawing.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Numerics.Vectors.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.Calendars.wasm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicApp.staticwebassets.runtime.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicApp.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicApp.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicApp.pdb
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Authorization.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Components.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Forms.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Web.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Components.WebAssembly.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Metadata.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.JSInterop.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.JSInterop.WebAssembly.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Pipelines.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.CSharp.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.VisualBasic.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Win32.Registry.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.AppContext.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Buffers.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.Immutable.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.Annotations.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Configuration.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Console.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Core.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Data.Common.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Data.DataSetExtensions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Data.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Drawing.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Formats.Asn1.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Formats.Tar.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Globalization.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Compression.Brotli.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Compression.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Linq.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Memory.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Http.Json.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Http.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.HttpListener.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Mail.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Ping.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Quic.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Requests.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Security.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.ServicePoint.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebClient.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebProxy.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Numerics.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ObjectModel.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.DataContractSerialization.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.Uri.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.Xml.Linq.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.Xml.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Emit.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Metadata.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Intrinsics.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Loader.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.AccessControl.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Claims.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Principal.Windows.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Principal.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ServiceModel.Web.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ServiceProcess.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Channels.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Transactions.Local.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Transactions.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ValueTuple.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Web.HttpUtility.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Web.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Windows.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.Linq.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.Serialization.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\WindowsBase.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\mscorlib.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\netstandard.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.CoreLib.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.js
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.js.map
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.native.js
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.native.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.runtime.js
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.runtime.js.map
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\icudt_CJK.dat
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\icudt_EFIGS.dat
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\icudt_no_CJK.dat
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\emcc-props.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.AssemblyInfo.cs
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Authorization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Components.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Components.Forms.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Components.Web.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Components.WebAssembly.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Metadata.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.Abstractions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.Binder.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.FileExtensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.Json.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.DependencyInjection.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.DependencyInjection.Abstractions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.FileProviders.Abstractions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.FileProviders.Physical.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.FileSystemGlobbing.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Logging.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Logging.Abstractions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Options.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.JSInterop.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.JSInterop.WebAssembly.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Pipelines.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.CSharp.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.VisualBasic.Core.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.VisualBasic.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Win32.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Win32.Registry.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.AppContext.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Buffers.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.Concurrent.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.Immutable.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.NonGeneric.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.Specialized.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.Annotations.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.DataAnnotations.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.EventBasedAsync.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.TypeConverter.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Configuration.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Console.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Core.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Data.Common.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Data.DataSetExtensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Data.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Contracts.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Debug.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.DiagnosticSource.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.FileVersionInfo.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Process.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.StackTrace.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.TextWriterTraceListener.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Tools.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.TraceSource.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Tracing.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Drawing.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Drawing.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Dynamic.Runtime.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Formats.Asn1.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Formats.Tar.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Globalization.Calendars.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Globalization.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Globalization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Compression.Brotli.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Compression.FileSystem.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Compression.ZipFile.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Compression.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.AccessControl.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.DriveInfo.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.Watcher.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.IsolatedStorage.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.MemoryMappedFiles.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Pipes.AccessControl.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Pipes.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.UnmanagedMemoryStream.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Linq.Expressions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Linq.Parallel.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Linq.Queryable.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Linq.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Memory.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Http.Json.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Http.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.HttpListener.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Mail.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.NameResolution.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.NetworkInformation.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Ping.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Quic.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Requests.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Security.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.ServicePoint.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Sockets.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebClient.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebHeaderCollection.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebProxy.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebSockets.Client.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebSockets.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Numerics.Vectors.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Numerics.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ObjectModel.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.DataContractSerialization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.Uri.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.Xml.Linq.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.Xml.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.DispatchProxy.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Emit.ILGeneration.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Emit.Lightweight.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Emit.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Metadata.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.TypeExtensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Resources.Reader.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Resources.ResourceManager.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Resources.Writer.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.CompilerServices.Unsafe.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.CompilerServices.VisualC.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Handles.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.InteropServices.JavaScript.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.InteropServices.RuntimeInformation.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.InteropServices.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Intrinsics.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Loader.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Numerics.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.Formatters.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.Json.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.Xml.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.AccessControl.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Claims.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Algorithms.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Cng.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Csp.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Encoding.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.OpenSsl.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Primitives.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.X509Certificates.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Principal.Windows.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Principal.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.SecureString.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ServiceModel.Web.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ServiceProcess.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Encoding.CodePages.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Encoding.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Encoding.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Encodings.Web.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Json.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.RegularExpressions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Channels.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Overlapped.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Tasks.Dataflow.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Tasks.Extensions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Tasks.Parallel.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Tasks.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Thread.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.ThreadPool.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Timer.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Transactions.Local.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Transactions.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ValueTuple.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Web.HttpUtility.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Web.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Windows.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.Linq.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.ReaderWriter.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.Serialization.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XDocument.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XPath.XDocument.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XPath.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XmlDocument.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XmlSerializer.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\WindowsBase.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\mscorlib.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\netstandard.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.CoreLib.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\ShiningCMusicApp.wasm
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\scopedcss\bundle\ShiningCMusicApp.styles.css
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\blazor.build.boot-extension.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\blazor.boot.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\5j8dxmne5b-mv535bwyet.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\dy5lkhjkn1-xtjqgewnsy.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f17o09ymz1-vbl3iftxmx.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f813afr1vj-i47vxqdqw2.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1gdl188rtq-y9j53ldofb.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\99xx5yiczq-3uudqrjyld.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1jvnjphhuc-txus4zzmh1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\sd4w29iblj-4njtqvtvgx.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qmrh3cyln1-8kr5d0tjmo.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qq3w8mqh2l-0r3amze666.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f0p7mhideg-en8mb8dgz5.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\luw69m5zpj-yy6f57640l.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\vhswmu8kpf-xqsu2wsvba.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\fg6rcqyzob-kgyjb8k43h.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rzc0mkqxf8-1c7ksbormu.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pyud5iqoj0-rpvltkbyzt.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3wl2w0d9cv-i464dwxnbb.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\r2q3mj3u9h-xlpspxuy08.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rvv7resapu-tz325eqvv5.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\sx4xzqaqm5-jt8xzja2dj.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ta7m7j2gei-lsakbjp1fg.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ldqgtsc41p-ae1qwufxjk.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ah5uj9fkaq-tr42ods1qv.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\e97ynr2icq-iudrcw56e1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pzxdtik6zo-vz4qn40jhg.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\egcrwen6o7-jmn0i77l29.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ojmli06k3u-fm393pmbxu.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0khw4724ee-twvzipegq8.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\yi1kr6g9b3-d6mm508bxf.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pzjo06rbey-kbohc9o1m1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rmczs60qrj-m4v28j00i9.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\8pinc6rjf7-i9p3knz3yt.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\vzisnnpkmx-scytofhtr9.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xde021tovc-o3jetrtgnh.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7a5kyzpvkv-3guih0sdew.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jimaxq9mi6-g8vdrlz0h3.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\n257kqytbc-4xvg71hsgb.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3paocsqo5e-0i9v6frssz.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cap10iz261-5i9kdzvcou.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\9ssd939fn0-gj6riry0jn.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\byt3nyxy2q-di3hb82ekm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\c39t6asywb-ywffyfl1am.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\hiyuk85oo1-x4qb3ksms4.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z9316qi1ji-323u56jecy.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1umd06yrfb-p72f735h1w.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\bz3avbczq2-i1cfmix8mq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\4wpiy2o4d3-jffdabxzvr.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\9wtv5r574i-213suq4adk.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\m3qtwn3ns2-gsqopizbl2.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xnalvtn1mb-xgbrvo4ldx.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\fnpgxujk9y-pqtqvld792.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\sxppidzjgj-6kzlufl37h.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1lzqee7hvd-8hnjisj3vj.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\gdmgran5y7-k06ub8s3dw.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\wdlm54q4fw-neuj33e7eq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\67lx4f78cj-u0uuhmbng1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\77au2vjfld-gbq0j9rogg.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\binqqlz1sy-xitryo2fjc.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\eh357harcp-qvh38h4680.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z9yxv7vuua-a7kh3cywpb.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\leomrziixf-ki27ve9qng.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\c4ro1hju42-nzhd1qr5s1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\57agg9smmy-bdexx8ma0s.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\zr3rxoo55n-mpupmihlv5.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z98cfng326-53g1f3dr4h.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\giup3hxlyd-accc32eqdk.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\smvg5f5rpt-lj6cmgeqhv.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3zji9tv399-j9bfi17b8k.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rgl88w1it9-tjonuda3ro.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lvyrlugiaz-9914j3ld6t.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\y3zikztta2-lzbzsi4vp4.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\l7ml98ds2n-8ze32cjyxh.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\26zimhthez-o0wyu8jhhb.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cumen4e1px-h5doo44qvw.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ohnw08v0s7-si9kvosi14.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\gmtjrsjpjr-d2ylp6awiq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\yqou8ak97v-x1lvlgryd5.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\y9x9709oxs-u3xqd499qg.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\2yj9ptca6s-hyl5xi93ot.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\5oprngc3na-tjuuiz7ypv.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\9rp1nsr78a-dp80ojuscj.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\wqbo3t8krt-yb3kwz1c6r.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\32t05zddb7-e9wuf7k1zv.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\o4a5y23fdq-79f2fraqlk.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\6a5ovfauzx-h8vi2s5bdm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\raqtb0g8tk-87rkvmkemn.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\k90ia90a0l-ini6m98lmo.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1cs6asnscm-8encq3ba69.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\om386z7hvl-5vvgnaetqc.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\01kj3kzu05-xlvmzre8v7.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\4ziudi5er1-xq6yku0xwp.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\gqrdkmv4gr-gxaikxk91b.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\778te4j2e9-tjolrfq28p.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\axunml5apk-kb98r43sqq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\swgmwtsne4-3kqucqumfq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\8bprzflbij-rr45bly91f.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\5peanjzhpf-7z61ik4suh.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z537psxkb5-sapmuuu4mp.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0prwepgyfd-x0p20ara4x.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\12jxpcjsau-e03lbs9l17.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\876btrwotz-w8lzr7pmua.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rb0r3wv541-0h0shlhccg.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\n9ufvg6zor-vneakj5rjf.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\s007umfqyr-xngar079xo.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\momr4swqrq-jv7ufqie0s.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\zlfrj5nqwi-a2es3754ua.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0lktitq3ad-zzk03c4wht.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\hmt6yadul1-a5op9k14zd.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\tqgnr7mkgy-xi6pr7gsb1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qm6lsqstgu-y1exhw7205.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\kt41y9oyfq-h70wyzdiuk.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\07zc013blp-10lsrbini9.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\tesx50ibi3-33bdi2v52p.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0t5acjtnsc-cfpzd27k9i.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\4b9lgapm59-ciy2vhh18w.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lawfelcw3p-u59bsa0kp7.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lc1a89fsfa-vznbhkh3pl.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ksef9slfq8-84o5n6u1ge.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\dlqy23dccu-3jb34ibhsc.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\58erqdvtin-odzer0rtll.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\odya9nxts3-dzx0ks0cuw.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lhpdkrosjy-8i1iwes264.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\bqol8tplix-fqlsii9jkl.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\iwjvmfxqu3-fk7jf4g1i6.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\9m1cnur73y-q5piu9ytbh.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\fw6jev8eke-wltz66tss2.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xklfolbr8t-gt9n46xdyw.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\nmba0r7o2l-1dabjpji6e.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\vb7m02hyiv-db4tvgzi9r.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\5ctps1xp05-971e70funs.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\royp2e729g-tfg95ait3y.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pexc7mp10a-vd1vof0t03.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7gj2dpgwqs-i7t4dnzdf1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\kbgplu5rwn-1cdtdsls49.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\j28a3ppivi-m0ysjrbpmq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jyffsad4v5-3whpij8aar.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7xl7opbj6m-elzo0zycyi.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pv91aidw81-nyutd2rtpo.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\nm3k1vr2b0-t74kskgemo.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\mv2ne30sfa-y7vd4f637g.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xm0a60puc7-5mmyj3ks42.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\fyv9wlngmq-91hew9hfdw.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qrxu2zwgr5-j8a5pndyid.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ja52lhp99e-hpn51boghw.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\g8d1ll62re-9yvf9sc6nz.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\plvxa4s6ga-9gsidvgizk.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\arw8hu9x14-hvgrts035n.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\il592650ow-9l5hpoupx1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ced8wcd8cu-ie7wuje7gz.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\eh7hhep0d3-6ahvfcl73m.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jyu1zp9qgf-gzh3d6w01x.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\p1o86wnkwn-dagr9hk6qd.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ylntj2h544-sdvf66mvkh.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xgis4ptx7x-uutrx1eiiq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\8801tn65l9-qtxbjpw7tr.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\v8iorw1ao4-3ztcjt4twq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f6wx6gd1gt-3rsp0ayfj9.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7x27t74nhm-quym49l40l.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ppfdnigb7q-7w821bbqj1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qdvfj0qa9d-v6qgbzm19a.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cn4p7z7pyl-2zwy1vq629.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ipm780nllg-sr5bakvf42.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jef7gsrqq7-l5qg2pvobx.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\laazswwohk-adjom04y34.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\n4x5zi1hy5-2otck1wy8e.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\awerrgejpl-gooifd3jzk.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\8nxszxol5d-or7zcrdu7s.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ezx0izcezf-8d6wubmj78.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\x959zs3735-7wrh1q4esd.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\4pab78fge1-wtrgt48lgp.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\hlv81p94us-7wytfvdutm.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lx4q04c6vo-6pgl78rhwz.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ym2sc23egi-izt0apbw6j.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\e0p52hsb7u-jc22oyailq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\axs689n286-eitl43ck04.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z9l7ueuc70-0kjmzj2vfa.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cajl2o1e7z-mggxt4ez9q.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\sadg8p8gec-ve8fhmm9ig.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pzgeww6nnf-5sroy1j11o.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\bdjfymnqrw-qe5ielndka.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\gwugds5sw2-zym3pg7f2j.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3kjfjnpc9x-p1bpgybrng.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ddncr1chdl-fx9s7xzte7.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\tb1rghhwn6-5e9alzzrrt.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\uh9y7ps724-0ijqy2z3wa.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\dc9ab4adpy-zobe9wvb4n.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7qt3udra8g-y8tjraturr.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7ycekvj0yv-3aznyawh5t.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xw3xqrs1gn-2gfuv7k5ow.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xv769p21qu-6f4fufr6q4.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\iqwuxofz82-8h1j7mmp89.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0tq3ubgpl3-82bukvnpte.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\12gy8c80sd-ic1b84is8h.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\mrmnme9fy7-xdsszf0p7m.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f2nmvc6iyo-enb7sbctbu.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\92k36dsjrc-exqkxgy9ma.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\mgqvc88zci-x2muqfwqyq.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\v9ucdunf1s-kxbozl8j1s.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\90hp2p71hu-tjcz0u77k5.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\iolod2it8d-tptq2av103.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\j19hqecu3k-lfu7j35m59.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\i4ozfjtw4i-oyz0vx2fzb.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xvf58668z8-echbtwv6v7.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\bqkz6kxeo8-5k5i4saob1.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\tga4xb2zqw-62z3h5lk4g.gz
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets\msbuild.ShiningCMusicApp.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets\msbuild.ShiningCMusicApp.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets\msbuild.build.ShiningCMusicApp.props
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.ShiningCMusicApp.props
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.ShiningCMusicApp.props
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningC.B4CB0432.Up2Date
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\refint\ShiningCMusicApp.dll
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.pdb
C:\Users\<USER>\source\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ref\ShiningCMusicApp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\blazor.webassembly.js
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.js
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.js.map
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.native.js
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.native.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.runtime.js
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.runtime.js.map
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\emcc-props.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_CJK.dat
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_EFIGS.dat
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_no_CJK.dat
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Authorization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.Forms.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.Web.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.WebAssembly.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Metadata.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.CSharp.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Abstractions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Binder.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.FileExtensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Json.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.DependencyInjection.Abstractions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.DependencyInjection.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileProviders.Abstractions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileProviders.Physical.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileSystemGlobbing.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Logging.Abstractions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Logging.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Options.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.JSInterop.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.JSInterop.WebAssembly.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.VisualBasic.Core.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.VisualBasic.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Win32.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Win32.Registry.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\mscorlib.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\netstandard.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.AppContext.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Buffers.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Concurrent.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Immutable.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.NonGeneric.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Specialized.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.Annotations.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.DataAnnotations.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.EventBasedAsync.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.TypeConverter.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Configuration.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Console.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Core.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.Common.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.DataSetExtensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Contracts.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Debug.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.DiagnosticSource.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.FileVersionInfo.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Process.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.StackTrace.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.TextWriterTraceListener.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Tools.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.TraceSource.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Tracing.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Drawing.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Drawing.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Dynamic.Runtime.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Formats.Asn1.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Formats.Tar.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.Calendars.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.Brotli.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.FileSystem.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.ZipFile.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.AccessControl.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.DriveInfo.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.Watcher.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.IsolatedStorage.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.MemoryMappedFiles.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipelines.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipes.AccessControl.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipes.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.UnmanagedMemoryStream.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Expressions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Parallel.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Queryable.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Memory.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Http.Json.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Http.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.HttpListener.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Mail.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.NameResolution.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.NetworkInformation.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Ping.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Quic.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Requests.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Security.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.ServicePoint.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Sockets.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebClient.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebHeaderCollection.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebProxy.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebSockets.Client.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebSockets.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Numerics.Vectors.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Numerics.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ObjectModel.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.CoreLib.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.DataContractSerialization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Uri.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Xml.Linq.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Xml.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.DispatchProxy.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.ILGeneration.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.Lightweight.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Metadata.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.TypeExtensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.Reader.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.ResourceManager.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.Writer.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.CompilerServices.Unsafe.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.CompilerServices.VisualC.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Handles.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.JavaScript.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.RuntimeInformation.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Intrinsics.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Loader.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Numerics.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Formatters.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Json.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Xml.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.AccessControl.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Claims.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Algorithms.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Cng.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Csp.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Encoding.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.OpenSsl.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.X509Certificates.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Principal.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Principal.Windows.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.SecureString.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ServiceModel.Web.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ServiceProcess.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.CodePages.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encodings.Web.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Json.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.RegularExpressions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Channels.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Overlapped.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Dataflow.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Parallel.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Thread.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.ThreadPool.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Timer.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Transactions.Local.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Transactions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ValueTuple.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Web.HttpUtility.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Web.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Windows.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.Linq.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.ReaderWriter.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.Serialization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XDocument.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XmlDocument.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XmlSerializer.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XPath.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XPath.XDocument.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\WindowsBase.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Mail.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Xml.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Win32.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Numerics.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Sockets.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.ILGeneration.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.CoreLib.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.js.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebClient.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Http.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.Web.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Metadata.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Process.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Core.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipes.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Parallel.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XDocument.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.DataAnnotations.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileSystemGlobbing.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.FileSystem.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.Lightweight.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.ThreadPool.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.DataSetExtensions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.NameResolution.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Formats.Tar.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.TypeExtensions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.RuntimeInformation.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\blazor.webassembly.js.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.UnmanagedMemoryStream.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Security.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Tools.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Ping.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.TraceSource.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Specialized.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Loader.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.Extensions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebHeaderCollection.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ServiceModel.Web.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Requests.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Parallel.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Concurrent.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_CJK.dat.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.native.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.WebAssembly.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.CompilerServices.Unsafe.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.JSInterop.WebAssembly.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.OpenSsl.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Extensions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ValueTuple.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.ReaderWriter.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Tracing.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.ResourceManager.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.TypeConverter.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Data.Common.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Formats.Asn1.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Web.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.EventBasedAsync.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.X509Certificates.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Json.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.Watcher.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XmlSerializer.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XPath.XDocument.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Authorization.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Transactions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipelines.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.VisualBasic.Core.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Drawing.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.FileExtensions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.native.js.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.CodePages.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.AspNetCore.Components.Forms.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.DiagnosticSource.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.CompilerServices.VisualC.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Claims.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Csp.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.StackTrace.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.IsolatedStorage.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.NetworkInformation.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.Serialization.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Configuration.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Timer.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ObjectModel.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\emcc-props.json.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_EFIGS.dat.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.RegularExpressions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\netstandard.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.Writer.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\icudt_no_CJK.dat.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Formatters.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Cng.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Channels.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Json.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Principal.Windows.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Http.Json.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Numerics.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Metadata.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Xml.Linq.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.DriveInfo.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Overlapped.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Emit.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.Extensions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.JSInterop.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Dynamic.Runtime.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Resources.Reader.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Json.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Contracts.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.runtime.js.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.js.map.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ComponentModel.Annotations.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Tasks.Dataflow.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebSockets.Client.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Handles.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Queryable.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.VisualBasic.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.HttpListener.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Principal.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Intrinsics.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Encoding.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encoding.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Serialization.Xml.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileProviders.Physical.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.Linq.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.AppContext.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.CSharp.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Text.Encodings.Web.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.Uri.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Abstractions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.Binder.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.Cryptography.Algorithms.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Logging.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Memory.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebProxy.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.ZipFile.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Buffers.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Logging.Abstractions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.FileProviders.Abstractions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.WebSockets.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Windows.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Configuration.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Compression.Brotli.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.Quic.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Options.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.FileVersionInfo.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.Primitives.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XPath.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Reflection.DispatchProxy.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Private.DataContractSerialization.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Xml.XmlDocument.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.ServiceProcess.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\dotnet.runtime.js.map.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.InteropServices.JavaScript.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Extensions.DependencyInjection.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.Immutable.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.TextWriterTraceListener.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Linq.Expressions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Threading.Thread.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Collections.NonGeneric.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Runtime.Extensions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.AccessControl.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Diagnostics.Debug.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\mscorlib.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\WindowsBase.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.FileSystem.AccessControl.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.Pipes.AccessControl.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Microsoft.Win32.Registry.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Security.SecureString.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Transactions.Local.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.IO.MemoryMappedFiles.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Net.ServicePoint.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Console.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.Extensions.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Web.HttpUtility.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Drawing.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Numerics.Vectors.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\System.Globalization.Calendars.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\blazor.boot.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicApp.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicApp.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicCommon.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicCommon.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Buttons.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Calendars.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Core.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Data.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.DropDowns.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Inputs.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Lists.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Navigations.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Notifications.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Popups.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Schedule.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Spinner.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.SplitButtons.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.ExcelExport.Net.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Licensing.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicCommon.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.DropDowns.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.ExcelExport.Net.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicApp.pdb.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Navigations.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Popups.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicCommon.pdb.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Lists.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Spinner.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Core.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Data.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Licensing.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Calendars.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Buttons.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\blazor.boot.json.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.SplitButtons.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Notifications.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Inputs.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Schedule.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\ShiningCMusicApp.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicApp.staticwebassets.runtime.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicApp.staticwebassets.endpoints.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicApp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicApp.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Authorization.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Components.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Forms.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Web.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Components.WebAssembly.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.AspNetCore.Metadata.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Binder.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Physical.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.JSInterop.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.JSInterop.WebAssembly.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Buttons.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Calendars.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Core.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Data.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.DropDowns.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Inputs.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Lists.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Navigations.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Notifications.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Popups.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Schedule.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Spinner.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.SplitButtons.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.ExcelExport.Net.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Licensing.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Pipelines.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.CSharp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.VisualBasic.Core.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.VisualBasic.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Win32.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Microsoft.Win32.Registry.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.AppContext.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Buffers.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.Concurrent.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.Immutable.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.NonGeneric.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.Specialized.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Collections.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.Annotations.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.DataAnnotations.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.EventBasedAsync.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.TypeConverter.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ComponentModel.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Configuration.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Console.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Core.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Data.Common.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Data.DataSetExtensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Data.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Contracts.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Debug.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.FileVersionInfo.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Process.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.StackTrace.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.TextWriterTraceListener.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Tools.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.TraceSource.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Diagnostics.Tracing.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Drawing.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Drawing.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Dynamic.Runtime.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Formats.Asn1.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Formats.Tar.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Globalization.Calendars.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Globalization.Extensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Globalization.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Compression.Brotli.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Compression.FileSystem.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Compression.ZipFile.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Compression.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.AccessControl.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.DriveInfo.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.Watcher.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.FileSystem.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.IsolatedStorage.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.MemoryMappedFiles.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Pipes.AccessControl.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.Pipes.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.UnmanagedMemoryStream.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.IO.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Linq.Expressions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Linq.Parallel.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Linq.Queryable.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Linq.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Memory.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Http.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Http.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.HttpListener.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Mail.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.NameResolution.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.NetworkInformation.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Ping.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Quic.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Requests.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Security.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.ServicePoint.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.Sockets.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebHeaderCollection.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebProxy.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebSockets.Client.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.WebSockets.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Net.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Numerics.Vectors.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Numerics.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ObjectModel.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.DataContractSerialization.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.Uri.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.Xml.Linq.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.Xml.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.DispatchProxy.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Emit.ILGeneration.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Emit.Lightweight.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Emit.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Extensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Metadata.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.TypeExtensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Reflection.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Resources.Reader.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Resources.ResourceManager.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Resources.Writer.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.CompilerServices.Unsafe.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.CompilerServices.VisualC.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Extensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Handles.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.InteropServices.JavaScript.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.InteropServices.RuntimeInformation.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.InteropServices.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Intrinsics.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Loader.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Numerics.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.Formatters.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.Xml.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.Serialization.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Runtime.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.AccessControl.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Claims.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Algorithms.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Cng.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Csp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Encoding.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.OpenSsl.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.Primitives.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.X509Certificates.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Cryptography.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Principal.Windows.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.Principal.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.SecureString.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Security.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ServiceModel.Web.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ServiceProcess.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Encoding.CodePages.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Encoding.Extensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Encoding.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Encodings.Web.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Text.RegularExpressions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Channels.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Overlapped.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Tasks.Dataflow.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Tasks.Extensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Tasks.Parallel.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Tasks.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Thread.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.ThreadPool.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.Timer.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Threading.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Transactions.Local.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Transactions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.ValueTuple.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Web.HttpUtility.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Web.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Windows.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.Linq.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.ReaderWriter.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.Serialization.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XDocument.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XPath.XDocument.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XPath.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XmlDocument.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.XmlSerializer.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Xml.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\WindowsBase.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\mscorlib.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\netstandard.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\System.Private.CoreLib.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.js
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.js.map
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.native.js
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.native.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.runtime.js
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\dotnet.runtime.js.map
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\icudt_CJK.dat
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\icudt_EFIGS.dat
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\icudt_no_CJK.dat
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\emcc-props.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicCommon.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\ShiningCMusicCommon.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.csproj.AssemblyReference.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.GeneratedMSBuildEditorConfig.editorconfig
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.AssemblyInfoInputs.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.AssemblyInfo.cs
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.csproj.CoreCompileInputs.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.MvcApplicationPartsAssemblyInfo.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Authorization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Components.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Components.Forms.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Components.Web.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Components.WebAssembly.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.AspNetCore.Metadata.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.Abstractions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.Binder.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.FileExtensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Configuration.Json.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.DependencyInjection.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.DependencyInjection.Abstractions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.FileProviders.Abstractions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.FileProviders.Physical.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.FileSystemGlobbing.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Logging.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Logging.Abstractions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Options.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Extensions.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.JSInterop.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.JSInterop.WebAssembly.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Buttons.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Calendars.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Core.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Data.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.DropDowns.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Inputs.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Lists.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Navigations.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Notifications.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Popups.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Schedule.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Spinner.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.SplitButtons.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.ExcelExport.Net.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Licensing.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Pipelines.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.CSharp.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.VisualBasic.Core.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.VisualBasic.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Win32.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Microsoft.Win32.Registry.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.AppContext.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Buffers.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.Concurrent.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.Immutable.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.NonGeneric.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.Specialized.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Collections.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.Annotations.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.DataAnnotations.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.EventBasedAsync.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.TypeConverter.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ComponentModel.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Configuration.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Console.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Core.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Data.Common.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Data.DataSetExtensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Data.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Contracts.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Debug.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.DiagnosticSource.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.FileVersionInfo.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Process.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.StackTrace.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.TextWriterTraceListener.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Tools.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.TraceSource.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Diagnostics.Tracing.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Drawing.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Drawing.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Dynamic.Runtime.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Formats.Asn1.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Formats.Tar.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Globalization.Calendars.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Globalization.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Globalization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Compression.Brotli.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Compression.FileSystem.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Compression.ZipFile.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Compression.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.AccessControl.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.DriveInfo.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.Watcher.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.FileSystem.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.IsolatedStorage.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.MemoryMappedFiles.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Pipes.AccessControl.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.Pipes.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.UnmanagedMemoryStream.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.IO.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Linq.Expressions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Linq.Parallel.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Linq.Queryable.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Linq.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Memory.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Http.Json.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Http.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.HttpListener.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Mail.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.NameResolution.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.NetworkInformation.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Ping.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Quic.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Requests.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Security.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.ServicePoint.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.Sockets.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebClient.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebHeaderCollection.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebProxy.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebSockets.Client.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.WebSockets.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Net.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Numerics.Vectors.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Numerics.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ObjectModel.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.DataContractSerialization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.Uri.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.Xml.Linq.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.Xml.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.DispatchProxy.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Emit.ILGeneration.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Emit.Lightweight.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Emit.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Metadata.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.TypeExtensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Reflection.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Resources.Reader.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Resources.ResourceManager.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Resources.Writer.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.CompilerServices.Unsafe.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.CompilerServices.VisualC.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Handles.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.InteropServices.JavaScript.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.InteropServices.RuntimeInformation.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.InteropServices.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Intrinsics.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Loader.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Numerics.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.Formatters.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.Json.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.Xml.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.Serialization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Runtime.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.AccessControl.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Claims.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Algorithms.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Cng.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Csp.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Encoding.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.OpenSsl.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.Primitives.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.X509Certificates.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Cryptography.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Principal.Windows.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.Principal.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.SecureString.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Security.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ServiceModel.Web.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ServiceProcess.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Encoding.CodePages.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Encoding.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Encoding.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Encodings.Web.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.Json.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Text.RegularExpressions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Channels.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Overlapped.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Tasks.Dataflow.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Tasks.Extensions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Tasks.Parallel.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Tasks.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Thread.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.ThreadPool.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.Timer.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Threading.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Transactions.Local.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Transactions.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.ValueTuple.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Web.HttpUtility.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Web.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Windows.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.Linq.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.ReaderWriter.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.Serialization.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XDocument.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XPath.XDocument.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XPath.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XmlDocument.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.XmlSerializer.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Xml.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\WindowsBase.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\mscorlib.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\netstandard.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\System.Private.CoreLib.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\ShiningCMusicCommon.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\ShiningCMusicApp.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\scopedcss\bundle\ShiningCMusicApp.styles.css
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\blazor.build.boot-extension.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\blazor.boot.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\5j8dxmne5b-mv535bwyet.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\dy5lkhjkn1-xtjqgewnsy.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f17o09ymz1-vbl3iftxmx.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f813afr1vj-i47vxqdqw2.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1gdl188rtq-y9j53ldofb.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\99xx5yiczq-3uudqrjyld.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1jvnjphhuc-txus4zzmh1.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\sd4w29iblj-4njtqvtvgx.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qmrh3cyln1-8kr5d0tjmo.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qq3w8mqh2l-0r3amze666.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f0p7mhideg-en8mb8dgz5.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\luw69m5zpj-yy6f57640l.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\vhswmu8kpf-xqsu2wsvba.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\fg6rcqyzob-kgyjb8k43h.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rzc0mkqxf8-1c7ksbormu.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pyud5iqoj0-rpvltkbyzt.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3wl2w0d9cv-i464dwxnbb.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\r2q3mj3u9h-xlpspxuy08.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rvv7resapu-tz325eqvv5.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\sx4xzqaqm5-jt8xzja2dj.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ta7m7j2gei-lsakbjp1fg.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ldqgtsc41p-ae1qwufxjk.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ah5uj9fkaq-tr42ods1qv.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rrp3ygrbh4-t3di59eis6.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pxxrbnr171-76z3t3ul0w.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jydetqvzvc-tb4icbsua9.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lcyu9ghdos-ldhtchhorc.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3bohcw7c8u-3b3recd4c3.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xnicbo8o6w-9027wsoep3.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\h6h4qv19aa-vei9li4mx3.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cp5mb4volr-5v0k8dfq0z.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\wgslxn3apm-yarh891x5f.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\dwdcdv3wg8-7ksm1zkb1a.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xsb0ndz1hr-7uae7ijupc.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jkmpkjk4xk-pcebwu17mj.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\w25zlm6clu-4z51p4oqs7.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\98ec87va6q-g0hxkhx4x3.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\md53uouqm4-4usqb2x1su.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\e97ynr2icq-iudrcw56e1.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\90hp2p71hu-tjcz0u77k5.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\iolod2it8d-tptq2av103.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\j19hqecu3k-lfu7j35m59.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\i4ozfjtw4i-oyz0vx2fzb.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.build.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.development.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningC.B4CB0432.Up2Date
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\refint\ShiningCMusicApp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ShiningCMusicApp.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\ref\ShiningCMusicApp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Themes.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\wwwroot\_framework\Syncfusion.Blazor.Themes.wasm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\bin\Debug\net8.0\Syncfusion.Blazor.Themes.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\rpswa.dswa.cache.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\webcil\Syncfusion.Blazor.Themes.wasm
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\rjimswa.dswa.cache.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\zojyde6k3s-11z9idvov7.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\12gy8c80sd-4rejzitfsn.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\mrmnme9fy7-3gypxjnyzv.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f2nmvc6iyo-vpgbraawsm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\92k36dsjrc-61e7sdubtr.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\mgqvc88zci-ywccyuk0ea.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\v9ucdunf1s-nmlfvbueei.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pzxdtik6zo-1hj4jqau9j.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\egcrwen6o7-rr2wy4asdd.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ojmli06k3u-uosabjs4t4.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0khw4724ee-af1hnqw24k.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\yi1kr6g9b3-n74qy90ozc.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pzjo06rbey-r3dkwup91o.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rmczs60qrj-j17trnwz0f.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\8pinc6rjf7-lkbadpelqi.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\vzisnnpkmx-4aax14grby.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xde021tovc-f2o09bw51d.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7a5kyzpvkv-exlzuq35jp.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jimaxq9mi6-y7qnt2sca0.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\n257kqytbc-v5hyanf0mc.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3paocsqo5e-2a2uywtzte.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cap10iz261-hsnsliye9b.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\9ssd939fn0-m498x7yd6j.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\byt3nyxy2q-69bwpm0gd5.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\c39t6asywb-yq2dti153n.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\hiyuk85oo1-pteo5cxcfm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z9316qi1ji-zsobxitq9m.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1umd06yrfb-ayoozo91sk.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\bz3avbczq2-ptdxkmw326.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\4wpiy2o4d3-ezdljfc37x.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\9wtv5r574i-s4jbqeso3o.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\m3qtwn3ns2-rl77dmc4g8.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xnalvtn1mb-d6r1qmhtiw.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\fnpgxujk9y-imdaogz3ij.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\sxppidzjgj-gqyf43a3pe.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1lzqee7hvd-2qdjhg82pw.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\gdmgran5y7-lx3knuy0pm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\wdlm54q4fw-p6vx4lif6u.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\67lx4f78cj-4s4qj16jn0.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\77au2vjfld-5howj2x4lt.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\binqqlz1sy-jg8c5ekqx6.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\eh357harcp-tvnls6hcxf.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z9yxv7vuua-2ad51ju9aa.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\leomrziixf-n0jfd8l0iz.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\c4ro1hju42-l2i13om05z.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\57agg9smmy-k6z943nrpy.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\zr3rxoo55n-g2zbr73a2g.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z98cfng326-m90ww2zviv.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\giup3hxlyd-8bmqvi5to8.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\smvg5f5rpt-xpehz1u5xg.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3zji9tv399-ofyky8esh0.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rgl88w1it9-kqgep265ab.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lvyrlugiaz-whf02me0s4.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\y3zikztta2-1trkjj9toj.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\l7ml98ds2n-pak789pfhc.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\26zimhthez-tx889edwyf.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cumen4e1px-2y53qsfelr.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ohnw08v0s7-5i7u2kz8gq.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\gmtjrsjpjr-bg69h2q1tx.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\yqou8ak97v-s21p6d0e1y.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\y9x9709oxs-6jayxq6dso.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\2yj9ptca6s-78mrke9rwp.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\5oprngc3na-rqikjp4z06.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\9rp1nsr78a-lyr9te5dpd.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\wqbo3t8krt-v19ocl650f.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\32t05zddb7-7jakql04zz.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\o4a5y23fdq-yskp2l2j28.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\6a5ovfauzx-dr9ustd9mn.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\raqtb0g8tk-r5wuytek4x.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\k90ia90a0l-r4fmndj4lr.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\1cs6asnscm-dfc7iaw959.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\om386z7hvl-ypu8t1a3th.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\01kj3kzu05-avt2ugss8m.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\4ziudi5er1-kqt05iowt5.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\gqrdkmv4gr-ac0n5txzyd.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\778te4j2e9-rrwevpsa36.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\axunml5apk-9jxobawljc.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\swgmwtsne4-nn6t1rxfu5.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\8bprzflbij-psjawytmz8.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\5peanjzhpf-49lyg32can.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z537psxkb5-rtqo41ax01.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0prwepgyfd-300sh8z8ui.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\12jxpcjsau-o9b03xt26m.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\876btrwotz-i1l90qe767.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\rb0r3wv541-ckahycs9oy.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\n9ufvg6zor-12eg0vhcpm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\s007umfqyr-tl58hhwwm9.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\momr4swqrq-s42jx0duup.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\zlfrj5nqwi-a1ph0cw4zn.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0lktitq3ad-s0hlxxu97u.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\hmt6yadul1-cfb8wdf8bw.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\tqgnr7mkgy-pf4dzgp6et.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qm6lsqstgu-3mrenqpwlz.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\kt41y9oyfq-nudbvebtzm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\07zc013blp-tbmcprtln3.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\tesx50ibi3-824m7y8iv9.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0t5acjtnsc-5e6t7jey7n.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\4b9lgapm59-zyak9ezx99.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lawfelcw3p-vqk9iwwdi8.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lc1a89fsfa-7whwneqdab.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ksef9slfq8-1fm33xfb4x.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\dlqy23dccu-kcvkdr4alb.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\58erqdvtin-gpga8vsymd.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\odya9nxts3-abr508h4gv.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lhpdkrosjy-fjhf6hjcqm.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\bqol8tplix-1zqopzubo2.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\iwjvmfxqu3-nilb9xe1yl.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\9m1cnur73y-xpg3jz0dn2.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\fw6jev8eke-83ia4yffoo.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xklfolbr8t-nbdqcvny6q.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\nmba0r7o2l-apxeirum8l.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\vb7m02hyiv-bhp1yeu8fb.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\5ctps1xp05-mvr25z4kt0.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\royp2e729g-qk5bzw2lz9.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pexc7mp10a-luafopex6b.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7gj2dpgwqs-j22agkcn9j.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\kbgplu5rwn-2k5z9g2rmq.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\j28a3ppivi-7e6tdrrvk0.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jyffsad4v5-ophjv8dnro.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7xl7opbj6m-f6qrsh5ggz.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pv91aidw81-768xxo33e0.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\nm3k1vr2b0-nolure4ku9.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\mv2ne30sfa-l12i2hq2kq.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xm0a60puc7-93x5jg3mkp.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\fyv9wlngmq-2cfqx8u8uj.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qrxu2zwgr5-wyelmb42ye.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ja52lhp99e-dst12xa6e5.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\g8d1ll62re-6ei8na5yhg.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\plvxa4s6ga-3eb8c3pfwe.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\arw8hu9x14-171oaphs01.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\il592650ow-1hrv129abp.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ced8wcd8cu-atf14r4yhw.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\eh7hhep0d3-ac8qf4w6lx.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jyu1zp9qgf-nqh8s5tb94.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\p1o86wnkwn-61u88luwrz.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ylntj2h544-kkdt47cvjv.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xgis4ptx7x-dhjbngzvrh.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\8801tn65l9-9c7ym8z6z0.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\v8iorw1ao4-74uvd51634.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\f6wx6gd1gt-su7r23l034.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7x27t74nhm-1yccpcq7id.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ppfdnigb7q-55ue31v3pl.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\qdvfj0qa9d-akbtg6mu9j.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cn4p7z7pyl-c5gx1pj8rz.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ipm780nllg-l1kk2a4a0t.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\jef7gsrqq7-1qhdretbuh.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\laazswwohk-609xepspin.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\n4x5zi1hy5-emm1p2vibe.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\awerrgejpl-68n5xxnez7.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\8nxszxol5d-zchzosoc8t.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ezx0izcezf-mzcae7sc1w.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\x959zs3735-av82mpp65m.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\4pab78fge1-03z9epkgeu.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\hlv81p94us-tcb84raxfr.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\lx4q04c6vo-j0x2amkbr8.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ym2sc23egi-3mr0k4ql8l.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\e0p52hsb7u-tebc67toix.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\axs689n286-mylfx8o0as.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\z9l7ueuc70-wj73nmpklo.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\cajl2o1e7z-fxengc3wyj.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\sadg8p8gec-nphd9f66p0.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\pzgeww6nnf-8cl47m4jkh.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\bdjfymnqrw-3yhl940msb.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\gwugds5sw2-75yss9zhoy.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\3kjfjnpc9x-i6esjxgs2i.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\ddncr1chdl-5xzb5f7wvr.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\tb1rghhwn6-uk4ktf4545.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\uh9y7ps724-ngnkh62a7t.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\dc9ab4adpy-tyrk4ywyu5.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7qt3udra8g-ekfbtxon3o.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\7ycekvj0yv-4isf5pcuol.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xw3xqrs1gn-u7plvjpova.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xv769p21qu-epnagkv91l.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\iqwuxofz82-u6wau0ktb7.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0tq3ubgpl3-710onjtcga.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\scopedcss\Layout\NavMenu.razor.rz.scp.css
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\scopedcss\projectbundle\ShiningCMusicApp.bundle.scp.css
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\scopedcss\Layout\MainLayout.razor.rz.scp.css
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\0qonx4ygs9-262l5lvhqa.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\esi1du385w-da2bufkpgn.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\xvf58668z8-tj6jbuwo08.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\bqkz6kxeo8-sax0uiljhd.gz
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\obj\Debug\net8.0\compressed\tga4xb2zqw-1o0v3a4mvv.gz
