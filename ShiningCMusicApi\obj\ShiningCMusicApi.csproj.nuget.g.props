﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages;C:\Program Files\dotnet\sdk\NuGetFallbackFolder</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
    <SourceRoot Include="C:\Program Files\dotnet\sdk\NuGetFallbackFolder\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.themes\29.2.11\buildTransitive\Syncfusion.Blazor.Themes.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.themes\29.2.11\buildTransitive\Syncfusion.Blazor.Themes.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.core\29.2.11\buildTransitive\Syncfusion.Blazor.Core.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.core\29.2.11\buildTransitive\Syncfusion.Blazor.Core.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.spinner\29.2.11\buildTransitive\Syncfusion.Blazor.Spinner.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.spinner\29.2.11\buildTransitive\Syncfusion.Blazor.Spinner.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.buttons\29.2.11\buildTransitive\Syncfusion.Blazor.Buttons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.buttons\29.2.11\buildTransitive\Syncfusion.Blazor.Buttons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.popups\29.2.11\buildTransitive\Syncfusion.Blazor.Popups.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.popups\29.2.11\buildTransitive\Syncfusion.Blazor.Popups.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\29.2.11\buildTransitive\Syncfusion.Blazor.SplitButtons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\29.2.11\buildTransitive\Syncfusion.Blazor.SplitButtons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.notifications\29.2.11\buildTransitive\Syncfusion.Blazor.Notifications.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.notifications\29.2.11\buildTransitive\Syncfusion.Blazor.Notifications.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.data\29.2.11\buildTransitive\Syncfusion.Blazor.Data.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.data\29.2.11\buildTransitive\Syncfusion.Blazor.Data.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.lists\29.2.11\buildTransitive\Syncfusion.Blazor.Lists.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.lists\29.2.11\buildTransitive\Syncfusion.Blazor.Lists.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.inputs\29.2.11\buildTransitive\Syncfusion.Blazor.Inputs.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.inputs\29.2.11\buildTransitive\Syncfusion.Blazor.Inputs.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.dropdowns\29.2.11\buildTransitive\Syncfusion.Blazor.DropDowns.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.dropdowns\29.2.11\buildTransitive\Syncfusion.Blazor.DropDowns.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.navigations\29.2.11\buildTransitive\Syncfusion.Blazor.Navigations.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.navigations\29.2.11\buildTransitive\Syncfusion.Blazor.Navigations.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.calendars\29.2.11\buildTransitive\Syncfusion.Blazor.Calendars.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.calendars\29.2.11\buildTransitive\Syncfusion.Blazor.Calendars.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.schedule\29.2.11\buildTransitive\Syncfusion.Blazor.Schedule.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.schedule\29.2.11\buildTransitive\Syncfusion.Blazor.Schedule.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.apidescription.server\6.0.5\build\Microsoft.Extensions.ApiDescription.Server.props" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.apidescription.server\6.0.5\build\Microsoft.Extensions.ApiDescription.Server.props')" />
    <Import Project="$(NuGetPackageRoot)swashbuckle.aspnetcore\6.6.2\build\Swashbuckle.AspNetCore.props" Condition="Exists('$(NuGetPackageRoot)swashbuckle.aspnetcore\6.6.2\build\Swashbuckle.AspNetCore.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.15\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.15\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_Extensions_ApiDescription_Server Condition=" '$(PkgMicrosoft_Extensions_ApiDescription_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.extensions.apidescription.server\6.0.5</PkgMicrosoft_Extensions_ApiDescription_Server>
    <PkgMicrosoft_AspNetCore_Components_WebAssembly_Server Condition=" '$(PkgMicrosoft_AspNetCore_Components_WebAssembly_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.webassembly.server\8.0.15</PkgMicrosoft_AspNetCore_Components_WebAssembly_Server>
  </PropertyGroup>
</Project>