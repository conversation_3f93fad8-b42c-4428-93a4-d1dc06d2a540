(()=>{"use strict";var e,t,n;!function(e){const t=[],n="__jsObjectId",r="__dotNetObject",o="__byte[]",i="__dotNetStream",s="__jsStreamReferenceLength";let a,c;class l{constructor(e){this._jsObject=e,this._cachedFunctions=new Map}findFunction(e){const t=this._cachedFunctions.get(e);if(t)return t;let n,r=this._jsObject;if(e.split(".").forEach((t=>{if(!(t in r))throw new Error(`Could not find '${e}' ('${t}' was undefined).`);n=r,r=r[t]})),r instanceof Function)return r=r.bind(n),this._cachedFunctions.set(e,r),r;throw new Error(`The value '${e}' is not a function.`)}getWrappedObject(){return this._jsObject}}const u={0:new l(window)};u[0]._cachedFunctions.set("import",(e=>("string"==typeof e&&e.startsWith("./")&&(e=new URL(e.substr(2),document.baseURI).toString()),import(e))));let d,f=1;function m(e){t.push(e)}function h(e){if(e&&"object"==typeof e){u[f]=new l(e);const t={[n]:f};return f++,t}throw new Error(`Cannot create a JSObjectReference from the value '${e}'.`)}function p(e){let t=-1;if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Blob)t=e.size;else{if(!(e.buffer instanceof ArrayBuffer))throw new Error("Supplied value is not a typed array or blob.");if(void 0===e.byteLength)throw new Error(`Cannot create a JSStreamReference from the value '${e}' as it doesn't have a byteLength.`);t=e.byteLength}const r={[s]:t};try{const t=h(e);r[n]=t[n]}catch(t){throw new Error(`Cannot create a JSStreamReference from the value '${e}'.`)}return r}function v(e,n){c=e;const r=n?JSON.parse(n,((e,n)=>t.reduce(((t,n)=>n(e,t)),n))):null;return c=void 0,r}function g(){if(void 0===a)throw new Error("No call dispatcher has been set.");if(null===a)throw new Error("There are multiple .NET runtimes present, so a default dispatcher could not be resolved. Use DotNetObject to invoke .NET instance methods.");return a}e.attachDispatcher=function(e){const t=new b(e);return void 0===a?a=t:a&&(a=null),t},e.attachReviver=m,e.invokeMethod=function(e,t,...n){return g().invokeDotNetStaticMethod(e,t,...n)},e.invokeMethodAsync=function(e,t,...n){return g().invokeDotNetStaticMethodAsync(e,t,...n)},e.createJSObjectReference=h,e.createJSStreamReference=p,e.disposeJSObjectReference=function(e){const t=e&&e[n];"number"==typeof t&&E(t)},function(e){e[e.Default=0]="Default",e[e.JSObjectReference=1]="JSObjectReference",e[e.JSStreamReference=2]="JSStreamReference",e[e.JSVoidResult=3]="JSVoidResult"}(d=e.JSCallResultType||(e.JSCallResultType={}));class b{constructor(e){this._dotNetCallDispatcher=e,this._byteArraysToBeRevived=new Map,this._pendingDotNetToJSStreams=new Map,this._pendingAsyncCalls={},this._nextAsyncCallId=1}getDotNetCallDispatcher(){return this._dotNetCallDispatcher}invokeJSFromDotNet(e,t,n,r){const o=v(this,t),i=I(w(e,r)(...o||[]),n);return null==i?null:_(this,i)}beginInvokeJSFromDotNet(e,t,n,r,o){const i=new Promise((e=>{const r=v(this,n);e(w(t,o)(...r||[]))}));e&&i.then((t=>_(this,[e,!0,I(t,r)]))).then((t=>this._dotNetCallDispatcher.endInvokeJSFromDotNet(e,!0,t)),(t=>this._dotNetCallDispatcher.endInvokeJSFromDotNet(e,!1,JSON.stringify([e,!1,y(t)]))))}endInvokeDotNetFromJS(e,t,n){const r=t?v(this,n):new Error(n);this.completePendingCall(parseInt(e,10),t,r)}invokeDotNetStaticMethod(e,t,...n){return this.invokeDotNetMethod(e,t,null,n)}invokeDotNetStaticMethodAsync(e,t,...n){return this.invokeDotNetMethodAsync(e,t,null,n)}invokeDotNetMethod(e,t,n,r){if(this._dotNetCallDispatcher.invokeDotNetFromJS){const o=_(this,r),i=this._dotNetCallDispatcher.invokeDotNetFromJS(e,t,n,o);return i?v(this,i):null}throw new Error("The current dispatcher does not support synchronous calls from JS to .NET. Use invokeDotNetMethodAsync instead.")}invokeDotNetMethodAsync(e,t,n,r){if(e&&n)throw new Error(`For instance method calls, assemblyName should be null. Received '${e}'.`);const o=this._nextAsyncCallId++,i=new Promise(((e,t)=>{this._pendingAsyncCalls[o]={resolve:e,reject:t}}));try{const i=_(this,r);this._dotNetCallDispatcher.beginInvokeDotNetFromJS(o,e,t,n,i)}catch(e){this.completePendingCall(o,!1,e)}return i}receiveByteArray(e,t){this._byteArraysToBeRevived.set(e,t)}processByteArray(e){const t=this._byteArraysToBeRevived.get(e);return t?(this._byteArraysToBeRevived.delete(e),t):null}supplyDotNetStream(e,t){if(this._pendingDotNetToJSStreams.has(e)){const n=this._pendingDotNetToJSStreams.get(e);this._pendingDotNetToJSStreams.delete(e),n.resolve(t)}else{const n=new C;n.resolve(t),this._pendingDotNetToJSStreams.set(e,n)}}getDotNetStreamPromise(e){let t;if(this._pendingDotNetToJSStreams.has(e))t=this._pendingDotNetToJSStreams.get(e).streamPromise,this._pendingDotNetToJSStreams.delete(e);else{const n=new C;this._pendingDotNetToJSStreams.set(e,n),t=n.streamPromise}return t}completePendingCall(e,t,n){if(!this._pendingAsyncCalls.hasOwnProperty(e))throw new Error(`There is no pending async call with ID ${e}.`);const r=this._pendingAsyncCalls[e];delete this._pendingAsyncCalls[e],t?r.resolve(n):r.reject(n)}}function y(e){return e instanceof Error?`${e.message}\n${e.stack}`:e?e.toString():"null"}function w(e,t){const n=u[t];if(n)return n.findFunction(e);throw new Error(`JS object instance with ID ${t} does not exist (has it been disposed?).`)}function E(e){delete u[e]}e.findJSFunction=w,e.disposeJSObjectReferenceById=E;class S{constructor(e,t){this._id=e,this._callDispatcher=t}invokeMethod(e,...t){return this._callDispatcher.invokeDotNetMethod(null,e,this._id,t)}invokeMethodAsync(e,...t){return this._callDispatcher.invokeDotNetMethodAsync(null,e,this._id,t)}dispose(){this._callDispatcher.invokeDotNetMethodAsync(null,"__Dispose",this._id,null).catch((e=>console.error(e)))}serializeAsArg(){return{[r]:this._id}}}e.DotNetObject=S,m((function(e,t){if(t&&"object"==typeof t){if(t.hasOwnProperty(r))return new S(t[r],c);if(t.hasOwnProperty(n)){const e=t[n],r=u[e];if(r)return r.getWrappedObject();throw new Error(`JS object instance with Id '${e}' does not exist. It may have been disposed.`)}if(t.hasOwnProperty(o)){const e=t[o],n=c.processByteArray(e);if(void 0===n)throw new Error(`Byte array index '${e}' does not exist.`);return n}if(t.hasOwnProperty(i)){const e=t[i],n=c.getDotNetStreamPromise(e);return new A(n)}}return t}));class A{constructor(e){this._streamPromise=e}stream(){return this._streamPromise}async arrayBuffer(){return new Response(await this.stream()).arrayBuffer()}}class C{constructor(){this.streamPromise=new Promise(((e,t)=>{this.resolve=e,this.reject=t}))}}function I(e,t){switch(t){case d.Default:return e;case d.JSObjectReference:return h(e);case d.JSStreamReference:return p(e);case d.JSVoidResult:return null;default:throw new Error(`Invalid JS call result type '${t}'.`)}}let N=0;function _(e,t){N=0,c=e;const n=JSON.stringify(t,R);return c=void 0,n}function R(e,t){if(t instanceof S)return t.serializeAsArg();if(t instanceof Uint8Array){c.getDotNetCallDispatcher().sendByteArray(N,t);const e={[o]:N};return N++,e}return t}}(e||(e={})),function(e){e[e.prependFrame=1]="prependFrame",e[e.removeFrame=2]="removeFrame",e[e.setAttribute=3]="setAttribute",e[e.removeAttribute=4]="removeAttribute",e[e.updateText=5]="updateText",e[e.stepIn=6]="stepIn",e[e.stepOut=7]="stepOut",e[e.updateMarkup=8]="updateMarkup",e[e.permutationListEntry=9]="permutationListEntry",e[e.permutationListEnd=10]="permutationListEnd"}(t||(t={})),function(e){e[e.element=1]="element",e[e.text=2]="text",e[e.attribute=3]="attribute",e[e.component=4]="component",e[e.region=5]="region",e[e.elementReferenceCapture=6]="elementReferenceCapture",e[e.markup=8]="markup",e[e.namedEvent=10]="namedEvent"}(n||(n={}));class r{constructor(e,t){this.componentId=e,this.fieldValue=t}static fromEvent(e,t){const n=t.target;if(n instanceof Element){const t=function(e){return e instanceof HTMLInputElement?e.type&&"checkbox"===e.type.toLowerCase()?{value:e.checked}:{value:e.value}:e instanceof HTMLSelectElement||e instanceof HTMLTextAreaElement?{value:e.value}:null}(n);if(t)return new r(e,t.value)}return null}}const o=new Map,i=new Map,s=[];function a(e){return o.get(e)}function c(e){const t=o.get(e);return(null==t?void 0:t.browserEventName)||e}function l(e,t){e.forEach((e=>o.set(e,t)))}function u(e){const t=[];for(let n=0;n<e.length;n++){const r=e[n];t.push({identifier:r.identifier,clientX:r.clientX,clientY:r.clientY,screenX:r.screenX,screenY:r.screenY,pageX:r.pageX,pageY:r.pageY})}return t}function d(e){return{detail:e.detail,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY,offsetX:e.offsetX,offsetY:e.offsetY,pageX:e.pageX,pageY:e.pageY,movementX:e.movementX,movementY:e.movementY,button:e.button,buttons:e.buttons,ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,altKey:e.altKey,metaKey:e.metaKey,type:e.type}}l(["input","change"],{createEventArgs:function(e){const t=e.target;if(function(e){return-1!==f.indexOf(e.getAttribute("type"))}(t)){const e=function(e){const t=e.value,n=e.type;switch(n){case"date":case"month":case"week":return t;case"datetime-local":return 16===t.length?t+":00":t;case"time":return 5===t.length?t+":00":t}throw new Error(`Invalid element type '${n}'.`)}(t);return{value:e}}if(function(e){return e instanceof HTMLSelectElement&&"select-multiple"===e.type}(t)){const e=t;return{value:Array.from(e.options).filter((e=>e.selected)).map((e=>e.value))}}{const e=function(e){return!!e&&"INPUT"===e.tagName&&"checkbox"===e.getAttribute("type")}(t);return{value:e?!!t.checked:t.value}}}}),l(["copy","cut","paste"],{createEventArgs:e=>({type:e.type})}),l(["drag","dragend","dragenter","dragleave","dragover","dragstart","drop"],{createEventArgs:e=>{return{...d(t=e),dataTransfer:t.dataTransfer?{dropEffect:t.dataTransfer.dropEffect,effectAllowed:t.dataTransfer.effectAllowed,files:Array.from(t.dataTransfer.files).map((e=>e.name)),items:Array.from(t.dataTransfer.items).map((e=>({kind:e.kind,type:e.type}))),types:t.dataTransfer.types}:null};var t}}),l(["focus","blur","focusin","focusout"],{createEventArgs:e=>({type:e.type})}),l(["keydown","keyup","keypress"],{createEventArgs:e=>{return{key:(t=e).key,code:t.code,location:t.location,repeat:t.repeat,ctrlKey:t.ctrlKey,shiftKey:t.shiftKey,altKey:t.altKey,metaKey:t.metaKey,type:t.type};var t}}),l(["contextmenu","click","mouseover","mouseout","mousemove","mousedown","mouseup","mouseleave","mouseenter","dblclick"],{createEventArgs:e=>d(e)}),l(["error"],{createEventArgs:e=>{return{message:(t=e).message,filename:t.filename,lineno:t.lineno,colno:t.colno,type:t.type};var t}}),l(["loadstart","timeout","abort","load","loadend","progress"],{createEventArgs:e=>{return{lengthComputable:(t=e).lengthComputable,loaded:t.loaded,total:t.total,type:t.type};var t}}),l(["touchcancel","touchend","touchmove","touchenter","touchleave","touchstart"],{createEventArgs:e=>{return{detail:(t=e).detail,touches:u(t.touches),targetTouches:u(t.targetTouches),changedTouches:u(t.changedTouches),ctrlKey:t.ctrlKey,shiftKey:t.shiftKey,altKey:t.altKey,metaKey:t.metaKey,type:t.type};var t}}),l(["gotpointercapture","lostpointercapture","pointercancel","pointerdown","pointerenter","pointerleave","pointermove","pointerout","pointerover","pointerup"],{createEventArgs:e=>{return{...d(t=e),pointerId:t.pointerId,width:t.width,height:t.height,pressure:t.pressure,tiltX:t.tiltX,tiltY:t.tiltY,pointerType:t.pointerType,isPrimary:t.isPrimary};var t}}),l(["wheel","mousewheel"],{createEventArgs:e=>{return{...d(t=e),deltaX:t.deltaX,deltaY:t.deltaY,deltaZ:t.deltaZ,deltaMode:t.deltaMode};var t}}),l(["cancel","close","toggle"],{createEventArgs:()=>({})});const f=["date","datetime-local","month","time","week"],m=new Map;let h,p,v=0;const g={async add(e,t,n){if(!n)throw new Error("initialParameters must be an object, even if empty.");const r="__bl-dynamic-root:"+(++v).toString();m.set(r,e);const o=await w().invokeMethodAsync("AddRootComponent",t,r),i=new y(o,p[t]);return await i.setParameters(n),i}};class b{invoke(e){return this._callback(e)}setCallback(t){this._selfJSObjectReference||(this._selfJSObjectReference=e.createJSObjectReference(this)),this._callback=t}getJSObjectReference(){return this._selfJSObjectReference}dispose(){this._selfJSObjectReference&&e.disposeJSObjectReference(this._selfJSObjectReference)}}class y{constructor(e,t){this._jsEventCallbackWrappers=new Map,this._componentId=e;for(const e of t)"eventcallback"===e.type&&this._jsEventCallbackWrappers.set(e.name.toLowerCase(),new b)}setParameters(e){const t={},n=Object.entries(e||{}),r=n.length;for(const[e,r]of n){const n=this._jsEventCallbackWrappers.get(e.toLowerCase());n&&r?(n.setCallback(r),t[e]=n.getJSObjectReference()):t[e]=r}return w().invokeMethodAsync("SetRootComponentParameters",this._componentId,r,t)}async dispose(){if(null!==this._componentId){await w().invokeMethodAsync("RemoveRootComponent",this._componentId),this._componentId=null;for(const e of this._jsEventCallbackWrappers.values())e.dispose()}}}function w(){if(!h)throw new Error("Dynamic root components have not been enabled in this application.");return h}const E=new Map,S=[],A=new Map;function C(e,t,n){return N(e,t.eventHandlerId,(()=>I(e).invokeMethodAsync("DispatchEventAsync",t,n)))}function I(e){const t=E.get(e);if(!t)throw new Error(`No interop methods are registered for renderer ${e}`);return t}let N=(e,t,n)=>n();const _=O(["abort","blur","cancel","canplay","canplaythrough","change","close","cuechange","durationchange","emptied","ended","error","focus","load","loadeddata","loadedmetadata","loadend","loadstart","mouseenter","mouseleave","pointerenter","pointerleave","pause","play","playing","progress","ratechange","reset","scroll","seeked","seeking","stalled","submit","suspend","timeupdate","toggle","unload","volumechange","waiting","DOMNodeInsertedIntoDocument","DOMNodeRemovedFromDocument"]),R={submit:!0},k=O(["click","dblclick","mousedown","mousemove","mouseup"]);class D{constructor(e){this.browserRendererId=e,this.afterClickCallbacks=[];const t=++D.nextEventDelegatorId;this.eventsCollectionKey=`_blazorEvents_${t}`,this.eventInfoStore=new F(this.onGlobalEvent.bind(this))}setListener(e,t,n,r){const o=this.getEventHandlerInfosForElement(e,!0),i=o.getHandler(t);if(i)this.eventInfoStore.update(i.eventHandlerId,n);else{const i={element:e,eventName:t,eventHandlerId:n,renderingComponentId:r};this.eventInfoStore.add(i),o.setHandler(t,i)}}getHandler(e){return this.eventInfoStore.get(e)}removeListener(e){const t=this.eventInfoStore.remove(e);if(t){const e=t.element,n=this.getEventHandlerInfosForElement(e,!1);n&&n.removeHandler(t.eventName)}}notifyAfterClick(e){this.afterClickCallbacks.push(e),this.eventInfoStore.addGlobalListener("click")}setStopPropagation(e,t,n){this.getEventHandlerInfosForElement(e,!0).stopPropagation(t,n)}setPreventDefault(e,t,n){this.getEventHandlerInfosForElement(e,!0).preventDefault(t,n)}onGlobalEvent(e){if(!(e.target instanceof Element))return;this.dispatchGlobalEventToAllElements(e.type,e);const t=(n=e.type,i.get(n));var n;t&&t.forEach((t=>this.dispatchGlobalEventToAllElements(t,e))),"click"===e.type&&this.afterClickCallbacks.forEach((t=>t(e)))}dispatchGlobalEventToAllElements(e,t){const n=t.composedPath();let o=n.shift(),i=null,s=!1;const c=Object.prototype.hasOwnProperty.call(_,e);let l=!1;for(;o;){const f=o,m=this.getEventHandlerInfosForElement(f,!1);if(m){const n=m.getHandler(e);if(n&&(u=f,d=t.type,!((u instanceof HTMLButtonElement||u instanceof HTMLInputElement||u instanceof HTMLTextAreaElement||u instanceof HTMLSelectElement)&&Object.prototype.hasOwnProperty.call(k,d)&&u.disabled))){if(!s){const n=a(e);i=(null==n?void 0:n.createEventArgs)?n.createEventArgs(t):{},s=!0}Object.prototype.hasOwnProperty.call(R,t.type)&&t.preventDefault(),C(this.browserRendererId,{eventHandlerId:n.eventHandlerId,eventName:e,eventFieldInfo:r.fromEvent(n.renderingComponentId,t)},i)}m.stopPropagation(e)&&(l=!0),m.preventDefault(e)&&t.preventDefault()}o=c||l?void 0:n.shift()}var u,d}getEventHandlerInfosForElement(e,t){return Object.prototype.hasOwnProperty.call(e,this.eventsCollectionKey)?e[this.eventsCollectionKey]:t?e[this.eventsCollectionKey]=new T:null}}D.nextEventDelegatorId=0;class F{constructor(e){this.globalListener=e,this.infosByEventHandlerId={},this.countByEventName={},s.push(this.handleEventNameAliasAdded.bind(this))}add(e){if(this.infosByEventHandlerId[e.eventHandlerId])throw new Error(`Event ${e.eventHandlerId} is already tracked`);this.infosByEventHandlerId[e.eventHandlerId]=e,this.addGlobalListener(e.eventName)}get(e){return this.infosByEventHandlerId[e]}addGlobalListener(e){if(e=c(e),Object.prototype.hasOwnProperty.call(this.countByEventName,e))this.countByEventName[e]++;else{this.countByEventName[e]=1;const t=Object.prototype.hasOwnProperty.call(_,e);document.addEventListener(e,this.globalListener,t)}}update(e,t){if(Object.prototype.hasOwnProperty.call(this.infosByEventHandlerId,t))throw new Error(`Event ${t} is already tracked`);const n=this.infosByEventHandlerId[e];delete this.infosByEventHandlerId[e],n.eventHandlerId=t,this.infosByEventHandlerId[t]=n}remove(e){const t=this.infosByEventHandlerId[e];if(t){delete this.infosByEventHandlerId[e];const n=c(t.eventName);0==--this.countByEventName[n]&&(delete this.countByEventName[n],document.removeEventListener(n,this.globalListener))}return t}handleEventNameAliasAdded(e,t){if(Object.prototype.hasOwnProperty.call(this.countByEventName,e)){const n=this.countByEventName[e];delete this.countByEventName[e],document.removeEventListener(e,this.globalListener),this.addGlobalListener(t),this.countByEventName[t]+=n-1}}}class T{constructor(){this.handlers={},this.preventDefaultFlags=null,this.stopPropagationFlags=null}getHandler(e){return Object.prototype.hasOwnProperty.call(this.handlers,e)?this.handlers[e]:null}setHandler(e,t){this.handlers[e]=t}removeHandler(e){delete this.handlers[e]}preventDefault(e,t){return void 0!==t&&(this.preventDefaultFlags=this.preventDefaultFlags||{},this.preventDefaultFlags[e]=t),!!this.preventDefaultFlags&&this.preventDefaultFlags[e]}stopPropagation(e,t){return void 0!==t&&(this.stopPropagationFlags=this.stopPropagationFlags||{},this.stopPropagationFlags[e]=t),!!this.stopPropagationFlags&&this.stopPropagationFlags[e]}}function O(e){const t={};return e.forEach((e=>{t[e]=!0})),t}const L=Symbol(),M=Symbol(),x=Symbol();function P(e,t){if(L in e)return e;const n=[];if(e.childNodes.length>0){if(!t)throw new Error("New logical elements must start empty, or allowExistingContents must be true");e.childNodes.forEach((t=>{const r=P(t,!0);r[M]=e,n.push(r)}))}return e[L]=n,e}function B(e){const t=U(e);for(;t.length;)H(e,0)}function J(e,t){const n=document.createComment("!");return j(n,e,t),n}function j(e,t,n){const r=e;let o=e;if(e instanceof Comment){const t=U(r);if((null==t?void 0:t.length)>0){const t=G(r),n=new Range;n.setStartBefore(e),n.setEndAfter(t),o=n.extractContents()}}const i=$(r);if(i){const e=U(i),t=Array.prototype.indexOf.call(e,r);e.splice(t,1),delete r[M]}const s=U(t);if(n<s.length){const e=s[n];e.parentNode.insertBefore(o,e),s.splice(n,0,r)}else Y(o,t),s.push(r);r[M]=t,L in r||(r[L]=[])}function H(e,t){const n=U(e).splice(t,1)[0];if(n instanceof Comment){const e=U(n);if(e)for(;e.length>0;)H(n,0)}const r=n;r.parentNode.removeChild(r)}function $(e){return e[M]||null}function z(e,t){return U(e)[t]}function W(e){const t=X(e);return"http://www.w3.org/2000/svg"===t.namespaceURI&&"foreignObject"!==t.tagName}function U(e){return e[L]}function K(e){const t=U($(e));return t[Array.prototype.indexOf.call(t,e)+1]||null}function V(e,t){const n=U(e);t.forEach((e=>{e.moveRangeStart=n[e.fromSiblingIndex],e.moveRangeEnd=G(e.moveRangeStart)})),t.forEach((t=>{const r=document.createComment("marker");t.moveToBeforeMarker=r;const o=n[t.toSiblingIndex+1];o?o.parentNode.insertBefore(r,o):Y(r,e)})),t.forEach((e=>{const t=e.moveToBeforeMarker,n=t.parentNode,r=e.moveRangeStart,o=e.moveRangeEnd;let i=r;for(;i;){const e=i.nextSibling;if(n.insertBefore(i,t),i===o)break;i=e}n.removeChild(t)})),t.forEach((e=>{n[e.toSiblingIndex]=e.moveRangeStart}))}function X(e){if(e instanceof Element||e instanceof DocumentFragment)return e;if(e instanceof Comment)return e.parentNode;throw new Error("Not a valid logical element")}function Y(e,t){if(t instanceof Element||t instanceof DocumentFragment)t.appendChild(e);else{if(!(t instanceof Comment))throw new Error(`Cannot append node because the parent is not a valid logical element. Parent: ${t}`);{const n=K(t);n?n.parentNode.insertBefore(e,n):Y(e,$(t))}}}function G(e){if(e instanceof Element||e instanceof DocumentFragment)return e;const t=K(e);if(t)return t.previousSibling;{const t=$(e);return t instanceof Element||t instanceof DocumentFragment?t.lastChild:G(t)}}function q(e){return`_bl_${e}`}const Z="__internalId";e.attachReviver(((e,t)=>t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,Z)&&"string"==typeof t[Z]?function(e){const t=`[${q(e)}]`;return document.querySelector(t)}(t[Z]):t));const Q="_blazorDeferredValue";function ee(e){return"select-multiple"===e.type}function te(e,t){e.value=t||""}function ne(e,t){e instanceof HTMLSelectElement?ee(e)?function(e,t){t||(t=[]);for(let n=0;n<e.options.length;n++)e.options[n].selected=-1!==t.indexOf(e.options[n].value)}(e,t):te(e,t):e.value=t}function re(e){const t=function(e){for(;e;){if(e instanceof HTMLSelectElement)return e;e=e.parentElement}return null}(e);if(!function(e){return!!e&&Q in e}(t))return!1;if(ee(t))e.selected=-1!==t._blazorDeferredValue.indexOf(e.value);else{if(t._blazorDeferredValue!==e.value)return!1;te(t,e.value),delete t._blazorDeferredValue}return!0}const oe=document.createElement("template"),ie=document.createElementNS("http://www.w3.org/2000/svg","g"),se=new Set,ae=Symbol(),ce=Symbol();class le{constructor(e){this.rootComponentIds=new Set,this.childComponentLocations={},this.eventDelegator=new D(e),this.eventDelegator.notifyAfterClick((e=>{Ie()&&function(e,t){if(0!==e.button||function(e){return e.ctrlKey||e.shiftKey||e.altKey||e.metaKey}(e))return;if(e.defaultPrevented)return;const n=function(e){const t=e.composedPath&&e.composedPath();if(t)for(let e=0;e<t.length;e++){const n=t[e];if(n instanceof HTMLAnchorElement||n instanceof SVGAElement)return n}return null}(e);if(n&&function(e){const t=e.getAttribute("target");return(!t||"_self"===t)&&e.hasAttribute("href")&&!e.hasAttribute("download")}(n)){const t=Ce(n.getAttribute("href"));we(t)&&(e.preventDefault(),Me(t,!0,!1))}}(e)}))}getRootComponentCount(){return this.rootComponentIds.size}attachRootComponentToLogicalElement(e,t,n){if(function(e){return e[ae]}(t))throw new Error(`Root component '${e}' could not be attached because its target element is already associated with a root component`);n&&(t=J(t,U(t).length)),ue(t,!0),this.attachComponentToElement(e,t),this.rootComponentIds.add(e),se.add(t)}updateComponent(e,t,n,r){var o;const i=this.childComponentLocations[t];if(!i)throw new Error(`No element is currently associated with component ${t}`);se.delete(i)&&(B(i),i instanceof Comment&&(i.textContent="!"));const s=null===(o=X(i))||void 0===o?void 0:o.getRootNode(),a=s&&s.activeElement;this.applyEdits(e,t,i,0,n,r),a instanceof HTMLElement&&s&&s.activeElement!==a&&a.focus()}disposeComponent(e){if(this.rootComponentIds.delete(e)){const t=this.childComponentLocations[e];ue(t,!1),!0===t[ce]?se.add(t):B(t)}delete this.childComponentLocations[e]}disposeEventHandler(e){this.eventDelegator.removeListener(e)}attachComponentToElement(e,t){this.childComponentLocations[e]=t}applyEdits(e,n,r,o,i,s){let a,c=0,l=o;const u=e.arrayBuilderSegmentReader,d=e.editReader,f=e.frameReader,m=u.values(i),h=u.offset(i),p=h+u.count(i);for(let i=h;i<p;i++){const u=e.diffReader.editsEntry(m,i),h=d.editType(u);switch(h){case t.prependFrame:{const t=d.newTreeIndex(u),o=e.referenceFramesEntry(s,t),i=d.siblingIndex(u);this.insertFrame(e,n,r,l+i,s,o,t);break}case t.removeFrame:H(r,l+d.siblingIndex(u));break;case t.setAttribute:{const t=d.newTreeIndex(u),o=e.referenceFramesEntry(s,t),i=z(r,l+d.siblingIndex(u));if(!(i instanceof Element))throw new Error("Cannot set attribute on non-element child");this.applyAttribute(e,n,i,o);break}case t.removeAttribute:{const e=z(r,l+d.siblingIndex(u));if(!(e instanceof Element))throw new Error("Cannot remove attribute from non-element child");{const t=d.removedAttributeName(u);this.setOrRemoveAttributeOrProperty(e,t,null)}break}case t.updateText:{const t=d.newTreeIndex(u),n=e.referenceFramesEntry(s,t),o=z(r,l+d.siblingIndex(u));if(!(o instanceof Text))throw new Error("Cannot set text content on non-text child");o.textContent=f.textContent(n);break}case t.updateMarkup:{const t=d.newTreeIndex(u),n=e.referenceFramesEntry(s,t),o=d.siblingIndex(u);H(r,l+o),this.insertMarkup(e,r,l+o,n);break}case t.stepIn:r=z(r,l+d.siblingIndex(u)),c++,l=0;break;case t.stepOut:r=$(r),c--,l=0===c?o:0;break;case t.permutationListEntry:a=a||[],a.push({fromSiblingIndex:l+d.siblingIndex(u),toSiblingIndex:l+d.moveToSiblingIndex(u)});break;case t.permutationListEnd:V(r,a),a=void 0;break;default:throw new Error(`Unknown edit type: ${h}`)}}}insertFrame(e,t,r,o,i,s,a){const c=e.frameReader,l=c.frameType(s);switch(l){case n.element:return this.insertElement(e,t,r,o,i,s,a),1;case n.text:return this.insertText(e,r,o,s),1;case n.attribute:throw new Error("Attribute frames should only be present as leading children of element frames.");case n.component:return this.insertComponent(e,r,o,s),1;case n.region:return this.insertFrameRange(e,t,r,o,i,a+1,a+c.subtreeLength(s));case n.elementReferenceCapture:if(r instanceof Element)return u=r,d=c.elementReferenceCaptureId(s),u.setAttribute(q(d),""),0;throw new Error("Reference capture frames can only be children of element frames.");case n.markup:return this.insertMarkup(e,r,o,s),1;case n.namedEvent:return 0;default:throw new Error(`Unknown frame type: ${l}`)}var u,d}insertElement(e,t,r,o,i,s,a){const c=e.frameReader,l=c.elementName(s),u="svg"===l||W(r)?document.createElementNS("http://www.w3.org/2000/svg",l):document.createElement(l),d=P(u);let f=!1;const m=a+c.subtreeLength(s);for(let s=a+1;s<m;s++){const a=e.referenceFramesEntry(i,s);if(c.frameType(a)!==n.attribute){j(u,r,o),f=!0,this.insertFrameRange(e,t,d,0,i,s,m);break}this.applyAttribute(e,t,u,a)}var h;f||j(u,r,o),(h=u)instanceof HTMLOptionElement?re(h):Q in h&&ne(h,h[Q])}insertComponent(e,t,n,r){const o=J(t,n),i=e.frameReader.componentId(r);this.attachComponentToElement(i,o)}insertText(e,t,n,r){const o=e.frameReader.textContent(r);j(document.createTextNode(o),t,n)}insertMarkup(e,t,n,r){const o=J(t,n),i=(s=e.frameReader.markupContent(r),W(t)?(ie.innerHTML=s||" ",ie):(oe.innerHTML=s||" ",oe.content.querySelectorAll("script").forEach((e=>{const t=document.createElement("script");t.textContent=e.textContent,e.getAttributeNames().forEach((n=>{t.setAttribute(n,e.getAttribute(n))})),e.parentNode.replaceChild(t,e)})),oe.content));var s;let a=0;for(;i.firstChild;)j(i.firstChild,o,a++)}applyAttribute(e,t,n,r){const o=e.frameReader,i=o.attributeName(r),s=o.attributeEventHandlerId(r);if(s){const e=fe(i);return void this.eventDelegator.setListener(n,e,s,t)}const a=o.attributeValue(r);this.setOrRemoveAttributeOrProperty(n,i,a)}insertFrameRange(e,t,n,r,o,i,s){const a=r;for(let a=i;a<s;a++){const i=e.referenceFramesEntry(o,a);r+=this.insertFrame(e,t,n,r,o,i,a),a+=de(e,i)}return r-a}setOrRemoveAttributeOrProperty(e,t,n){(function(e,t,n){switch(t){case"value":return function(e,t){switch(t&&"INPUT"===e.tagName&&(t=function(e,t){switch(t.getAttribute("type")){case"time":return 8!==e.length||!e.endsWith("00")&&t.hasAttribute("step")?e:e.substring(0,5);case"datetime-local":return 19!==e.length||!e.endsWith("00")&&t.hasAttribute("step")?e:e.substring(0,16);default:return e}}(t,e)),e.tagName){case"INPUT":case"SELECT":case"TEXTAREA":return t&&e instanceof HTMLSelectElement&&ee(e)&&(t=JSON.parse(t)),ne(e,t),e[Q]=t,!0;case"OPTION":return t||""===t?e.setAttribute("value",t):e.removeAttribute("value"),re(e),!0;default:return!1}}(e,n);case"checked":return function(e,t){return"INPUT"===e.tagName&&(e.checked=null!==t,!0)}(e,n);default:return!1}})(e,t,n)||(t.startsWith("__internal_")?this.applyInternalAttribute(e,t.substring(11),n):null!==n?e.setAttribute(t,n):e.removeAttribute(t))}applyInternalAttribute(e,t,n){if(t.startsWith("stopPropagation_")){const r=fe(t.substring(16));this.eventDelegator.setStopPropagation(e,r,null!==n)}else{if(!t.startsWith("preventDefault_"))throw new Error(`Unsupported internal attribute '${t}'`);{const r=fe(t.substring(15));this.eventDelegator.setPreventDefault(e,r,null!==n)}}}}function ue(e,t){e[ae]=t}function de(e,t){const r=e.frameReader;switch(r.frameType(t)){case n.component:case n.element:case n.region:return r.subtreeLength(t)-1;default:return 0}}function fe(e){if(e.startsWith("on"))return e.substring(2);throw new Error(`Attribute should be an event name, but doesn't start with 'on'. Value: '${e}'`)}const me={};let he,pe,ve,ge,be=!1;function ye(e,t,n,r){let o=me[e];o||(o=new le(e),me[e]=o),o.attachRootComponentToLogicalElement(n,t,r)}function we(e){const t=(n=document.baseURI).substring(0,n.lastIndexOf("/"));var n;const r=e.charAt(t.length);return e.startsWith(t)&&(""===r||"/"===r||"?"===r||"#"===r)}function Ee(e){var t;null===(t=document.getElementById(e))||void 0===t||t.scrollIntoView()}function Se(){return void 0!==pe}function Ae(e,t){if(!pe)throw new Error("No enhanced programmatic navigation handler has been attached");pe(e,t)}function Ce(e){return ge=ge||document.createElement("a"),ge.href=e,ge.href}function Ie(){return void 0!==he}function Ne(){return he}let _e=!1,Re=0,ke=0;const De=new Map;let Fe=async function(e){var t,n,r;Be();const o=$e();if(null==o?void 0:o.hasLocationChangingEventListeners){const i=null!==(n=null===(t=e.state)||void 0===t?void 0:t._index)&&void 0!==n?n:0,s=null===(r=e.state)||void 0===r?void 0:r.userState,a=i-Re,c=location.href;if(await Pe(-a),!await Je(c,s,!1,o))return;await Pe(a)}await je(!1)},Te=null;const Oe={listenForNavigationEvents:function(e,t,n){var r,o;De.set(e,{rendererId:e,hasLocationChangingEventListeners:!1,locationChanged:t,locationChanging:n}),_e||(_e=!0,window.addEventListener("popstate",He),Re=null!==(o=null===(r=history.state)||void 0===r?void 0:r._index)&&void 0!==o?o:0,ve=(e,t)=>{je(t,e)})},enableNavigationInterception:function(e){if(void 0!==he&&he!==e)throw new Error("Only one interactive runtime may enable navigation interception at a time.");he=e},setHasLocationChangingListeners:function(e,t){const n=De.get(e);if(!n)throw new Error(`Renderer with ID '${e}' is not listening for navigation events`);n.hasLocationChangingEventListeners=t},endLocationChanging:function(e,t){Te&&e===ke&&(Te(t),Te=null)},navigateTo:function(e,t){Le(e,t,!0)},refresh:function(e){!e&&Se()?Ae(location.href,!0):location.reload()},getBaseURI:()=>document.baseURI,getLocationHref:()=>location.href,scrollToElement:Ee};function Le(e,t,n=!1){const r=Ce(e),o=ze();if(t.forceLoad||!we(r)||"serverside-fullpageload"===o)!function(e,t){if(location.href===e){const t=e+"?";history.replaceState(null,"",t),location.replace(e)}else t?location.replace(e):location.href=e}(e,t.replaceHistoryEntry);else if("clientside-router"===o)Me(r,!1,t.replaceHistoryEntry,t.historyEntryState,n);else{if("serverside-enhanced"!==o)throw new Error(`Unsupported page load mechanism: ${o}`);Ae(r,t.replaceHistoryEntry)}}async function Me(e,t,n,r=void 0,o=!1){if(Be(),function(e){const t=new URL(e);return""!==t.hash&&location.origin===t.origin&&location.pathname===t.pathname&&location.search===t.search}(e))return xe(e,n,r),void function(e){const t=e.indexOf("#");t!==e.length-1&&Ee(e.substring(t+1))}(e);const i=$e();(o||!(null==i?void 0:i.hasLocationChangingEventListeners)||await Je(e,r,t,i))&&(be=!0,xe(e,n,r),await je(t))}function xe(e,t,n=void 0){t?history.replaceState({userState:n,_index:Re},"",e):(Re++,history.pushState({userState:n,_index:Re},"",e))}function Pe(e){return new Promise((t=>{const n=Fe;Fe=()=>{Fe=n,t()},history.go(e)}))}function Be(){Te&&(Te(!1),Te=null)}function Je(e,t,n,r){return new Promise((o=>{Be(),ke++,Te=o,r.locationChanging(ke,e,t,n)}))}async function je(e,t){const n=null!=t?t:location.href;await Promise.all(Array.from(De,(async([t,r])=>{var o,i;i=t,E.has(i)&&await r.locationChanged(n,null===(o=history.state)||void 0===o?void 0:o.userState,e)})))}async function He(e){var t,n;Fe&&"serverside-enhanced"!==ze()&&await Fe(e),Re=null!==(n=null===(t=history.state)||void 0===t?void 0:t._index)&&void 0!==n?n:0}function $e(){const e=Ne();if(void 0!==e)return De.get(e)}function ze(){return Ie()?"clientside-router":Se()?"serverside-enhanced":window.Blazor._internal.isBlazorWeb?"serverside-fullpageload":"clientside-router"}const We={focus:function(e,t){if(e instanceof HTMLElement)e.focus({preventScroll:t});else{if(!(e instanceof SVGElement))throw new Error("Unable to focus an invalid element.");if(!e.hasAttribute("tabindex"))throw new Error("Unable to focus an SVG element that does not have a tabindex.");e.focus({preventScroll:t})}},focusBySelector:function(e,t){const n=document.querySelector(e);n&&(n.hasAttribute("tabindex")||(n.tabIndex=-1),n.focus({preventScroll:!0}))}},Ue={init:function(e,t,n,r=50){const o=Ve(t);(o||document.documentElement).style.overflowAnchor="none";const i=document.createRange();f(n.parentElement)&&(t.style.display="table-row",n.style.display="table-row");const s=new IntersectionObserver((function(r){r.forEach((r=>{var o;if(!r.isIntersecting)return;i.setStartAfter(t),i.setEndBefore(n);const s=i.getBoundingClientRect().height,a=null===(o=r.rootBounds)||void 0===o?void 0:o.height;r.target===t?e.invokeMethodAsync("OnSpacerBeforeVisible",r.intersectionRect.top-r.boundingClientRect.top,s,a):r.target===n&&n.offsetHeight>0&&e.invokeMethodAsync("OnSpacerAfterVisible",r.boundingClientRect.bottom-r.intersectionRect.bottom,s,a)}))}),{root:o,rootMargin:`${r}px`});s.observe(t),s.observe(n);const a=d(t),c=d(n),{observersByDotNetObjectId:l,id:u}=Xe(e);function d(e){const t={attributes:!0},n=new MutationObserver(((n,r)=>{f(e.parentElement)&&(r.disconnect(),e.style.display="table-row",r.observe(e,t)),s.unobserve(e),s.observe(e)}));return n.observe(e,t),n}function f(e){return null!==e&&(e instanceof HTMLTableElement&&""===e.style.display||"table"===e.style.display||e instanceof HTMLTableSectionElement&&""===e.style.display||"table-row-group"===e.style.display)}l[u]={intersectionObserver:s,mutationObserverBefore:a,mutationObserverAfter:c}},dispose:function(e){const{observersByDotNetObjectId:t,id:n}=Xe(e),r=t[n];r&&(r.intersectionObserver.disconnect(),r.mutationObserverBefore.disconnect(),r.mutationObserverAfter.disconnect(),e.dispose(),delete t[n])}},Ke=Symbol();function Ve(e){return e&&e!==document.body&&e!==document.documentElement?"visible"!==getComputedStyle(e).overflowY?e:Ve(e.parentElement):null}function Xe(e){var t;const n=e._callDispatcher,r=e._id;return null!==(t=n[Ke])&&void 0!==t||(n[Ke]={}),{observersByDotNetObjectId:n[Ke],id:r}}const Ye={getAndRemoveExistingTitle:function(){var e;const t=document.head?document.head.getElementsByTagName("title"):[];if(0===t.length)return null;let n=null;for(let r=t.length-1;r>=0;r--){const o=t[r],i=o.previousSibling;i instanceof Comment&&null!==$(i)||(null===n&&(n=o.textContent),null===(e=o.parentNode)||void 0===e||e.removeChild(o))}return n}},Ge={init:function(e,t){t._blazorInputFileNextFileId=0,t.addEventListener("click",(function(){t.value=""})),t.addEventListener("change",(function(){t._blazorFilesById={};const n=Array.prototype.map.call(t.files,(function(e){const n={id:++t._blazorInputFileNextFileId,lastModified:new Date(e.lastModified).toISOString(),name:e.name,size:e.size,contentType:e.type,readPromise:void 0,arrayBuffer:void 0,blob:e};return t._blazorFilesById[n.id]=n,n}));e.invokeMethodAsync("NotifyChange",n)}))},toImageFile:async function(e,t,n,r,o){const i=qe(e,t),s=await new Promise((function(e){const t=new Image;t.onload=function(){URL.revokeObjectURL(t.src),e(t)},t.onerror=function(){t.onerror=null,URL.revokeObjectURL(t.src)},t.src=URL.createObjectURL(i.blob)})),a=await new Promise((function(e){var t;const i=Math.min(1,r/s.width),a=Math.min(1,o/s.height),c=Math.min(i,a),l=document.createElement("canvas");l.width=Math.round(s.width*c),l.height=Math.round(s.height*c),null===(t=l.getContext("2d"))||void 0===t||t.drawImage(s,0,0,l.width,l.height),l.toBlob(e,n)})),c={id:++e._blazorInputFileNextFileId,lastModified:i.lastModified,name:i.name,size:(null==a?void 0:a.size)||0,contentType:n,blob:a||i.blob};return e._blazorFilesById[c.id]=c,c},readFileData:async function(e,t){return qe(e,t).blob}};function qe(e,t){const n=e._blazorFilesById[t];if(!n)throw new Error(`There is no file with ID ${t}. The file list may have changed. See https://aka.ms/aspnet/blazor-input-file-multiple-selections.`);return n}const Ze=new Set,Qe={enableNavigationPrompt:function(e){0===Ze.size&&window.addEventListener("beforeunload",et),Ze.add(e)},disableNavigationPrompt:function(e){Ze.delete(e),0===Ze.size&&window.removeEventListener("beforeunload",et)}};function et(e){e.preventDefault(),e.returnValue=!0}const tt=new Map,nt={navigateTo:function(e,t,n=!1){Le(e,t instanceof Object?t:{forceLoad:t,replaceHistoryEntry:n})},registerCustomEventType:function(e,t){if(!t)throw new Error("The options parameter is required.");if(o.has(e))throw new Error(`The event '${e}' is already registered.`);if(t.browserEventName){const n=i.get(t.browserEventName);n?n.push(e):i.set(t.browserEventName,[e]),s.forEach((n=>n(e,t.browserEventName)))}o.set(e,t)},rootComponents:g,runtime:{},_internal:{navigationManager:Oe,domWrapper:We,Virtualize:Ue,PageTitle:Ye,InputFile:Ge,NavigationLock:Qe,getJSDataStreamChunk:async function(e,t,n){return e instanceof Blob?await async function(e,t,n){const r=e.slice(t,t+n),o=await r.arrayBuffer();return new Uint8Array(o)}(e,t,n):function(e,t,n){return new Uint8Array(e.buffer,e.byteOffset+t,n)}(e,t,n)},attachWebRendererInterop:function(t,n,r,o){var i,s;if(E.has(t))throw new Error(`Interop methods are already registered for renderer ${t}`);E.set(t,n),r&&o&&Object.keys(r).length>0&&function(t,n,r){if(h)throw new Error("Dynamic root components have already been enabled.");h=t,p=n;for(const[t,o]of Object.entries(r)){const r=e.findJSFunction(t,0);for(const e of o)r(e,n[e])}}(I(t),r,o),null===(s=null===(i=A.get(t))||void 0===i?void 0:i[0])||void 0===s||s.call(i),function(e){for(const t of S)t(e)}(t)}}};var rt,ot;window.Blazor=nt;const it=navigator,st=it.userAgentData&&it.userAgentData.brands,at=st&&st.length>0?st.some((e=>"Google Chrome"===e.brand||"Microsoft Edge"===e.brand||"Chromium"===e.brand)):window.chrome,ct=null!==(ot=null===(rt=it.userAgentData)||void 0===rt?void 0:rt.platform)&&void 0!==ot?ot:navigator.platform;function lt(e){return 0!==e.debugLevel&&(at||navigator.userAgent.includes("Firefox"))}let ut=!1;function dt(){const e=document.querySelector("#blazor-error-ui");e&&(e.style.display="block"),ut||(ut=!0,document.querySelectorAll("#blazor-error-ui .reload").forEach((e=>{e.onclick=function(e){location.reload(),e.preventDefault()}})),document.querySelectorAll("#blazor-error-ui .dismiss").forEach((e=>{e.onclick=function(e){const t=document.querySelector("#blazor-error-ui");t&&(t.style.display="none"),e.preventDefault()}})))}var ft,mt;!function(e){e[e.Default=0]="Default",e[e.Server=1]="Server",e[e.WebAssembly=2]="WebAssembly",e[e.WebView=3]="WebView"}(ft||(ft={})),function(e){e[e.Trace=0]="Trace",e[e.Debug=1]="Debug",e[e.Information=2]="Information",e[e.Warning=3]="Warning",e[e.Error=4]="Error",e[e.Critical=5]="Critical",e[e.None=6]="None"}(mt||(mt={}));class ht{constructor(e=!0,t,n,r=0){this.singleRuntime=e,this.logger=t,this.webRendererId=r,this.afterStartedCallbacks=[],n&&this.afterStartedCallbacks.push(...n)}async importInitializersAsync(e,t){await Promise.all(e.map((e=>async function(e,n){const r=function(e){const t=document.baseURI;return t.endsWith("/")?`${t}${e}`:`${t}/${e}`}(n),o=await import(r);if(void 0!==o){if(e.singleRuntime){const{beforeStart:n,afterStarted:r,beforeWebAssemblyStart:s,afterWebAssemblyStarted:a,beforeServerStart:c,afterServerStarted:l}=o;let u=n;e.webRendererId===ft.Server&&c&&(u=c),e.webRendererId===ft.WebAssembly&&s&&(u=s);let d=r;return e.webRendererId===ft.Server&&l&&(d=l),e.webRendererId===ft.WebAssembly&&a&&(d=a),i(e,u,d,t)}return function(e,t,n){var o;const s=n[0],{beforeStart:a,afterStarted:c,beforeWebStart:l,afterWebStarted:u,beforeWebAssemblyStart:d,afterWebAssemblyStarted:f,beforeServerStart:m,afterServerStarted:h}=t,p=!(l||u||d||f||m||h||!a&&!c),v=p&&s.enableClassicInitializers;if(p&&!s.enableClassicInitializers)null===(o=e.logger)||void 0===o||o.log(mt.Warning,`Initializer '${r}' will be ignored because multiple runtimes are available. use 'before(web|webAssembly|server)Start' and 'after(web|webAssembly|server)Started?' instead.)`);else if(v)return i(e,a,c,n);if(function(e){e.webAssembly?e.webAssembly.initializers||(e.webAssembly.initializers={beforeStart:[],afterStarted:[]}):e.webAssembly={initializers:{beforeStart:[],afterStarted:[]}},e.circuit?e.circuit.initializers||(e.circuit.initializers={beforeStart:[],afterStarted:[]}):e.circuit={initializers:{beforeStart:[],afterStarted:[]}}}(s),d&&s.webAssembly.initializers.beforeStart.push(d),f&&s.webAssembly.initializers.afterStarted.push(f),m&&s.circuit.initializers.beforeStart.push(m),h&&s.circuit.initializers.afterStarted.push(h),u&&e.afterStartedCallbacks.push(u),l)return l(s)}(e,o,t)}function i(e,t,n,r){if(n&&e.afterStartedCallbacks.push(n),t)return t(...r)}}(this,e))))}async invokeAfterStartedCallbacks(e){const t=function(e){var t;return null===(t=A.get(e))||void 0===t?void 0:t[1]}(this.webRendererId);t&&await t,await Promise.all(this.afterStartedCallbacks.map((t=>t(e))))}}let pt,vt,gt,bt,yt,wt,Et;const St=Math.pow(2,32),At=Math.pow(2,21)-1;let Ct=null;function It(e){return vt.getI32(e)}const Nt={load:function(e,t){return async function(e,t){const{dotnet:n}=await async function(e){if("undefined"==typeof WebAssembly||!WebAssembly.validate)throw new Error("This browser does not support WebAssembly.");let t="_framework/dotnet.js";if(e.loadBootResource){const n="dotnetjs",r=e.loadBootResource(n,"dotnet.js",t,"","js-module-dotnet");if("string"==typeof r)t=r;else if(r)throw new Error(`For a ${n} resource, custom loaders must supply a URI string.`)}const n=new URL(t,document.baseURI).toString();return await import(n)}(e),r=function(e,t){const n={maxParallelDownloads:1e6,enableDownloadRetry:!1,applicationEnvironment:e.environment},r={...window.Module||{},onConfigLoaded:async n=>{n.environmentVariables||(n.environmentVariables={}),"sharded"===n.globalizationMode&&(n.environmentVariables.__BLAZOR_SHARDED_ICU="1"),nt._internal.getApplicationEnvironment=()=>n.applicationEnvironment,null==t||t(n),Et=await async function(e,t){var n,r,o;if(e.initializers)return await Promise.all(e.initializers.beforeStart.map((t=>t(e)))),new ht(!1,void 0,e.initializers.afterStarted,ft.WebAssembly);{const i=[e,null!==(r=null===(n=t.resources)||void 0===n?void 0:n.extensions)&&void 0!==r?r:{}],s=new ht(!0,void 0,void 0,ft.WebAssembly),a=Object.keys((null===(o=null==t?void 0:t.resources)||void 0===o?void 0:o.libraryInitializers)||{});return await s.importInitializersAsync(a,i),s}}(e,n)},onDownloadResourceProgress:_t,config:n,disableDotnet6Compatibility:!1,out:kt,err:Dt};return r}(e,t);e.applicationCulture&&n.withApplicationCulture(e.applicationCulture),e.environment&&n.withApplicationEnvironment(e.environment),e.loadBootResource&&n.withResourceLoader(e.loadBootResource),n.withModuleConfig(r),e.configureRuntime&&e.configureRuntime(n),wt=await n.create()}(e,t)},start:function(){return async function(){if(!wt)throw new Error("The runtime must be loaded it gets configured.");const{MONO:t,BINDING:n,Module:r,setModuleImports:o,INTERNAL:i,getConfig:s,invokeLibraryInitializers:a}=wt;gt=r,pt=n,vt=t,yt=i,function(e){const t=ct.match(/^Mac/i)?"Cmd":"Alt";lt(e)&&console.info(`Debugging hotkey: Shift+${t}+D (when application has focus)`),document.addEventListener("keydown",(t=>{t.shiftKey&&(t.metaKey||t.altKey)&&"KeyD"===t.code&&(lt(e)?navigator.userAgent.includes("Firefox")?async function(){const e=await fetch(`_framework/debug?url=${encodeURIComponent(location.href)}&isFirefox=true`);200!==e.status&&console.warn(await e.text())}():at?function(){const e=document.createElement("a");e.href=`_framework/debug?url=${encodeURIComponent(location.href)}`,e.target="_blank",e.rel="noopener noreferrer",e.click()}():console.error("Currently, only Microsoft Edge (80+), Google Chrome, or Chromium, are supported for debugging."):console.error("Cannot start debugging, because the application was not compiled with debugging enabled."))}))}(s()),nt.runtime=wt,nt._internal.dotNetCriticalError=Dt,o("blazor-internal",{Blazor:{_internal:nt._internal}});const c=await wt.getAssemblyExports("Microsoft.AspNetCore.Components.WebAssembly");return Object.assign(nt._internal,{dotNetExports:{...c.Microsoft.AspNetCore.Components.WebAssembly.Services.DefaultWebAssemblyJSRuntime}}),bt=e.attachDispatcher({beginInvokeDotNetFromJS:(e,t,n,r,o)=>{if(Tt(),!r&&!t)throw new Error("Either assemblyName or dotNetObjectId must have a non null value.");const i=r?r.toString():t;nt._internal.dotNetExports.BeginInvokeDotNet(e?e.toString():null,i,n,o)},endInvokeJSFromDotNet:(e,t,n)=>{nt._internal.dotNetExports.EndInvokeJS(n)},sendByteArray:(e,t)=>{nt._internal.dotNetExports.ReceiveByteArrayFromJS(e,t)},invokeDotNetFromJS:(e,t,n,r)=>(Tt(),nt._internal.dotNetExports.InvokeDotNet(e||null,t,null!=n?n:0,r))}),{invokeLibraryInitializers:a}}()},callEntryPoint:async function(){try{await wt.runMain(wt.getConfig().mainAssemblyName,[])}catch(e){console.error(e),dt()}},toUint8Array:function(e){const t=Ft(e),n=It(t),r=new Uint8Array(n);return r.set(gt.HEAPU8.subarray(t+4,t+4+n)),r},getArrayLength:function(e){return It(Ft(e))},getArrayEntryPtr:function(e,t,n){return Ft(e)+4+t*n},getObjectFieldsBaseAddress:function(e){return e+8},readInt16Field:function(e,t){return n=e+(t||0),vt.getI16(n);var n},readInt32Field:function(e,t){return It(e+(t||0))},readUint64Field:function(e,t){return function(e){const t=e>>2,n=gt.HEAPU32[t+1];if(n>At)throw new Error(`Cannot read uint64 with high order part ${n}, because the result would exceed Number.MAX_SAFE_INTEGER.`);return n*St+gt.HEAPU32[t]}(e+(t||0))},readFloatField:function(e,t){return n=e+(t||0),vt.getF32(n);var n},readObjectField:function(e,t){return It(e+(t||0))},readStringField:function(e,t,n){const r=It(e+(t||0));if(0===r)return null;if(n){const e=pt.unbox_mono_obj(r);return"boolean"==typeof e?e?"":null:e}return pt.conv_string(r)},readStructField:function(e,t){return e+(t||0)},beginHeapLock:function(){return Tt(),Ct=Ot.create(),Ct},invokeWhenHeapUnlocked:function(e){Ct?Ct.enqueuePostReleaseAction(e):e()}};function _t(e,t){const n=e/t*100;document.documentElement.style.setProperty("--blazor-load-percentage",`${n}%`),document.documentElement.style.setProperty("--blazor-load-percentage-text",`"${Math.floor(n)}%"`)}const Rt=["DEBUGGING ENABLED"],kt=e=>Rt.indexOf(e)<0&&console.log(e),Dt=e=>{console.error(e||"(null)"),dt()};function Ft(e){return e+12}function Tt(){if(Ct)throw new Error("Assertion failed - heap is currently locked")}class Ot{enqueuePostReleaseAction(e){this.postReleaseActions||(this.postReleaseActions=[]),this.postReleaseActions.push(e)}release(){var e;if(Ct!==this)throw new Error("Trying to release a lock which isn't current");for(yt.mono_wasm_gc_unlock(),Ct=null;null===(e=this.postReleaseActions)||void 0===e?void 0:e.length;)this.postReleaseActions.shift()(),Tt()}static create(){return yt.mono_wasm_gc_lock(),new Ot}}let Lt;class Mt{constructor(e){this.batchAddress=e,this.arrayRangeReader=xt,this.arrayBuilderSegmentReader=Pt,this.diffReader=Bt,this.editReader=Jt,this.frameReader=jt}updatedComponents(){return Lt.readStructField(this.batchAddress,0)}referenceFrames(){return Lt.readStructField(this.batchAddress,xt.structLength)}disposedComponentIds(){return Lt.readStructField(this.batchAddress,2*xt.structLength)}disposedEventHandlerIds(){return Lt.readStructField(this.batchAddress,3*xt.structLength)}updatedComponentsEntry(e,t){return Ht(e,t,Bt.structLength)}referenceFramesEntry(e,t){return Ht(e,t,jt.structLength)}disposedComponentIdsEntry(e,t){const n=Ht(e,t,4);return Lt.readInt32Field(n)}disposedEventHandlerIdsEntry(e,t){const n=Ht(e,t,8);return Lt.readUint64Field(n)}}const xt={structLength:8,values:e=>Lt.readObjectField(e,0),count:e=>Lt.readInt32Field(e,4)},Pt={structLength:12,values:e=>{const t=Lt.readObjectField(e,0),n=Lt.getObjectFieldsBaseAddress(t);return Lt.readObjectField(n,0)},offset:e=>Lt.readInt32Field(e,4),count:e=>Lt.readInt32Field(e,8)},Bt={structLength:4+Pt.structLength,componentId:e=>Lt.readInt32Field(e,0),edits:e=>Lt.readStructField(e,4),editsEntry:(e,t)=>Ht(e,t,Jt.structLength)},Jt={structLength:20,editType:e=>Lt.readInt32Field(e,0),siblingIndex:e=>Lt.readInt32Field(e,4),newTreeIndex:e=>Lt.readInt32Field(e,8),moveToSiblingIndex:e=>Lt.readInt32Field(e,8),removedAttributeName:e=>Lt.readStringField(e,16)},jt={structLength:36,frameType:e=>Lt.readInt16Field(e,4),subtreeLength:e=>Lt.readInt32Field(e,8),elementReferenceCaptureId:e=>Lt.readStringField(e,16),componentId:e=>Lt.readInt32Field(e,12),elementName:e=>Lt.readStringField(e,16),textContent:e=>Lt.readStringField(e,16),markupContent:e=>Lt.readStringField(e,16),attributeName:e=>Lt.readStringField(e,16),attributeValue:e=>Lt.readStringField(e,24,!0),attributeEventHandlerId:e=>Lt.readUint64Field(e,8)};function Ht(e,t,n){return Lt.getArrayEntryPtr(e,t,n)}const $t=/^\s*Blazor-WebAssembly-Component-State:(?<state>[a-zA-Z0-9+/=]+)$/;function zt(e,t,n="state"){var r;if(e.nodeType===Node.COMMENT_NODE){const o=e.textContent||"",i=t.exec(o),s=i&&i.groups&&i.groups[n];return s&&(null===(r=e.parentNode)||void 0===r||r.removeChild(e)),s}if(!e.hasChildNodes())return;const o=e.childNodes;for(let e=0;e<o.length;e++){const r=zt(o[e],t,n);if(r)return r}}function Wt(e,t){const n=[],r=new en(e.childNodes);for(;r.next()&&r.currentElement;){const e=Kt(r,t);if(e)n.push(e);else if(r.currentElement.hasChildNodes()){const e=Wt(r.currentElement,t);for(let t=0;t<e.length;t++){const r=e[t];n.push(r)}}}return n}const Ut=new RegExp(/^\s*Blazor:[^{]*(?<descriptor>.*)$/);function Kt(e,t){const n=e.currentElement;var r,o,i;if(n&&n.nodeType===Node.COMMENT_NODE&&n.textContent){const s=Ut.exec(n.textContent),a=s&&s.groups&&s.groups.descriptor;if(!a)return;!function(e){if(e.parentNode instanceof Document)throw new Error("Root components cannot be marked as interactive. The <html> element must be rendered statically so that scripts are not evaluated multiple times.")}(n);try{const s=function(e){const t=JSON.parse(e),{type:n}=t;if("server"!==n&&"webassembly"!==n&&"auto"!==n)throw new Error(`Invalid component type '${n}'.`);return t}(a),c=function(e,t,n){const{prerenderId:r}=e;if(r){for(;n.next()&&n.currentElement;){const e=n.currentElement;if(e.nodeType!==Node.COMMENT_NODE)continue;if(!e.textContent)continue;const t=Ut.exec(e.textContent),o=t&&t[1];if(o)return Qt(o,r),e}throw new Error(`Could not find an end component comment for '${t}'.`)}}(s,n,e);if(t!==s.type)return;switch(s.type){case"webassembly":return o=n,i=c,Zt(r=s),{...r,uniqueId:Gt++,start:o,end:i};case"server":return function(e,t,n){return qt(e),{...e,uniqueId:Gt++,start:t,end:n}}(s,n,c);case"auto":return function(e,t,n){return qt(e),Zt(e),{...e,uniqueId:Gt++,start:t,end:n}}(s,n,c)}}catch(e){throw new Error(`Found malformed component comment at ${n.textContent}`)}}}let Vt,Xt,Yt,Gt=0;function qt(e){const{descriptor:t,sequence:n}=e;if(!t)throw new Error("descriptor must be defined when using a descriptor.");if(void 0===n)throw new Error("sequence must be defined when using a descriptor.");if(!Number.isInteger(n))throw new Error(`Error parsing the sequence '${n}' for component '${JSON.stringify(e)}'`)}function Zt(e){const{assembly:t,typeName:n}=e;if(!t)throw new Error("assembly must be defined when using a descriptor.");if(!n)throw new Error("typeName must be defined when using a descriptor.");e.parameterDefinitions=e.parameterDefinitions&&atob(e.parameterDefinitions),e.parameterValues=e.parameterValues&&atob(e.parameterValues)}function Qt(e,t){const n=JSON.parse(e);if(1!==Object.keys(n).length)throw new Error(`Invalid end of component comment: '${e}'`);const r=n.prerenderId;if(!r)throw new Error(`End of component comment must have a value for the prerendered property: '${e}'`);if(r!==t)throw new Error(`End of component comment prerendered property must match the start comment prerender id: '${t}', '${r}'`)}class en{constructor(e){this.childNodes=e,this.currentIndex=-1,this.length=e.length}next(){return this.currentIndex++,this.currentIndex<this.length?(this.currentElement=this.childNodes[this.currentIndex],!0):(this.currentElement=void 0,!1)}}class tn{constructor(e){this.componentManager=e}resolveRegisteredElement(e){const t=Number.parseInt(e);if(!Number.isNaN(t))return function(e){const{start:t,end:n}=e,r=t[x];if(r){if(r!==e)throw new Error("The start component comment was already associated with another component descriptor.");return t}const o=t.parentNode;if(!o)throw new Error(`Comment not connected to the DOM ${t.textContent}`);const i=P(o,!0),s=U(i);t[M]=i,t[x]=e;const a=P(t);if(n){const e=U(a),r=Array.prototype.indexOf.call(s,a)+1;let o=null;for(;o!==n;){const n=s.splice(r,1)[0];if(!n)throw new Error("Could not find the end component comment in the parent logical node list");n[M]=t,e.push(n),o=n}}return a}(this.componentManager.resolveRootComponent(t))}getParameterValues(e){return this.componentManager.initialComponents[e].parameterValues}getParameterDefinitions(e){return this.componentManager.initialComponents[e].parameterDefinitions}getTypeName(e){return this.componentManager.initialComponents[e].typeName}getAssembly(e){return this.componentManager.initialComponents[e].assembly}getCount(){return this.componentManager.initialComponents.length}}let nn,rn,on,sn=!1,an=!1,cn=!1;new Promise((e=>{rn=e}));const ln=new Promise((e=>{on=e}));let un;function dn(e){if(Vt)throw new Error("WebAssembly options have already been configured.");!async function(e){const t=await e;Vt=t,un()}(e)}function fn(e){if(void 0!==nn)throw new Error("Blazor WebAssembly has already started.");return nn=new Promise(mn.bind(null,e)),nn}async function mn(e,t,n){(function(){if(window.parent!==window&&!window.opener&&window.frameElement){const e=window.sessionStorage&&window.sessionStorage["Microsoft.AspNetCore.Components.WebAssembly.Authentication.CachedAuthSettings"],t=e&&JSON.parse(e);return t&&t.redirect_uri&&location.href.startsWith(t.redirect_uri)}return!1})()&&await new Promise((()=>{}));const r=hn();!function(e){const t=N;N=(e,n,r)=>{((e,t,n)=>{const r=function(e){return me[e]}(e);(null==r?void 0:r.eventDelegator.getHandler(t))&&Nt.invokeWhenHeapUnlocked(n)})(e,n,(()=>t(e,n,r)))}}(),nt._internal.applyHotReload=(e,t,n,r)=>{bt.invokeDotNetStaticMethod("Microsoft.AspNetCore.Components.WebAssembly","ApplyHotReloadDelta",e,t,n,r)},nt._internal.getApplyUpdateCapabilities=()=>bt.invokeDotNetStaticMethod("Microsoft.AspNetCore.Components.WebAssembly","GetApplyUpdateCapabilities"),nt._internal.invokeJSFromDotNet=pn,nt._internal.invokeJSJson=vn,nt._internal.endInvokeDotNetFromJS=gn,nt._internal.receiveWebAssemblyDotNetDataStream=bn,nt._internal.receiveByteArray=yn;const o=(Lt=Nt,Lt);nt.platform=o,nt._internal.renderBatch=(e,t)=>{const n=Nt.beginHeapLock();try{!function(e,t){const n=me[e];if(!n)throw new Error(`There is no browser renderer with ID ${e}.`);const r=t.arrayRangeReader,o=t.updatedComponents(),i=r.values(o),s=r.count(o),a=t.referenceFrames(),c=r.values(a),l=t.diffReader;for(let e=0;e<s;e++){const r=t.updatedComponentsEntry(i,e),o=l.componentId(r),s=l.edits(r);n.updateComponent(t,o,s,c)}const u=t.disposedComponentIds(),d=r.values(u),f=r.count(u);for(let e=0;e<f;e++){const r=t.disposedComponentIdsEntry(d,e);n.disposeComponent(r)}const m=t.disposedEventHandlerIds(),h=r.values(m),p=r.count(m);for(let e=0;e<p;e++){const r=t.disposedEventHandlerIdsEntry(h,e);n.disposeEventHandler(r)}be&&(be=!1,window.scrollTo&&window.scrollTo(0,0))}(e,new Mt(t))}finally{n.release()}},nt._internal.navigationManager.listenForNavigationEvents(ft.WebAssembly,(async(e,t,n)=>{await bt.invokeDotNetStaticMethodAsync("Microsoft.AspNetCore.Components.WebAssembly","NotifyLocationChanged",e,t,n)}),(async(e,t,n,r)=>{const o=await bt.invokeDotNetStaticMethodAsync("Microsoft.AspNetCore.Components.WebAssembly","NotifyLocationChangingAsync",t,n,r);nt._internal.navigationManager.endLocationChanging(e,o)}));const i=new tn(e);let s;nt._internal.registeredComponents={getRegisteredComponentsCount:()=>i.getCount(),getAssembly:e=>i.getAssembly(e),getTypeName:e=>i.getTypeName(e),getParameterDefinitions:e=>i.getParameterDefinitions(e)||"",getParameterValues:e=>i.getParameterValues(e)||""},nt._internal.getPersistedState=()=>zt(document,$t)||"",nt._internal.getInitialComponentsUpdate=()=>ln,nt._internal.updateRootComponents=e=>{var t;return null===(t=nt._internal.dotNetExports)||void 0===t?void 0:t.UpdateRootComponentsCore(e)},nt._internal.endUpdateRootComponents=t=>{var n;return null===(n=e.onAfterUpdateRootComponents)||void 0===n?void 0:n.call(e,t)},nt._internal.attachRootComponentToElement=(e,t,n)=>{const r=i.resolveRegisteredElement(e);r?ye(n,r,t,!1):function(e,t,n){const r="::before";let o=!1;if(e.endsWith("::after"))e=e.slice(0,-7),o=!0;else if(e.endsWith(r))throw new Error(`The '${r}' selector is not supported.`);const i=function(e){const t=m.get(e);if(t)return m.delete(e),t}(e)||document.querySelector(e);if(!i)throw new Error(`Could not find any element matching selector '${e}'.`);ye(n,P(i,!0),t,o)}(e,t,n)};try{await r,s=await o.start()}catch(e){throw new Error(`Failed to start platform. Reason: ${e}`)}o.callEntryPoint(),Et.invokeAfterStartedCallbacks(nt),an=!0,t()}function hn(){return null!=Yt||(Yt=(async()=>{await Xt;const e=null!=Vt?Vt:{},t=null==Vt?void 0:Vt.configureRuntime;e.configureRuntime=e=>{null==t||t(e),cn&&e.withEnvironmentVariable("__BLAZOR_WEBASSEMBLY_WAIT_FOR_ROOT_COMPONENTS","true")},await Nt.load(e,rn),sn=!0})()),Yt}function pn(t,n,r,o){const i=Nt.readStringField(t,0),s=Nt.readInt32Field(t,4),a=Nt.readStringField(t,8),c=Nt.readUint64Field(t,20);if(null!==a){const e=Nt.readUint64Field(t,12);if(0!==e)return bt.beginInvokeJSFromDotNet(e,i,a,s,c),0;{const e=bt.invokeJSFromDotNet(i,a,s,c);return null===e?0:pt.js_string_to_mono_string(e)}}{const t=e.findJSFunction(i,c).call(null,n,r,o);switch(s){case e.JSCallResultType.Default:return t;case e.JSCallResultType.JSObjectReference:return e.createJSObjectReference(t).__jsObjectId;case e.JSCallResultType.JSStreamReference:{const n=e.createJSStreamReference(t),r=JSON.stringify(n);return pt.js_string_to_mono_string(r)}case e.JSCallResultType.JSVoidResult:return null;default:throw new Error(`Invalid JS call result type '${s}'.`)}}}function vn(e,t,n,r,o){return 0!==o?(bt.beginInvokeJSFromDotNet(o,e,r,n,t),null):bt.invokeJSFromDotNet(e,r,n,t)}function gn(e,t,n){bt.endInvokeDotNetFromJS(e,t,n)}function bn(e,t,n,r){!function(e,t,n,r,o){let i=tt.get(t);if(!i){const n=new ReadableStream({start(e){tt.set(t,e),i=e}});e.supplyDotNetStream(t,n)}o?(i.error(o),tt.delete(t)):0===r?(i.close(),tt.delete(t)):i.enqueue(n.length===r?n:n.subarray(0,r))}(bt,e,t,n,r)}function yn(e,t){bt.receiveByteArray(e,t)}Xt=new Promise((e=>{un=e}));class wn{constructor(e){this.initialComponents=e}resolveRootComponent(e){return this.initialComponents[e]}}class En{constructor(){this._eventListeners=new Map}static create(e){const t=new En;return e.addEventListener=t.addEventListener.bind(t),e.removeEventListener=t.removeEventListener.bind(t),t}addEventListener(e,t){let n=this._eventListeners.get(e);n||(n=new Set,this._eventListeners.set(e,n)),n.add(t)}removeEventListener(e,t){var n;null===(n=this._eventListeners.get(e))||void 0===n||n.delete(t)}dispatchEvent(e,t){const n=this._eventListeners.get(e);if(!n)return;const r={...t,type:e};for(const e of n)e(r)}}let Sn=!1;async function An(e){if(Sn)throw new Error("Blazor has already started.");Sn=!0,dn(Promise.resolve(e||{})),En.create(nt);const t=Wt(document,"webassembly"),n=new wn(t);await fn(n)}nt.start=An,window.DotNet=e,document&&document.currentScript&&"false"!==document.currentScript.getAttribute("autostart")&&An().catch((e=>{void 0!==gt&&gt.err?gt.err(e):console.error(e)}))})();