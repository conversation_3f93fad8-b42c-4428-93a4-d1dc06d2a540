C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.csproj.AssemblyReference.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.GeneratedMSBuildEditorConfig.editorconfig
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.AssemblyInfoInputs.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.AssemblyInfo.cs
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.csproj.CoreCompileInputs.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.MvcApplicationPartsAssemblyInfo.cs
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.MvcApplicationPartsAssemblyInfo.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\appsettings.Development.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\appsettings.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApi.staticwebassets.endpoints.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApi.exe
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApi.deps.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApi.runtimeconfig.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApi.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Dapper.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\IdentityModel.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\IdentityModel.AspNetCore.OAuth2Introspection.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\IdentityServer4.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\IdentityServer4.AccessTokenValidation.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\IdentityServer4.Storage.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.OpenIdConnect.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win-arm64\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win-x64\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win-x86\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicCommon.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicCommon.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\scopedcss\bundle\ShiningCMusicApi.styles.css
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\staticwebassets.build.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\staticwebassets.development.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningC.F8DE0432.Up2Date
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\refint\ShiningCMusicApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ShiningCMusicApi.genruntimeconfig.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\ref\ShiningCMusicApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\rpswa.dswa.cache.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\rjimswa.dswa.cache.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApp.staticwebassets.runtime.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApp.staticwebassets.endpoints.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApi.staticwebassets.runtime.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Authorization.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Components.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Forms.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Web.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Components.WebAssembly.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Metadata.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Binder.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.JSInterop.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.JSInterop.WebAssembly.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Buttons.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Calendars.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Core.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Data.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.DropDowns.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Inputs.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Lists.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Navigations.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Notifications.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Popups.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Schedule.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Spinner.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.SplitButtons.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Blazor.Themes.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.ExcelExport.Net.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Syncfusion.Licensing.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\ShiningCMusicApp.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\BrowserDebugHost.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\BrowserDebugHost.runtimeconfig.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\BrowserDebugProxy.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\Microsoft.CodeAnalysis.CSharp.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\Microsoft.CodeAnalysis.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\Microsoft.CodeAnalysis.Scripting.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\Microsoft.FileFormats.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\Microsoft.NET.WebAssembly.Webcil.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\Microsoft.SymbolStore.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\BlazorDebugProxy\Newtonsoft.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.AspNetCore.Components.WebAssembly.Server.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Azure.Core.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Azure.Identity.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.SqlServer.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.ClientModel.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.Drawing.Common.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.Memory.Data.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.Runtime.Caching.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.Security.Permissions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\System.Windows.Extensions.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
