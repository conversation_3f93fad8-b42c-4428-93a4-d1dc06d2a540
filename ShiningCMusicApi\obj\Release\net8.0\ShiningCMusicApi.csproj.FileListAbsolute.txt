C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\appsettings.Development.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\appsettings.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\ShiningCMusicApi.staticwebassets.endpoints.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\ShiningCMusicApi.exe
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\ShiningCMusicApi.deps.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\ShiningCMusicApi.runtimeconfig.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\ShiningCMusicApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\ShiningCMusicApi.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Dapper.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\IdentityModel.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\IdentityModel.AspNetCore.OAuth2Introspection.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\IdentityServer4.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\IdentityServer4.AccessTokenValidation.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\IdentityServer4.Storage.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Microsoft.AspNetCore.Authentication.OpenIdConnect.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Microsoft.IdentityModel.Logging.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Microsoft.OpenApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Newtonsoft.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\runtimes\win-arm64\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\runtimes\win-x64\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\runtimes\win-x86\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\ShiningCMusicCommon.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\ShiningCMusicCommon.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.csproj.AssemblyReference.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.GeneratedMSBuildEditorConfig.editorconfig
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.AssemblyInfoInputs.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.AssemblyInfo.cs
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.csproj.CoreCompileInputs.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.MvcApplicationPartsAssemblyInfo.cs
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.MvcApplicationPartsAssemblyInfo.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\scopedcss\bundle\ShiningCMusicApi.styles.css
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets.build.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets.development.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets.build.endpoints.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets\msbuild.ShiningCMusicApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets\msbuild.ShiningCMusicApi.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets\msbuild.build.ShiningCMusicApi.props
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets\msbuild.buildMultiTargeting.ShiningCMusicApi.props
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets\msbuild.buildTransitive.ShiningCMusicApi.props
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\staticwebassets.pack.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningC.F8DE0432.Up2Date
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\refint\ShiningCMusicApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ShiningCMusicApi.genruntimeconfig.cache
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\obj\Release\net8.0\ref\ShiningCMusicApi.dll
