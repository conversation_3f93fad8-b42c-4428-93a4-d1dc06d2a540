namespace ShiningCMusicCommon.Models
{
    public class ScheduleEvent
    {
        public int Id { get; set; }
        public string Subject { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string? Description { get; set; }
        public string? Location { get; set; }
        public bool IsAllDay { get; set; } = false;
        public string? RecurrenceRule { get; set; }
        public int? RecurrenceID { get; set; }
        public string? RecurrenceException { get; set; }
        
        // Additional properties for lesson management
        public int TutorId { get; set; }
        public int StudentId { get; set; }
        public string? TutorName { get; set; }
        public string? StudentName { get; set; }
        
        // Convert from Lesson to ScheduleEvent
        public static ScheduleEvent FromLesson(Lesson lesson)
        {
            return new ScheduleEvent
            {
                Id = lesson.LessonId,
                Subject = lesson.Subject,
                StartTime = lesson.StartTime,
                EndTime = lesson.EndTime,
                Description = lesson.Description,
                Location = lesson.Location,
                RecurrenceRule = lesson.RecurrenceRule,
                TutorId = lesson.TutorId,
                StudentId = lesson.StudentId,
                TutorName = lesson.Tutor?.TutorName,
                StudentName = lesson.Student?.StudentName
            };
        }
        
        // Convert from ScheduleEvent to Lesson
        public Lesson ToLesson()
        {
            return new Lesson
            {
                LessonId = Id,
                Subject = Subject,
                StartTime = StartTime,
                EndTime = EndTime,
                Description = Description,
                Location = Location,
                RecurrenceRule = RecurrenceRule,
                TutorId = TutorId,
                StudentId = StudentId,
                IsRecurring = !string.IsNullOrEmpty(RecurrenceRule)
            };
        }
    }
}
