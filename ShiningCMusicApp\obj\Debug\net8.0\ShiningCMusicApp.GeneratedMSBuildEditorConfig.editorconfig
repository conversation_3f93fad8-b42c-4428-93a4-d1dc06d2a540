is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = browser
build_property.RootNamespace = ShiningCMusicApp
build_property.RootNamespace = ShiningCMusicApp
build_property.ProjectDir = C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = false
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApp
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[C:/DevOps/repos/ShiningCMusicApp/ShiningCMusicApp/App.razor]
build_metadata.AdditionalFiles.TargetPath = QXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/DevOps/repos/ShiningCMusicApp/ShiningCMusicApp/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSG9tZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/DevOps/repos/ShiningCMusicApp/ShiningCMusicApp/Pages/LessonScheduler.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTGVzc29uU2NoZWR1bGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/DevOps/repos/ShiningCMusicApp/ShiningCMusicApp/Pages/Scheduler.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2NoZWR1bGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/DevOps/repos/ShiningCMusicApp/ShiningCMusicApp/Pages/Weather.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2VhdGhlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/DevOps/repos/ShiningCMusicApp/ShiningCMusicApp/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/DevOps/repos/ShiningCMusicApp/ShiningCMusicApp/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = TGF5b3V0XE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-nulpykb2k4

[C:/DevOps/repos/ShiningCMusicApp/ShiningCMusicApp/Layout/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = TGF5b3V0XE5hdk1lbnUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-mpyuj6ixf1
