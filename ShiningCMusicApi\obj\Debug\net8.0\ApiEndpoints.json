[{"ContainingType": "ShiningCMusicApi.Controllers.LessonsController", "Method": "GetLessons", "RelativePath": "api/Lessons", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ShiningCMusicCommon.Models.ScheduleEvent, ShiningCMusicCommon, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShiningCMusicApi.Controllers.LessonsController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Lessons", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "scheduleEvent", "Type": "ShiningCMusicCommon.Models.ScheduleEvent", "IsRequired": true}], "ReturnTypes": [{"Type": "ShiningCMusicCommon.Models.ScheduleEvent", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShiningCMusicApi.Controllers.LessonsController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Lessons/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ShiningCMusicCommon.Models.ScheduleEvent", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShiningCMusicApi.Controllers.LessonsController", "Method": "Update<PERSON><PERSON><PERSON>", "RelativePath": "api/Lessons/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "scheduleEvent", "Type": "ShiningCMusicCommon.Models.ScheduleEvent", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ShiningCMusicApi.Controllers.LessonsController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Lessons/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ShiningCMusicApi.Controllers.StudentsController", "Method": "GetStudents", "RelativePath": "api/Students", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ShiningCMusicCommon.Models.Student, ShiningCMusicCommon, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShiningCMusicApi.Controllers.TutorsController", "Method": "GetTutors", "RelativePath": "api/Tutors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ShiningCMusicCommon.Models.Tutor, ShiningCMusicCommon, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShiningCMusicApi.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ShiningCMusicApi.WeatherForecast, ShiningCMusicApi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]