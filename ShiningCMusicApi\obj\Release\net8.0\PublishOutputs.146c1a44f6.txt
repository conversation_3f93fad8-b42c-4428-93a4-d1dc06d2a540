C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\ShiningCMusicApi.exe
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\appsettings.Development.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\appsettings.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\ShiningCMusicApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\ShiningCMusicApi.deps.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\ShiningCMusicApi.runtimeconfig.json
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\ShiningCMusicApi.pdb
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Dapper.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\IdentityModel.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\IdentityModel.AspNetCore.OAuth2Introspection.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\IdentityServer4.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\IdentityServer4.AccessTokenValidation.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\IdentityServer4.Storage.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Microsoft.AspNetCore.Authentication.OpenIdConnect.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Microsoft.IdentityModel.JsonWebTokens.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Microsoft.IdentityModel.Logging.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Microsoft.IdentityModel.Protocols.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Microsoft.IdentityModel.Tokens.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Microsoft.OpenApi.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Newtonsoft.Json.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Swashbuckle.AspNetCore.Swagger.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\System.IdentityModel.Tokens.Jwt.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\runtimes\win-arm64\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\runtimes\win-x64\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\runtimes\win-x86\native\sni.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\ShiningCMusicCommon.dll
C:\DevOps\repos\ShiningCMusicApp\ShiningCMusicApi\bin\Release\net8.0\publish\ShiningCMusicCommon.pdb
