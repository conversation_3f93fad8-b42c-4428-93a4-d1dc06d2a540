{"openapi": "3.0.1", "info": {"title": "ShiningCMusicApi", "version": "1.0"}, "paths": {"/api/Lessons": {"get": {"tags": ["Lessons"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleEvent"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleEvent"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleEvent"}}}}}}}, "post": {"tags": ["Lessons"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}}}}}}, "/api/Lessons/{id}": {"get": {"tags": ["Lessons"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}}}}}, "put": {"tags": ["Lessons"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ScheduleEvent"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Lessons"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Students": {"get": {"tags": ["Students"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Student"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Student"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Student"}}}}}}}}, "/api/Tutors": {"get": {"tags": ["Tutors"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tutor"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tutor"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tutor"}}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"Lesson": {"required": ["endTime", "startTime", "studentId", "subject", "tutorId"], "type": "object", "properties": {"lessonId": {"type": "integer", "format": "int32"}, "subject": {"maxLength": 200, "minLength": 0, "type": "string"}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "tutorId": {"type": "integer", "format": "int32"}, "studentId": {"type": "integer", "format": "int32"}, "location": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "isRecurring": {"type": "boolean"}, "recurrenceRule": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "createdUTC": {"type": "string", "format": "date-time"}, "updatedUTC": {"type": "string", "format": "date-time", "nullable": true}, "isArchived": {"type": "boolean"}, "tutor": {"$ref": "#/components/schemas/Tutor"}, "student": {"$ref": "#/components/schemas/Student"}}, "additionalProperties": false}, "ScheduleEvent": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "subject": {"type": "string", "nullable": true}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "isAllDay": {"type": "boolean"}, "recurrenceRule": {"type": "string", "nullable": true}, "recurrenceID": {"type": "integer", "format": "int32", "nullable": true}, "recurrenceException": {"type": "string", "nullable": true}, "tutorId": {"type": "integer", "format": "int32"}, "studentId": {"type": "integer", "format": "int32"}, "tutorName": {"type": "string", "nullable": true}, "studentName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Student": {"type": "object", "properties": {"studentId": {"type": "integer", "format": "int32"}, "studentName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "email": {"maxLength": 250, "minLength": 0, "type": "string", "nullable": true}, "createdUTC": {"type": "string", "format": "date-time"}, "updatedUTC": {"type": "string", "format": "date-time", "nullable": true}, "isArchived": {"type": "boolean"}, "tutorID": {"type": "integer", "format": "int32", "nullable": true}, "tutor": {"$ref": "#/components/schemas/Tutor"}, "lessons": {"type": "array", "items": {"$ref": "#/components/schemas/Lesson"}, "nullable": true}}, "additionalProperties": false}, "Tutor": {"type": "object", "properties": {"tutorId": {"type": "integer", "format": "int32"}, "tutorName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "email": {"maxLength": 250, "minLength": 0, "type": "string", "nullable": true}, "createdUTC": {"type": "string", "format": "date-time"}, "updatedUTC": {"type": "string", "format": "date-time", "nullable": true}, "isArchived": {"type": "boolean"}, "loginName": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "password": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "students": {"type": "array", "items": {"$ref": "#/components/schemas/Student"}, "nullable": true}, "lessons": {"type": "array", "items": {"$ref": "#/components/schemas/Lesson"}, "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}