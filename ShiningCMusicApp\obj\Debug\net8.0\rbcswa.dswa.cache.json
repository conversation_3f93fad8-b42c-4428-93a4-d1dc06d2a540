{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["z7ok2jU/il/hW3ta1tl1qaMMxKNAebqLrZJt/TeOkt8=", "fAxl8J/rEAZvKq+xl6GnmnRhblKocyvpsUOhkPuSrkA=", "KCpXNksQM/a2+9GaT1AoSbKFpr7I03vFDqwMjdW/BkU=", "aWsWQ5U76TiUYRp3wVjZuBStdLYOxWfJSkge08BaaIk=", "YGdgjhT1Y9EbvFR8YCEI961nHJiBE7kkScpoABZf4hI=", "auRLvOhHl/vLQdvWC0kR71KyG0l7kZ03z6iXGCT188g=", "EpnPgH7GguhEyLpBcRCW1lJ27Tgzk2p4CTEUhwRZi/I=", "zhWQXabEkOH2z9LDSNg+DR56/knKAbOhHis1YS8hTCw=", "6ARM0gyJvUEx95cR4Ew6XImSoqI9urKMPUxUH2Erio8=", "bqYd0xYG6GmAyEm2x+CicbTZsMUzeo6c1UtFZggTllY=", "YklPTpCsJL7LB6ja0cj+RS//e9SASQmh/m+O/tmho0k=", "473ue3f/7tNfq93v9LFh01pJ9lYT/a8RvdX7eFZFcGA=", "EE4UREa418UKdXlBoebhGv+d2fcdWRlKWZ1cCydXyQ8=", "YI1RJvLXfj//g+HhuhJNTWDVoy1hrsl8xfbXeM5DxNY=", "AhS4H49XWftGhsMHD0RCx7j3399AS4GBQt5q+JU+B+s=", "7QgMsQk9+ueOcNDS2/06696nCt1rpsqR1APIdg2LNOw=", "fYg+TRx6dZUiLR7eY5IaD0VY+trznXjsN2mE2Y3sjFg=", "P+4oBaUWOWcaFFHOppiSkhPO13BVWUGwfqJjjLb1l58=", "ci7f71JFfw2FtKHkz3350lR9bot08llFb6vVnM9jK78=", "6udg+/t32bypmkwGSRE+1GTjrhbmv99IzBx5X4Im8FQ=", "bAF+skFj13cHpM27wCRIhSkwo0KlBhhoszPPZ6LSaU8=", "wKJuY7Iy0BrfQgORvOubtBDL3T/9TaK31WmNSsp+OxA=", "qytbjjgTBcfUDRmMhJDkXvv9d08E83VhaPHAC9of0VI=", "qEDWQiM0qm6MByaLTJa3uiTkLUpM+E8LYZqRrwsxKxI=", "Rmffdx/ezXadGK614EpfPydNGJGbaIQcS49OMZF0Z2k=", "TAIU/Bn5rUVAoVVgIt8htIE5VreWL68B03p7SgZQiSI=", "TwgF76X1cj+RTCL9nrXGw82QS2az9Um/XWBS5jM62dw=", "drbo1hAHGooSMdWsF0A0D4SSjXpcoA+eZhgWlJKIHnM=", "/cCZ/nCoTbSWKjpWEUUIq0H8VI977pKoDX9aFWkcdnk=", "JCHY7YyNNxcIEsefr7aPx41S8nDl1KJBKXCNJWpo+io=", "8fNPLXCzsnFipAh1m+SyhalhwBMKB8ZaHsIg8gXULaM=", "Mjo8QSyCP1F+98wholBC/kItzOSpWcKmpp5igIXaTMs=", "auPJSzT3hBIAUmMSAGYmtukOpR8Nihks7moPS663tDA=", "jn4XwOUkhDTUoTloGu08HOZ67hxQbmXzeq3PBi3PEJc=", "7X3OOIYkqB9sQVBkKjOfL85DG7RJ6/snuYSCmvLxUOk=", "ipwH7Tl1Yp1WpOB2oTgkQiAKz5dXy7wSU1ultB496Go=", "vhag1S08cYjDB3QlEIozduFTNbeK1mFK7dhKtTKDl84=", "cx4y/yE5emX75gMmlQguqnZXZwrhc+UKpWAOwsPIuRM=", "y4aycrdu6LAsekDLcoMt5ynrOuSE4kyMt1DTilBwx28=", "qxDyyv8NpsWoKXVngi1bE9tQHfikec3IgXZFktNU+uw=", "Kjou67evQf+def08G2JmQQVkS2hjiey9FqRrB99nIbo=", "N9mOqVNh88WsBDTYx0oTvwhZaL5sZJa23XxIfdTEVe0=", "Hjh1W3npLPABtUMpucgKtWhhuTu+Twf9NZmy1lcsM8U=", "FJbQ702JImvqT+1/DNUOhRxkE1koP8lfG2DKeqPqANg=", "yumDMMGRj98qUldMIzvMsof8WXyZlj+2n4YArIvwAV4=", "r80d4Xcexm5iqPrgyrTaniMmd9ypFQxo/ACYHsoN8SE=", "zRHAT60ILtkUB7em9xRM6KELaZftI67j9qK7eeIOfmg=", "ktLVnJfWtRkNdXvkKyK3S1WwqMedVzHyV7H7EdQtoT4=", "fdsXuXNxGcQrlVE3/r5df8MikLD5OnqhPt0rDnW2kS8=", "Z03CtFKz/zeyQ9X/LVfbo9az7V5jsokepBVj6NPNST4=", "WOT6BJosz+1HWqH8fykpWW3SAvZWyGTlAvgPDPavzBA=", "uv/yGSOZrwDkzIq5CgjtkGfZupOD32PvhJ+yDHPUYtY=", "LjlZR8EJU1GtAH+rau2mcLGVQTjf2UoeddSEIR3+NCI=", "QYNPDWn4/NcQI/fUfmU+lydth6PweAU2iPX550a7KIg=", "tQrOlCEFreSFhlIhe/GfcOnuhJgyXctPV8BHgo9yjTA=", "VKJnO+yuvZ0GR4uh4s6LlAAUqHVsr6R/B4vFXGYM4U8=", "N/kQkBiDqK/+EGrcmeUho6ftJoHAoIrJaQY7Wz9suho=", "BktflUAd3bn32q6hVVC9y5jd2T58FgVBKETVmRi5Ctw=", "TyACglhBG29YJobYlpLsPXXxcADgGZvWAnZKZbi6T7g=", "0UnUC2Ffs8m9QpVaQXTQZGnpAbSRiQOfjWgstpm6WKc=", "1Q7aEQTeLdJXkqYopFJdugkhdAasG0AVQU5vbxRfLU0=", "AroOIAyQVmmbO96kmgUzmsaoSbkpnyfZUYc02jK55Ew=", "B/ofvFBa9Lmym9k+P93Ahv1V1Vxqeg0p3EEec1uwrGo=", "aJfsFa/ghrcK9Uxgb3nzQwSwX88TWe3ZJPCfw7c7qH8=", "WxSk2K2xNig1qz+02tfcPTp9B0IXqwLYtLdJKK0SToA=", "aB/Ras/CVM7S4R/erjoPt5Mk5lDLdPFSkz6u0jLRNRg=", "xUROYaTniM9xYgWWECc9P9eFB/1iUEQBX3xeib2pWOc=", "kyp30t8z5ahSWl144Z3N6ykqo1L2S3TdL5tCKTprgcw=", "6M2NnqnAyBHCochYWcg00f8TOzh+b5EirfmPjQVFLX0=", "o6q1pL7no9Vj7+S/nz0zoVyXOzOWoj79vIt0vK5LqZc=", "7A+AwaQmm2uh+02gUxGCbOmVUD4qMmyOgmwraYa46H4=", "ReLE57KKyUWX0Vd+hEK19dEXgymrqlaCyWlxMcCc5As=", "UTw3yoVEnDR0exZ3k0sPlb+AhLk8tuvT0OW5ahQqG88=", "YD+Slvj5fhzxTOfzNISiYTcJe1Tiw801TWgKbIgGjKI=", "qPaeK58Hu3mudHk021lv7xYuAXqjuT+AC5N0zYlrVAQ=", "AlZHFweGIv7NgRtrvd1xDn+JOTsd4EnTiWtivOMR23A=", "w2uf7p4S2XRhuNrx2C9LYVT22JqjbApC675YCpa4SWk=", "YZfYmdOV2Vv4bpG216lVSISYN5Jt7IArKqdkGndSRMo=", "NGw4I8bR4NUdxLviy0rknCq3BKOLV1osGIo5nP4vdeg=", "7+kTA7Tnt7T67eNtyvuV53lkhe25v03vPrlJoyXylDA=", "lY+pmWxc/P2KGRpBahgSRV7Dgqy3AONV0XmphBpKvvc=", "HFMHZj0Epvu3cPP7QU0r0xHrfPCfOCm0BScUT3zjZtQ=", "8dAYVoIdpybykda60tbR5omMvz6vlZrvhN/nRNmlvK0=", "Ox8S7wtZ0DTbNiSrdA2F2eVc16DbLz3sA5yuLinpeC4=", "aGvOPs4E2Tn1uU9n57LYPUBvWgor/HGwerLMOAeIfPU=", "xWKIhDra2K+2L5zCRHSig5N0zdshMSfqXxXOUTlWGNI=", "XPpwzzrw2iUum15AZ8ogNI26QPO349C5ZD7u5MPkvxc=", "I7CFumsTl3Zuzo/Y7OTNxMEcHQXpKhSUI+UF1GGB/GE=", "kUPdejeIRkqk/24UZbYAXproVz+oDGYadLBf2fxQNr8=", "RK/xYWRwb6tGwTnPY1hp4HFOIyrlaDNAnWgBmjwP6f4=", "kSD2UOG7Tk6ko4sezvfiXbrPeMMJ4ATpQUuXeO/eGao=", "NJby657lxrzOJeVpI26kbMdSe4z/bPOq8httg+he3qE=", "eihIRXU9zYr5vdyANG1LYJYrXEuebWOzYf5LuDWLUkc=", "AiUJZxucxf+fBGoz3ronpQ0+X19EIOQUDWVBVEP84OA=", "dTY8I4kTWKmoSLMBoNgxtVwTiifaSVfZjPhyj2S8m7g=", "/LYqiupJK9zI+Z0ieXwk4/UeDbMfmHNP3CwumXxibW8=", "sScsg6YezgO5RuP4Ji9Hy9lrONS527q82/4FDq9G6q4=", "+mJIO+HjzkKXDFGEFArqc5HT5brkcRAh1c6jaKaXCUA=", "DnHDjifII5ljTj+xsF5OrefRuHJuwUJVDhc+B32wes4=", "hezzLgXmXk+RRtIYYOxO5tA9b/qU9MtpLPe/OO2Iy5o=", "05a0XM5Vo8HZqwlJVlTNNasETvNW2Mub+LW2AXDoEz4=", "RlbvuZnoVECV3SPiN316vwnMObZAQSww12alZrIqJkQ=", "NYx+cS/LOMUrvqCLyZUDZCE2ChOKADCh9V6cxT8NgBA=", "uSaa1DkYWOF7k5grRPuy/aIUsx3TbojPiyriFqjiHBU=", "bVJ6brfkG6g2f9OKs5A6kmIHRmtJ40oRjjDwyAIvMu8=", "8qr6tBSncOH1UwqGEveBQa4YQY077gz5kByb+/H07Zs=", "4BBtvSOcIBUw1ES3KE2V/9P9xRo9gdXjIaCmUicZHpc=", "nlWYpdvnSfXb18LI9md9Q50p76JyBDmXLran4+CteKU=", "ndGQmKAiI1YhA/wliSgoGOk0H1Ekrnz3w47+PEzvNk4=", "Xe3DpRVIfH6ORbpkENxq2+xceeGa+UGvZjS8ouqRqSM=", "dO48iRI0iZ1W7p1suu1rzkCQoaAk7K/JIvENKGUTuns=", "AkQL4ew35LL93T8ehKd5pSq57uQO2LtLTsGxHQNEKN4=", "W7Z+XkoMMCDh4LYHhRXd8lC7xUQrG/qoNBmY0V0/DsQ=", "ftNyZcgYl3L1oaoW19LqVLMMp+nzqkyHHXX02ZRcOuo=", "LR3DSRSv2KfCkOWcFNo3dB63N9W9H47y8Kwjc49IlWU=", "HSe9+FKjgNvsc9E/xxec6vrSdNxtnH5b/jBJmvuD9Pk=", "/UY5Dmk3GhxNIIvUDWqvBFMmW0ith1i3ER2O47Uchtc=", "xpKBiLE+N8kCBHtsU9OOjdHAfFMULHh6KWvuIpXjXaI=", "Q7KHNAA3Dk4hsTARXy8Rw7uGY7YlUZxMbOHPlqAaVp4=", "H01JMKzsHf3LX9sfyHRjL6pxs/PTothCal99jk7fgX4=", "iqJYABJXxWnxV1l9loBe4mhfHr+GI6LDfcOkZ49jt54=", "NkSyjIQ1esrOxqhkDbmP5oYXDgAoNs16jB9ThgXYxrQ=", "1NHvjzl2JuiihZHz+eSy3MHP28iegwlkH9vXDf5TbE8=", "b+4VCj5/ifWFGBk6LR7xtqj651pD/KcFTSIRTO8IAMY=", "NFRsEIgzy5GaEFjEQR+Zmwq3zzdbMt6vx7PyjOZxL68=", "yIswn6/nDokgDX0kpa/OHkBfv2PLSn26d2zE+KxCjTg=", "Lp9g8bTsoz+e9LiPQY2rhXjwhnd3O3eBHbh4puX47K0=", "aWgYQlNXkDZ1+Tt0iFNwKyU26EJarsgqirAUtD9MQM4=", "4fyoRgD6gcEEWszcHYNBNPkUD4krZrn0UNY7T9lDFn4=", "yUEl5lgVzIcsi+TlDYZdV/dnX75lqVXCOy/5ikwbh9U=", "Dbyn0SbLIgSiBISpduWekUgpkvq+gJGR3JWBurg93R8=", "ah8177tehv+iTA/z+WX1bxfR5fBRmQ4evER4z57svfA=", "425DqBmB9CJENBLNOc2BJKw5EtMeXslKEG2UhIOK+Bc=", "fAy5GgU05xqd0/pftodjW/8QCVZbpoYHsPy2C1gHnnQ=", "fKTgR/HmWWtpGYQLtaW6k74ChtqICkhi/7zcE6M0m8w=", "pUO0H1fI2I2UewPk4nlbqxG1zHJVW9+y+w814Omn5Ao=", "k0WH0XHVuEptRMQLn1+r03h9zArcC9595W2sRQkv+yc=", "Qe81+AoRsLgmOdkBaxQl/ZOAszyuEPkfTBZ6qazMQuc=", "4zmrLwZyExbI9qgJ68F9+19IYTgkp2oliMZbZX0tVqI=", "7FytEwWtHJRpVSC6ImB1iH+pyB05+znR9B2Ba/mYk50=", "1f/+Y1yBo4DTdcCzYExA8WOhA2klSHscoZLzUlXp79U=", "J+uR+RYYxSXc9Hin18JqhOF69bAHW0Z2xWQTdHAaSrE=", "7tqypJ3UKHBz+82o8IxyHb7YOyS7/I5SnjkX3r44IFg=", "bXQ9lX1BBpTgRSnXOz8l2W1p1xWPvND6I/YxDkl5Z6w=", "pukWX+iNtZt0+JoOI+0lkB8/lrfF0EMZPf4yH4CCpqQ=", "OjUZKmw5QDHXk2IOdVmo1GOtyx+O4raNHhisHOBUsnE=", "feGuY2aQZJZV8gdIx/dug5a776eIFcQPLtYM0g+peEc=", "wp7FVSQcGMx9xJa2BqsIaVFjqWKXSJfzEupXmaT9tmw=", "PopOJOeBpslrmpCbih0QWaQ+oytIYM6L9qKsXcV8XnQ=", "ZTtkYzmopVayl2bREzSrQcT6eOlo7ldhtutbuGm7w2k=", "C2s2k2eGnq+jevnpovLpujZnX01EN9zU3kvc5a5ef8E=", "q7tBLvIqOpedM3EAcwjcUNl9rNRv6qA7wObnircsfmM=", "/tCEoH2/WyAFEU7J86szZRVFlJiXF36TcF6R8mdmdFQ=", "Sdjt33u/FgxMh6bG7fEb/2oFONCgt2V95DSqFlwTcPs=", "cBTP4vxYigy0+KdiRPw/ZBBA+cxGOcKN/nwPZVGmkcQ=", "Pcubr6ugr59Bwo7m9sAsW0F1835P51gPoFo1PFP9tco=", "aucGMU6PaKytfyIv6tI054PG6JiNE2SC6nBBU8tYJUA=", "wcJSOgMexzIO2p3cEUwrBSWt5BB50YpbKLiFatIna2U=", "ZEOyk+WocLAY+ALp2zT/9g5OctzWSliX7dDs2QfzJR8=", "+gJetkjeBbdrOvE/GGA23QgLXivjnPwO7ogqHCn8a2E=", "GMYEov42m4ML9TbyVqXOiBumO7jOuJMV5SfygDDwtHg=", "aXpwCCuom2HdctiRNPOGM0Lg2tuQQJWRHLLcjzAtjNM=", "s0MercjK2OaTZohK8THGAbmpcNZ0fvQ3hyccE/moGBA=", "lo+tIZwF0zSRaRIPHgXcGnCCh6LPE4BJMiwtCfSOO8g=", "p0k0g2Q1y2OQwuQnMHBN6Q6Om2FovNDmqLRFhKWhLCo=", "sRcYQnTSjBR0VttIwYsxYLghZrx0eheA2PD6Q1l+t+c=", "ytZrC8gswKhMbP1/G7iZLBiiEg9ReDPsQIPWYH0nsbY=", "aJKj5+J21HA14+o9Qd3POgZW/KdS/fge49Far7ak13g=", "HKZRECMxoFUrAklkPKfzvJsxD0wVj0N/y6mye5x4R+A=", "x88viLp6IzGZJUVuW3vZgjRnUucXvaio+o5EY4VYyGA=", "u5Xwn66oGcQhSXGScfDw4oRz4EWcQcVYbFrAe6DSSR0=", "7OwDry/0YjDaWAZ+KY+KyB6pUxr6hVWgWbHebqWc3H8=", "UzPI9Hso7M3x8ikfzOuqd6WqrYv41AVk7t0OC8xExYk=", "JHu6KpfTFYZp+dXo3tjuL1qBe5RqT7jzULyaHbLIzr8=", "OrPGvNBED9xAAI1VpJqCaip1AcA3CNMO8B79abV3BXc=", "sbyeFXXQJ2NwiwKsNbreKPapMz70aQUMR98kRDx2MhU=", "cfuuQpJTXasRJsjRuavrT2TRnsMF8MYk7bZnIHu03Ek=", "kWqO/CVRlIm/yp7KPn90Cn+XwTqjSvnKpteakZNaus0=", "WyVAA5WLVx/9CxQM1Qdxs6/ta255IjrAumOVnN5aP+g=", "QwsFRLE5y0nieI2aevAUV6OFR2zjJvD/wv7Ypqs8V2g=", "NFkF/34YJZ/vC7CaMbudFHUDEWs5BZbWKYQ7EnGbsrA=", "rYSeeV03nK/Nalrna60JEURUrxIV2c0Du/9Z5uEt9Fc=", "wtWIh9ECcwpgUBDst4lJm09s3TOPvYjpEQYALZlUBBE=", "053SXILX4WxztviT3We0LBDrwQqXdCj6npDlDrBDjvo=", "pbp/812aFqJ9rdN2NB+1IHddRWKPrUBNEb0Cj8/U6xI=", "mFwGmu5r66CnVasoSEpeYSN4e1MXEoZ7ImQMck7XlII=", "De3Z2ztXeL2xoc4ITKbCbw15QY9ZxUfP2KcWbW/b71I=", "St1AQy3gvXbGkZrPyTVHzMJpmvkzSSsLofkF6eXsTlg=", "lbHaKt38II09qOwhjTbkU41n1VcS7wHbidYQ3k01czk=", "bV5dkEV7/0jF5rhCR1VXBFVmUB05Jmp8y4cRttSEnYE=", "DGU061tMmXyPpuh5etcX0V6hOdcAvyCcvFxN4ECsmzM=", "SJJQPDt0Oj+kdMPAIqEL2FJOqdUv+Ya1wdAtzBLDj1E=", "Y/+CbZJV1k2rCVNSgLW0CwxlY/595MiDEAHPfvK8tiw=", "dY7JPZ/vkp3632vPUtITrX6i6ITwYScegSCJdcP/gR4=", "kIhX7bQyrtiXRMiv27U3Opf/RHBVLbeomeEkG4XCIdE=", "PibDAGcm3mbbbaRIBhhjnojWJY0EdKFRaPXnTk+H2rs=", "HUg8py4aiU5GoFMMzk3Z68OWPffYw4H+hcTHFxy5YL8=", "/5f4CpxgSwKNQx6rJ22FMywbga1uheVWiUyjCkFC1EQ=", "fUTpqoJQFzER0jp8H13zjA7H3dBkdliar3R0e171RJk=", "yHR8G8TjEYSa1a9Rjn0hjj6Cg/J1g1pRWZmuv5WDVH8=", "c7yMwHB3gH2qJn8fMiUDXuP/Ncx7n3R7+2oCMkLdLq0=", "tiokPl5QGFuqyCAkOvj8K3Sx7b5LReKaTcnFYHWBr+A=", "tocjg+snXltj3gE4mIXGCqnRmNVbbi5sJAvXe8F6ITI=", "vzTG5ykOv61n8exa8Gyj+br1aHj9WcOM52/0vPRmrB0=", "BPEjx+pNdJpPCXvOekLnDQBzHzFp3FGHuDhCL3HHUtU=", "SYoR6lXnIcAkXXJ+RJPbo8Its3LUZiSkgklNdlSE7nc=", "2RNLhXvHkC5taK99AwwCT2S9TP/pXDBqUtPQrE8uEvw=", "Jei0LnpoQ8Ffxqlo8XnhRoyNcGVjvg9dVQzY8Y5ezkA=", "nwLiNKK++xt5eQvNO3vtsbv0GYHbLRgbHvE/rN3oFSc=", "TEDqP8majLLxY6J6hqZeshH8bh70rfuogJXJ68iIb0g=", "8FtL0QdIZ9S8nmijW6XPp/yboIVRz/dWvs/5A1trEro=", "/nrYAxEZ3tQMjS6MuRYqX4JiM8aEA6P1/4Mx/2rMj2o=", "Ij9gqa0jISepB+wSnCU45ONZomyVQnkGq2irpzV4R0g=", "8RCFldU85T7jZtlW4l11OeEl3NVs1NalalHwSieyets=", "P5B7+BrLqH0BVWy5dA4o4yiqEHiKKUGeCt1rkbOQGLM=", "V/i7hAK1//gmNDdPbtRNApYyaRNCrRDJQgh2ocD6FlI=", "8A8XPaKFli6lGRUYoi38AAoM8H0P8n1/1LUMmN4ke8s=", "hd/xqVA9S9+SW1vxQBq1BNFg/pgUJnnalnbzrtp3mqE=", "cpyrlFp3844a5qnOb9j6SG93rVpYgeerg6GuuZn963k=", "zOOUriQvx7AvJK8DyDxzlazeZrmS+yhgxAVYrB9M78M=", "IOnRwQmcE/woY6eaWBqy/l2i6lLY/EpA+5H9ZAEL+OY=", "88hcH8qVw7+iZyjsn8iY5M1mscSgW0Me2aGH8Rat0eY=", "xX8Z6DB39lcT0EfIirIdjRzBYbNDG1Ghtg2ntui7P5c="], "CachedAssets": {"xX8Z6DB39lcT0EfIirIdjRzBYbNDG1Ghtg2ntui7P5c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tga4xb2zqw-gopl42lcd3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "521l974bz4", "Integrity": "Y+JjcJWMJDDpwxbyvhWLZNDDg/mLoy/SnOR3yNPjl3k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 9455, "LastWriteTime": "2025-06-24T04:57:08.1231614+00:00"}, "88hcH8qVw7+iZyjsn8iY5M1mscSgW0Me2aGH8Rat0eY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bqkz6kxeo8-ei3lux3y6n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicApp.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2w1vzga3ac", "Integrity": "Ur68y6nScNVUOwWOqJn8Vs9tfEOOb1X3alL9/fCoh1E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.pdb", "FileLength": 33752, "LastWriteTime": "2025-06-24T04:56:56.2086158+00:00"}, "IOnRwQmcE/woY6eaWBqy/l2i6lLY/EpA+5H9ZAEL+OY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xvf58668z8-o829nqq0cj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicApp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hppgtp3egf", "Integrity": "2yiyzQJ5aOH2Is2wcrILz7ZitNNaYHUxaS3txG8qwo0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.wasm", "FileLength": 30910, "LastWriteTime": "2025-06-24T04:56:56.1988955+00:00"}, "zOOUriQvx7AvJK8DyDxzlazeZrmS+yhgxAVYrB9M78M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\esi1du385w-96flqtntqw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicCommon.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnqoifgltv", "Integrity": "8cqzbyKePOd6sNGFznzysw/h7zUOhWf7WvY+zMVLWGg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.pdb", "FileLength": 7505, "LastWriteTime": "2025-06-24T04:48:42.5463822+00:00"}, "cpyrlFp3844a5qnOb9j6SG93rVpYgeerg6GuuZn963k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0qonx4ygs9-h8fxi2yw1v.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicCommon.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zmtw275peu", "Integrity": "9VMVjGWGXMyqx8e0eWdCmZFdFkTZXrr28xpi6RM+OKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.wasm", "FileLength": 5288, "LastWriteTime": "2025-06-24T04:48:42.4771852+00:00"}, "hd/xqVA9S9+SW1vxQBq1BNFg/pgUJnnalnbzrtp3mqE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\i4ozfjtw4i-oyz0vx2fzb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/emcc-props.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\emcc-props.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6rw5k6cf8e", "Integrity": "7t6AVk6lvrWEqY7hRavzlgS107PQ4doQEFxFK3dDtRQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\emcc-props.json", "FileLength": 592, "LastWriteTime": "2025-06-24T04:48:42.4482853+00:00"}, "8A8XPaKFli6lGRUYoi38AAoM8H0P8n1/1LUMmN4ke8s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\j19hqecu3k-lfu7j35m59.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v385ycndre", "Integrity": "S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 317618, "LastWriteTime": "2025-06-24T04:48:42.6378401+00:00"}, "V/i7hAK1//gmNDdPbtRNApYyaRNCrRDJQgh2ocD6FlI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iolod2it8d-tptq2av103.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnxfkgr4e8", "Integrity": "G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 196037, "LastWriteTime": "2025-06-24T04:48:44.0249403+00:00"}, "P5B7+BrLqH0BVWy5dA4o4yiqEHiKKUGeCt1rkbOQGLM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\90hp2p71hu-tjcz0u77k5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "su9h2nea1m", "Integrity": "JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 333110, "LastWriteTime": "2025-06-24T04:48:43.9532653+00:00"}, "8RCFldU85T7jZtlW4l11OeEl3NVs1NalalHwSieyets=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\v9ucdunf1s-nmlfvbueei.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilzgw5gy6t", "Integrity": "yhOL83W6Mgf0jJLwHzMZGA4NkEfwQoUJmCpJRya8LBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 100294, "LastWriteTime": "2025-06-24T04:48:43.8374257+00:00"}, "Ij9gqa0jISepB+wSnCU45ONZomyVQnkGq2irpzV4R0g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mgqvc88zci-ywccyuk0ea.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvhsn92mqz", "Integrity": "e9swblY/HDrM4H+jYQUqr8xKrdMNLra5T0+d7BOz+2c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 64973, "LastWriteTime": "2025-06-24T04:48:43.7804381+00:00"}, "/nrYAxEZ3tQMjS6MuRYqX4JiM8aEA6P1/4Mx/2rMj2o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\92k36dsjrc-61e7sdubtr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ahan8gc0vy", "Integrity": "zpxDddN4c/cNCws/EZSTYxW//AVvfrNfBVrupVC31kc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1154460, "LastWriteTime": "2025-06-24T04:48:43.7518392+00:00"}, "8FtL0QdIZ9S8nmijW6XPp/yboIVRz/dWvs/5A1trEro=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f2nmvc6iyo-vpgbraawsm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2hpdksa4ob", "Integrity": "NuanXv5ObHQUgmzahq06eVrXcG+WoMunixO8gmQKGMQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 36160, "LastWriteTime": "2025-06-24T04:48:44.1287808+00:00"}, "TEDqP8majLLxY6J6hqZeshH8bh70rfuogJXJ68iIb0g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mrmnme9fy7-3gypxjnyzv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hllvpd1438", "Integrity": "62MDqadOj+/GIGmhftV1peTz0tId0vCBCXCHa38OKb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 18726, "LastWriteTime": "2025-06-24T04:48:44.1023668+00:00"}, "nwLiNKK++xt5eQvNO3vtsbv0GYHbLRgbHvE/rN3oFSc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\12gy8c80sd-4rejzitfsn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wcqmrrwexh", "Integrity": "EApuGNk8AvmCwl07TLFJG222JaFOdNOcFoqhIUQeBzM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 11445, "LastWriteTime": "2025-06-24T04:48:44.0883619+00:00"}, "Jei0LnpoQ8Ffxqlo8XnhRoyNcGVjvg9dVQzY8Y5ezkA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0tq3ubgpl3-710onjtcga.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wu8<PERSON><PERSON><PERSON><PERSON>", "Integrity": "WCtnSrphKRoxTEOreGgMgEpNFHYkj/yE2QhdPZPtHmU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1394567, "LastWriteTime": "2025-06-24T04:48:44.0760025+00:00"}, "2RNLhXvHkC5taK99AwwCT2S9TP/pXDBqUtPQrE8uEvw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iqwuxofz82-u6wau0ktb7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b12xqtnz2p", "Integrity": "cVkmRyxFNqeMxwYJp1BTXbZkqgQXQz6TfXtHtPjyJkA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 25996, "LastWriteTime": "2025-06-24T04:48:43.4779325+00:00"}, "SYoR6lXnIcAkXXJ+RJPbo8Its3LUZiSkgklNdlSE7nc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xv769p21qu-epnagkv91l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4kcqoa988", "Integrity": "5IpB6j9TP4oWcspGDVI/FYwRJvK44Ca0Sglh/sypQz4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14744, "LastWriteTime": "2025-06-24T04:48:43.2863822+00:00"}, "BPEjx+pNdJpPCXvOekLnDQBzHzFp3FGHuDhCL3HHUtU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xw3xqrs1gn-u7plvjpova.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0t6qsm<PERSON><PERSON>", "Integrity": "9BjRdvYM2FEgBAfyV2JzkFm8EfyJfyI6Rpdt2w1ZHmg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2513, "LastWriteTime": "2025-06-24T04:48:43.2171289+00:00"}, "vzTG5ykOv61n8exa8Gyj+br1aHj9WcOM52/0vPRmrB0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7ycekvj0yv-4isf5pcuol.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wecjxff8x6", "Integrity": "aO2H3P36KeeCoaSb5fPLtgdR3F6Si+/dCry8yVAOyAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11741, "LastWriteTime": "2025-06-24T04:48:42.7165908+00:00"}, "tocjg+snXltj3gE4mIXGCqnRmNVbbi5sJAvXe8F6ITI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7qt3udra8g-ekfbtxon3o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mazk4w0jj", "Integrity": "f0TlS7a0kYBeQmpINNfz0Ay5ZCIVQoqSbfhAaFIHInY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4201, "LastWriteTime": "2025-06-24T04:48:42.6637656+00:00"}, "tiokPl5QGFuqyCAkOvj8K3Sx7b5LReKaTcnFYHWBr+A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dc9ab4adpy-tyrk4ywyu5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqur2gucz9", "Integrity": "azbHFvj0G2FOt+zpbeKcqhQJDzyG/31RNStqOIkWSHk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2843, "LastWriteTime": "2025-06-24T04:48:42.6368374+00:00"}, "c7yMwHB3gH2qJn8fMiUDXuP/Ncx7n3R7+2oCMkLdLq0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\uh9y7ps724-ngnkh62a7t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qipu9hrpsw", "Integrity": "sln492p7YGgUlXl75mNzNha0rQHV+hKkNEaF22Mv3Ro=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2348, "LastWriteTime": "2025-06-24T04:48:42.5830564+00:00"}, "yHR8G8TjEYSa1a9Rjn0hjj6Cg/J1g1pRWZmuv5WDVH8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tb1rghhwn6-uk4ktf4545.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkoq6vbpco", "Integrity": "7ZB8I+L31+JpElgTdmIsmbYiHU8Hq1NNWq3E2hpxfnQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2308, "LastWriteTime": "2025-06-24T04:48:42.5453849+00:00"}, "fUTpqoJQFzER0jp8H13zjA7H3dBkdliar3R0e171RJk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ddncr1chdl-5xzb5f7wvr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3q3icw7s5t", "Integrity": "W1+mdvDOSqtF5vDo7RbvpKKHcGBwhHhtZyebziHx+/U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2482, "LastWriteTime": "2025-06-24T04:48:42.7825168+00:00"}, "/5f4CpxgSwKNQx6rJ22FMywbga1uheVWiUyjCkFC1EQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3kjfjnpc9x-i6esjxgs2i.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8k<PERSON><PERSON><PERSON><PERSON>", "Integrity": "OP+3WHGSLQ714XTMeRxcZYvBQFKxupqkANRGFfci+hI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2385, "LastWriteTime": "2025-06-24T04:48:42.7320106+00:00"}, "HUg8py4aiU5GoFMMzk3Z68OWPffYw4H+hcTHFxy5YL8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gwugds5sw2-75yss9zhoy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u69fk6s3z8", "Integrity": "ujcxkCNExrbgBUktEcT8sIcBnXCAqDQpFYtDsYr/DkQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2228, "LastWriteTime": "2025-06-24T04:48:42.6212912+00:00"}, "PibDAGcm3mbbbaRIBhhjnojWJY0EdKFRaPXnTk+H2rs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bdjfymnqrw-3yhl940msb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sfnr940nm4", "Integrity": "aKii++1t3eIFbfJ8kxXNztrAmbDf5lCnEWFPG2OiWPE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4009, "LastWriteTime": "2025-06-24T04:48:42.5483831+00:00"}, "kIhX7bQyrtiXRMiv27U3Opf/RHBVLbeomeEkG4XCIdE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzgeww6nnf-8cl47m4jkh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yoebce8vme", "Integrity": "IuOAYSA7zT4MekVsU+fb6GR/9hV5tJSlCUI8cf4HB74=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2197, "LastWriteTime": "2025-06-24T04:48:42.5228542+00:00"}, "dY7JPZ/vkp3632vPUtITrX6i6ITwYScegSCJdcP/gR4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sadg8p8gec-nphd9f66p0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zzh8nxx6oi", "Integrity": "HzgJnoptUX805KkbZZpCJGQ1DPanHrSj/0of3xNCaYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2266, "LastWriteTime": "2025-06-24T04:48:42.4671085+00:00"}, "Y/+CbZJV1k2rCVNSgLW0CwxlY/595MiDEAHPfvK8tiw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cajl2o1e7z-fxengc3wyj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m0kte91qtt", "Integrity": "lix+/U0hL690/ReuuOA+NURVSh0IVSPJh0gK2WN+aj0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2111, "LastWriteTime": "2025-06-24T04:48:42.5383444+00:00"}, "SJJQPDt0Oj+kdMPAIqEL2FJOqdUv+Ya1wdAtzBLDj1E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9l7ueuc70-wj73nmpklo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3pvi7gcbsb", "Integrity": "CWjbsZdyuHfxzuNNN3TW5gU+J85Ei81pEUkRqOOaZjw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 8370, "LastWriteTime": "2025-06-24T04:48:42.4502097+00:00"}, "DGU061tMmXyPpuh5etcX0V6hOdcAvyCcvFxN4ECsmzM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\axs689n286-mylfx8o0as.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gb3ss7nrty", "Integrity": "zEfMTLs8gexhvPxrVpWgS4sJWpHVlQvdO3NhEX6Apdw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2168, "LastWriteTime": "2025-06-24T04:48:42.4407571+00:00"}, "bV5dkEV7/0jF5rhCR1VXBFVmUB05Jmp8y4cRttSEnYE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\e0p52hsb7u-tebc67toix.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7o4w1c655", "Integrity": "d6jaxiV4B/7ivYnqIJEugtxkYC0Ke2ySkqb530zduCc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2360, "LastWriteTime": "2025-06-24T04:48:42.4217232+00:00"}, "lbHaKt38II09qOwhjTbkU41n1VcS7wHbidYQ3k01czk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ym2sc23egi-3mr0k4ql8l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "07vh6x72bn", "Integrity": "LJISaxlzzsg2/QrGJ9sXmDlBOSkXiCgKIZYTyakiYxU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 51105, "LastWriteTime": "2025-06-24T04:48:43.5353022+00:00"}, "St1AQy3gvXbGkZrPyTVHzMJpmvkzSSsLofkF6eXsTlg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lx4q04c6vo-j0x2amkbr8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azdx7qdg1d", "Integrity": "+lvTsQQjzQqjIL0b1dDp2L70w9zLut+pm9Xgk05SGto=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14271, "LastWriteTime": "2025-06-24T04:48:43.4579949+00:00"}, "De3Z2ztXeL2xoc4ITKbCbw15QY9ZxUfP2KcWbW/b71I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hlv81p94us-tcb84raxfr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ir72gb1chy", "Integrity": "zr8zlBl43Vc05ZggODe9cCOPjq/dkWM58HYX7ofISKQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2122, "LastWriteTime": "2025-06-24T04:48:43.2924012+00:00"}, "mFwGmu5r66CnVasoSEpeYSN4e1MXEoZ7ImQMck7XlII=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4pab78fge1-03z9epkgeu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8685rjzp8e", "Integrity": "YLD6OG8G5k/hY1CmycBl2TwwBXlGeAf4pkos3ZLQ/xI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2249, "LastWriteTime": "2025-06-24T04:48:43.2470962+00:00"}, "pbp/812aFqJ9rdN2NB+1IHddRWKPrUBNEb0Cj8/U6xI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\x959zs3735-av82mpp65m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1wbw7xae8", "Integrity": "+KKEcm6UbopA25E3V1r0Hz9bpPELXokumhb07RvsNAg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2324, "LastWriteTime": "2025-06-24T04:48:43.2054877+00:00"}, "053SXILX4WxztviT3We0LBDrwQqXdCj6npDlDrBDjvo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ezx0izcezf-mzcae7sc1w.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zt10t3qa5g", "Integrity": "R1Ji6MJf1BoMC8mughzPcjuSJd2ofWbxqP3nMQIfnhE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2547, "LastWriteTime": "2025-06-24T04:48:43.4579949+00:00"}, "wtWIh9ECcwpgUBDst4lJm09s3TOPvYjpEQYALZlUBBE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8nxszxol5d-zchzosoc8t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u4udsecrro", "Integrity": "zu3s3rgiI/1CU8rvuvxPstyBDrDdSOOmmY5JscBhKIY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21575, "LastWriteTime": "2025-06-24T04:48:43.3034443+00:00"}, "rYSeeV03nK/Nalrna60JEURUrxIV2c0Du/9Z5uEt9Fc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\awerrgejpl-68n5xxnez7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o0e1d0n0id", "Integrity": "OJpuywD4IWo13VL/M7DVr0mfCvWbnrbURkle2C5SC9E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2285, "LastWriteTime": "2025-06-24T04:48:43.1988955+00:00"}, "NFkF/34YJZ/vC7CaMbudFHUDEWs5BZbWKYQ7EnGbsrA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n4x5zi1hy5-emm1p2vibe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1o27p37ksp", "Integrity": "v+EzzPbHp6+i/owi3b7X51qyihZHGtRtET6End+DclI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 73778, "LastWriteTime": "2025-06-24T04:48:43.0954967+00:00"}, "QwsFRLE5y0nieI2aevAUV6OFR2zjJvD/wv7Ypqs8V2g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\laazswwohk-609xepspin.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8j4xfz7b8", "Integrity": "b4ces5UKtHhTT4focWQsQQ4peWgUs91fjW2pXqwd5qw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2300, "LastWriteTime": "2025-06-24T04:48:43.6648416+00:00"}, "WyVAA5WLVx/9CxQM1Qdxs6/ta255IjrAumOVnN5aP+g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jef7gsrqq7-1qhdretbuh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hphvgmocym", "Integrity": "m+yGXO8YqCr6OeBaPgpxUw5KkhVZfiu/UFDLmIz62Lk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 19241, "LastWriteTime": "2025-06-24T04:48:43.6271841+00:00"}, "kWqO/CVRlIm/yp7KPn90Cn+XwTqjSvnKpteakZNaus0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ipm780nllg-l1kk2a4a0t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qd7nlb140s", "Integrity": "4il47+VZ7DWYRBb7ei/QcLUSSnRYRpW6GrgUevs64i0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 149516, "LastWriteTime": "2025-06-24T04:48:43.5413199+00:00"}, "cfuuQpJTXasRJsjRuavrT2TRnsMF8MYk7bZnIHu03Ek=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cn4p7z7pyl-c5gx1pj8rz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b1rbk16ps0", "Integrity": "YcohLbWIfGPVOKk2RB5lRX3xuPV9jkO8yA9rXZfUDBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 200887, "LastWriteTime": "2025-06-24T04:48:43.4189104+00:00"}, "sbyeFXXQJ2NwiwKsNbreKPapMz70aQUMR98kRDx2MhU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qdvfj0qa9d-akbtg6mu9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j4oh12472a", "Integrity": "nYIailQEVdO/2dsRaY1NdIhXFXX7uJcdj6Ucd719EiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23478, "LastWriteTime": "2025-06-24T04:48:43.2371514+00:00"}, "OrPGvNBED9xAAI1VpJqCaip1AcA3CNMO8B79abV3BXc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ppfdnigb7q-55ue31v3pl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00652p439q", "Integrity": "oPNYwXcRoWl7uya/HXXYKERu94fKm0CZJ8hFL0CQFZk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2228, "LastWriteTime": "2025-06-24T04:48:43.1413837+00:00"}, "JHu6KpfTFYZp+dXo3tjuL1qBe5RqT7jzULyaHbLIzr8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7x27t74nhm-1yccpcq7id.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zjhdfezxp8", "Integrity": "H3FtXAVgj7swGReRVg1moh35L+hmMgm+2VXR9Lkelvs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2255, "LastWriteTime": "2025-06-24T04:48:43.0752005+00:00"}, "UzPI9Hso7M3x8ikfzOuqd6WqrYv41AVk7t0OC8xExYk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f6wx6gd1gt-su7r23l034.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0rv36z1ex9", "Integrity": "hHojCn0xiRCwWyLrslxq6PClbL7a/Q2omlr8wJOxKcc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 514592, "LastWriteTime": "2025-06-24T04:48:43.0241371+00:00"}, "7OwDry/0YjDaWAZ+KY+KyB6pUxr6hVWgWbHebqWc3H8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\v8iorw1ao4-74uvd51634.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dgxvrqhl7c", "Integrity": "41GOXv3/61q6PAei8t3AYDrEns2K97f8ymoD3ZV1qyI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2295, "LastWriteTime": "2025-06-24T04:48:42.8002106+00:00"}, "u5Xwn66oGcQhSXGScfDw4oRz4EWcQcVYbFrAe6DSSR0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8801tn65l9-9c7ym8z6z0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxdot1nk1o", "Integrity": "f63be/hihAtZ17hZpOPi6kiNztHMkZyzgQOztyfKpvQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2529, "LastWriteTime": "2025-06-24T04:48:42.6659883+00:00"}, "x88viLp6IzGZJUVuW3vZgjRnUucXvaio+o5EY4VYyGA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xgis4ptx7x-dhjbngzvrh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c4pgkkmsgv", "Integrity": "LKdHfLw90xpicOQyO0W2P3QJlBkiuIVnXgB7U6/Nh8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2952, "LastWriteTime": "2025-06-24T04:48:42.6603122+00:00"}, "HKZRECMxoFUrAklkPKfzvJsxD0wVj0N/y6mye5x4R+A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ylntj2h544-kkdt47cvjv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xz07b795u7", "Integrity": "M8ityBtfy534MWphTysAcVhdDUpR9qBJU8TgCh6Pqmw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2184, "LastWriteTime": "2025-06-24T04:48:42.6543196+00:00"}, "aJKj5+J21HA14+o9Qd3POgZW/KdS/fge49Far7ak13g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\p1o86wnkwn-61u88luwrz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bolqu0i7zp", "Integrity": "xVHyKZDBFzSye02g1tUAgVfqXS4Q71WdcXJ7u5BfzII=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2151, "LastWriteTime": "2025-06-24T04:48:42.5238629+00:00"}, "ytZrC8gswKhMbP1/G7iZLBiiEg9ReDPsQIPWYH0nsbY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jyu1zp9qgf-nqh8s5tb94.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ax3au530m6", "Integrity": "XnRIHwY2UCoiFriXyUFvCdkZICFGHZEtMa9F8lvzbWw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 10532, "LastWriteTime": "2025-06-24T04:48:42.4595884+00:00"}, "sRcYQnTSjBR0VttIwYsxYLghZrx0eheA2PD6Q1l+t+c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\eh7hhep0d3-ac8qf4w6lx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n18lxojb0f", "Integrity": "+yyjpud2HwQ+OGPCdI/UqCTj+pb8244ITQWUvwfpbRw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 177338, "LastWriteTime": "2025-06-24T04:48:42.5955265+00:00"}, "p0k0g2Q1y2OQwuQnMHBN6Q6Om2FovNDmqLRFhKWhLCo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ced8wcd8cu-atf14r4yhw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7l50o3qupp", "Integrity": "S0DHA/lyKN91O8N20+vhKfUiDaVYpC1L1K5kilpR13c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2652, "LastWriteTime": "2025-06-24T04:48:42.4751853+00:00"}, "lo+tIZwF0zSRaRIPHgXcGnCCh6LPE4BJMiwtCfSOO8g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\il592650ow-1hrv129abp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tt4ixubui0", "Integrity": "8RH9fKYh+mRXmKintECdQg+YOjEP4UyJL72KP9CV1AA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2324, "LastWriteTime": "2025-06-24T04:48:42.4407571+00:00"}, "s0MercjK2OaTZohK8THGAbmpcNZ0fvQ3hyccE/moGBA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\arw8hu9x14-171oaphs01.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cscfdewx0f", "Integrity": "hKxOOBQ/fSCsL2LrAMqY/2sULobybRjt4W14A9awCE0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2197, "LastWriteTime": "2025-06-24T04:48:42.4202074+00:00"}, "aXpwCCuom2HdctiRNPOGM0Lg2tuQQJWRHLLcjzAtjNM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\plvxa4s6ga-3eb8c3pfwe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t50l0nov1q", "Integrity": "EfBffVPWpe58xruJwJsNlZ9rzwMqGZx5/IAZ2lmbNLo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2267, "LastWriteTime": "2025-06-24T04:48:43.5446232+00:00"}, "GMYEov42m4ML9TbyVqXOiBumO7jOuJMV5SfygDDwtHg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\g8d1ll62re-6ei8na5yhg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3vzd0bntcf", "Integrity": "MU3xjGMY8vD4xnfZH0irqgRDlIBPJurjfIRHMdFgLrE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2322, "LastWriteTime": "2025-06-24T04:48:43.4789344+00:00"}, "+gJetkjeBbdrOvE/GGA23QgLXivjnPwO7ogqHCn8a2E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ja52lhp99e-dst12xa6e5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "afv0r8aju3", "Integrity": "Fho5O/9o0iNDe3nq9dL374I8HzMvgHYf0XKv1mVHHes=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2459, "LastWriteTime": "2025-06-24T04:48:43.3311605+00:00"}, "ZEOyk+WocLAY+ALp2zT/9g5OctzWSliX7dDs2QfzJR8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qrxu2zwgr5-wyelmb42ye.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ozm5870g9d", "Integrity": "pIfsvI61HYodf9Y9VWYojaWDL65wjkRFjcxpTvEazOA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2695, "LastWriteTime": "2025-06-24T04:48:43.3032802+00:00"}, "wcJSOgMexzIO2p3cEUwrBSWt5BB50YpbKLiFatIna2U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fyv9wlngmq-2cfqx8u8uj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5716h6abnu", "Integrity": "kTQaOlLKJ55KtOlrilATCAL1Itqma1FiPT6QGGiyGtA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 15709, "LastWriteTime": "2025-06-24T04:48:43.2371514+00:00"}, "aucGMU6PaKytfyIv6tI054PG6JiNE2SC6nBBU8tYJUA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xm0a60puc7-93x5jg3mkp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uaiucbm1pv", "Integrity": "JvCs1LG2508Ne/b8JNQOKhGfyds6lO3lbhO4gfDAoFY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 16254, "LastWriteTime": "2025-06-24T04:48:43.0901825+00:00"}, "Pcubr6ugr59Bwo7m9sAsW0F1835P51gPoFo1PFP9tco=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mv2ne30sfa-l12i2hq2kq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h8ipoprl0m", "Integrity": "kTRdqnkmDvNToK+j8y8JoBEkgz9+TJWxiZpZFGnsZ9I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10440, "LastWriteTime": "2025-06-24T04:48:43.0040005+00:00"}, "cBTP4vxYigy0+KdiRPw/ZBBA+cxGOcKN/nwPZVGmkcQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\nm3k1vr2b0-nolure4ku9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zo7gpxk5f", "Integrity": "0WF3VkObva6Wa3u4Vat19CvEfUrVJRidnq2b5GDlf6s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2489, "LastWriteTime": "2025-06-24T04:48:42.9966728+00:00"}, "Sdjt33u/FgxMh6bG7fEb/2oFONCgt2V95DSqFlwTcPs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pv91aidw81-768xxo33e0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sbm9t2lxqb", "Integrity": "SO7rAf74h99cMMuws/ncxK+EwAJXVdgQJ5sAp1KSbXU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2554, "LastWriteTime": "2025-06-24T04:48:42.9306339+00:00"}, "/tCEoH2/WyAFEU7J86szZRVFlJiXF36TcF6R8mdmdFQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7xl7opbj6m-f6qrsh5ggz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1qcl35ck5", "Integrity": "5n/RRLbNsyNW3ibvrcqQcQtwj/VpOyqKM6Bb/MIqQLc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5410, "LastWriteTime": "2025-06-24T04:48:43.2618001+00:00"}, "q7tBLvIqOpedM3EAcwjcUNl9rNRv6qA7wObnircsfmM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jyffsad4v5-ophjv8dnro.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgipc54347", "Integrity": "RBa+y94ve8y1iS8PDXaSh/V96m4T0/iqzcyeL7E12w4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2252, "LastWriteTime": "2025-06-24T04:48:43.2158488+00:00"}, "C2s2k2eGnq+jevnpovLpujZnX01EN9zU3kvc5a5ef8E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\j28a3ppivi-7e6tdrrvk0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k0ghmblhyu", "Integrity": "rCKyYStmdYCL3Rb5QMndexQo+r4CtD7OkZKREMn1LCc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 23893, "LastWriteTime": "2025-06-24T04:48:43.1435955+00:00"}, "ZTtkYzmopVayl2bREzSrQcT6eOlo7ldhtutbuGm7w2k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\kbgplu5rwn-2k5z9g2rmq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tdrmp9329y", "Integrity": "354Er12tThW7oA0QVo5k3B3EcPpXERhtb8dlAPCtH0g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 45362, "LastWriteTime": "2025-06-24T04:48:43.0911975+00:00"}, "PopOJOeBpslrmpCbih0QWaQ+oytIYM6L9qKsXcV8XnQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7gj2dpgwqs-j22agkcn9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2rvq94iv4", "Integrity": "0XgMu/eU9YU73vu82zjWMvSSvqS5VfvoG50jYZHW31I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2310, "LastWriteTime": "2025-06-24T04:48:43.0553552+00:00"}, "wp7FVSQcGMx9xJa2BqsIaVFjqWKXSJfzEupXmaT9tmw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pexc7mp10a-luafopex6b.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pu1yxqgj95", "Integrity": "H5mIEJSMzvO1gBcLb9KNWSHdMWfZ7p6gbLV+wuXVVyA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2638, "LastWriteTime": "2025-06-24T04:48:42.9630312+00:00"}, "feGuY2aQZJZV8gdIx/dug5a776eIFcQPLtYM0g+peEc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\royp2e729g-qk5bzw2lz9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw02lop9x2", "Integrity": "7CljJtoJ76Wy2TbElyvQjYTmJhCAfK9b6HGkQwgew8o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 20369, "LastWriteTime": "2025-06-24T04:48:42.7593534+00:00"}, "OjUZKmw5QDHXk2IOdVmo1GOtyx+O4raNHhisHOBUsnE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5ctps1xp05-mvr25z4kt0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "agwxpzbopm", "Integrity": "dghGeAbBTXa1lrqBOZ7liVUBAR7r8HnYYw+2JkCUW/8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2147, "LastWriteTime": "2025-06-24T04:48:42.6887259+00:00"}, "pukWX+iNtZt0+JoOI+0lkB8/lrfF0EMZPf4yH4CCpqQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vb7m02hyiv-bhp1yeu8fb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "exl3s6xjb9", "Integrity": "wtX+hXuUbcxGbmCDJRNlpESnKEId7ErP/IOBoEvCJ7A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 34087, "LastWriteTime": "2025-06-24T04:48:42.6303102+00:00"}, "bXQ9lX1BBpTgRSnXOz8l2W1p1xWPvND6I/YxDkl5Z6w=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\nmba0r7o2l-apxeirum8l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0lwizuzr7t", "Integrity": "dKDYYhuZlnAhq5RZjmRjP/EsRLJWttjadZlXsM+HIEk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2192, "LastWriteTime": "2025-06-24T04:48:42.587058+00:00"}, "7tqypJ3UKHBz+82o8IxyHb7YOyS7/I5SnjkX3r44IFg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xklfolbr8t-nbdqcvny6q.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "psyrbwnqwz", "Integrity": "F7z0D/oOHYmW0zj+p/WLcJjjicVz6wdx2buDaInw5W8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2964, "LastWriteTime": "2025-06-24T04:48:42.5538601+00:00"}, "J+uR+RYYxSXc9Hin18JqhOF69bAHW0Z2xWQTdHAaSrE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fw6jev8eke-83ia4yffoo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nk5xnik6d8", "Integrity": "d1mEv0Rt6WBdfpQL4rwhIoaDS6qWWmej9hJxIqnLTh0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3034, "LastWriteTime": "2025-06-24T04:48:42.5217938+00:00"}, "1f/+Y1yBo4DTdcCzYExA8WOhA2klSHscoZLzUlXp79U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9m1cnur73y-xpg3jz0dn2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "58jyewk2fk", "Integrity": "K1wD4MTMOw+faqanguKuQgdh2La9vlHySq9iZs2pqsA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2118, "LastWriteTime": "2025-06-24T04:48:42.4761856+00:00"}, "7FytEwWtHJRpVSC6ImB1iH+pyB05+znR9B2Ba/mYk50=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iwjvmfxqu3-nilb9xe1yl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvqbudk76z", "Integrity": "x44Wd45OmcsG3qNwVpj6F+7mk93lzssVclI1rAt7B+0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7620, "LastWriteTime": "2025-06-24T04:48:42.4631094+00:00"}, "4zmrLwZyExbI9qgJ68F9+19IYTgkp2oliMZbZX0tVqI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bqol8tplix-1zqopzubo2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uwb3h5iqqo", "Integrity": "8htJbKBtKuj6YHTaWR0isn5Y9fRY17VTyW5F5u+k0Q8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2227, "LastWriteTime": "2025-06-24T04:48:42.4761856+00:00"}, "Qe81+AoRsLgmOdkBaxQl/ZOAszyuEPkfTBZ6qazMQuc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lhpdkrosjy-fjhf6hjcqm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e45jqid7h2", "Integrity": "Uu3HYh+pHjWKEq6sJhvrE4tH03XtN4W8kxSrUBQapyQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2110, "LastWriteTime": "2025-06-24T04:48:42.4512178+00:00"}, "k0WH0XHVuEptRMQLn1+r03h9zArcC9595W2sRQkv+yc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\odya9nxts3-abr508h4gv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bluxglujkc", "Integrity": "4cJH9oWzScvfq5sPDwLqijLKv4Kbn+eFNlihP39BDIE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2450, "LastWriteTime": "2025-06-24T04:48:42.4407571+00:00"}, "pUO0H1fI2I2UewPk4nlbqxG1zHJVW9+y+w814Omn5Ao=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\58erqdvtin-gpga8vsymd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmxrtxfzjp", "Integrity": "AvR1RokpATMyFYF92UFBck8yadV27MEZe1mQw5y+IPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5661, "LastWriteTime": "2025-06-24T04:48:42.4217232+00:00"}, "fKTgR/HmWWtpGYQLtaW6k74ChtqICkhi/7zcE6M0m8w=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dlqy23dccu-kcvkdr4alb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "55m1xc85z7", "Integrity": "WVd92+USYOsvgF1RgSbHvExhgdzRFshDmc1F6gdFxBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2354, "LastWriteTime": "2025-06-24T04:48:43.4869727+00:00"}, "fAy5GgU05xqd0/pftodjW/8QCVZbpoYHsPy2C1gHnnQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ksef9slfq8-1fm33xfb4x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77vv86mi5w", "Integrity": "s2+E2apeU/8UtRBgyWXPjk6JSsoklkkKiblSMsIIqx0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 180470, "LastWriteTime": "2025-06-24T04:48:43.3593488+00:00"}, "425DqBmB9CJENBLNOc2BJKw5EtMeXslKEG2UhIOK+Bc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lc1a89fsfa-7whwneqdab.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bqh2hkphpo", "Integrity": "vegRt2qgkUyawGZjWk9QvdAM8Ql6QUV8uDOzTAjXYb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2140, "LastWriteTime": "2025-06-24T04:48:43.2022101+00:00"}, "ah8177tehv+iTA/z+WX1bxfR5fBRmQ4evER4z57svfA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lawfelcw3p-vqk9iwwdi8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jwgopog89g", "Integrity": "bXtSY/cj+SHug4/IkWHIAmGKk4/0fWqz+o1KvV5Gndg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 26864, "LastWriteTime": "2025-06-24T04:48:43.1295772+00:00"}, "Dbyn0SbLIgSiBISpduWekUgpkvq+gJGR3JWBurg93R8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4b9lgapm59-zyak9ezx99.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdk7a3e3e4", "Integrity": "5zihI5BBHgZ6C9wCUHWqtcYbht5v6PGpTIdMtM5GGe4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2229, "LastWriteTime": "2025-06-24T04:48:43.1072737+00:00"}, "yUEl5lgVzIcsi+TlDYZdV/dnX75lqVXCOy/5ikwbh9U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0t5acjtnsc-5e6t7jey7n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oglg669tnk", "Integrity": "WME+orWFAK6pzqfLprop1rImI5xc8lTN10592bCWA1Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2270, "LastWriteTime": "2025-06-24T04:48:44.0860427+00:00"}, "4fyoRgD6gcEEWszcHYNBNPkUD4krZrn0UNY7T9lDFn4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tesx50ibi3-824m7y8iv9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1izng5m8z", "Integrity": "6DJGUFZR8n2Mves782YEbmp6DAE78QTi5yTiiDmEfIE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 12103, "LastWriteTime": "2025-06-24T04:48:44.0760025+00:00"}, "aWgYQlNXkDZ1+Tt0iFNwKyU26EJarsgqirAUtD9MQM4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\07zc013blp-tbmcprtln3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ipjnodiyxp", "Integrity": "ejAVdjqjPJSREmeWoKoFWCthv0S1pfawXAGQ0PCtL3U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1062979, "LastWriteTime": "2025-06-24T04:48:44.0654599+00:00"}, "Lp9g8bTsoz+e9LiPQY2rhXjwhnd3O3eBHbh4puX47K0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\kt41y9oyfq-nudbvebtzm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ubgu6qjwu", "Integrity": "EUqF8NOJ+jTFMJ9zNxnXyY4Z0RushGQ4xMWXukydvow=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 58586, "LastWriteTime": "2025-06-24T04:48:43.5919987+00:00"}, "yIswn6/nDokgDX0kpa/OHkBfv2PLSn26d2zE+KxCjTg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qm6lsqstgu-3mrenqpwlz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8i35yc22yc", "Integrity": "AK2YY7vBkmfd61XozZugjgFjiiqQ9+lFTQOfju1uJpU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 41146, "LastWriteTime": "2025-06-24T04:48:43.4729359+00:00"}, "NFRsEIgzy5GaEFjEQR+Zmwq3zzdbMt6vx7PyjOZxL68=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tqgnr7mkgy-pf4dzgp6et.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jfrip8650v", "Integrity": "oStP6XLcdz/5EfQPc9LYBRGrgqjm5i6VYyEDljdc9Lg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 301144, "LastWriteTime": "2025-06-24T04:48:43.2944115+00:00"}, "b+4VCj5/ifWFGBk6LR7xtqj651pD/KcFTSIRTO8IAMY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hmt6yadul1-cfb8wdf8bw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhbrd5g2p5", "Integrity": "9lPQEYhKUE0lwHNsgmMclDwfvwso1aKf6cS3bMGw38A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 12607, "LastWriteTime": "2025-06-24T04:48:43.1130859+00:00"}, "1NHvjzl2JuiihZHz+eSy3MHP28iegwlkH9vXDf5TbE8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0lktitq3ad-s0hlxxu97u.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4ka65cgi8", "Integrity": "ZD4xPO4qztMhOo8cuRrA35wsEViKaxMJf/6/FyU2Lbk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2023, "LastWriteTime": "2025-06-24T04:48:43.0641467+00:00"}, "NkSyjIQ1esrOxqhkDbmP5oYXDgAoNs16jB9ThgXYxrQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zlfrj5nqwi-a1ph0cw4zn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ad2x663mk", "Integrity": "QpcVARPeINasJbo3DgR/iduaglKuEALRJ8r8Bo+q+n8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2259, "LastWriteTime": "2025-06-24T04:48:43.0502367+00:00"}, "iqJYABJXxWnxV1l9loBe4mhfHr+GI6LDfcOkZ49jt54=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\momr4swqrq-s42jx0duup.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "peimhjwjlv", "Integrity": "saOft7q0HakPdXdg8F0yKzrLc5L47V5VaAyINLdnWhI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2736, "LastWriteTime": "2025-06-24T04:48:42.9442034+00:00"}, "H01JMKzsHf3LX9sfyHRjL6pxs/PTothCal99jk7fgX4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\s007umfqyr-tl58hhwwm9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2ebllpfgpt", "Integrity": "Fqo5F5okh6+1LaodfcG32bl5Kc/Q/0lMwgjk8Hj5cxo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 31154, "LastWriteTime": "2025-06-24T04:48:42.7643831+00:00"}, "Q7KHNAA3Dk4hsTARXy8Rw7uGY7YlUZxMbOHPlqAaVp4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n9ufvg6zor-12eg0vhcpm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y830a1zy97", "Integrity": "cq6zyP5y2mpG2a+gaK4TPe2NohRa+61dX1XphyXo74w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 16048, "LastWriteTime": "2025-06-24T04:48:42.7059502+00:00"}, "xpKBiLE+N8kCBHtsU9OOjdHAfFMULHh6KWvuIpXjXaI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rb0r3wv541-ckahycs9oy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zojeka79o6", "Integrity": "zfgwasG5Wqe80sBTey2LPKjKwtA/P67J7bG4RyDW1fY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5611, "LastWriteTime": "2025-06-24T04:48:43.0019933+00:00"}, "/UY5Dmk3GhxNIIvUDWqvBFMmW0ith1i3ER2O47Uchtc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\876btrwotz-i1l90qe767.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "db52nw0so2", "Integrity": "aX6Czvy2bWobhB9gZsBcEZkxqQw8sB8CO52znepjKi8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10638, "LastWriteTime": "2025-06-24T04:48:42.9401722+00:00"}, "HSe9+FKjgNvsc9E/xxec6vrSdNxtnH5b/jBJmvuD9Pk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\12jxpcjsau-o9b03xt26m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2tg12glemn", "Integrity": "qqIbhoUpTxWiHD2FYyneT+mabxlksEDlNNRVttluOs8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 13342, "LastWriteTime": "2025-06-24T04:48:42.7593534+00:00"}, "LR3DSRSv2KfCkOWcFNo3dB63N9W9H47y8Kwjc49IlWU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0prwepgyfd-300sh8z8ui.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ee6hj5lchi", "Integrity": "prE4z0kV0rwaqLHEDAOIDQ21xglM0hFsisHTidgW9xU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 22456, "LastWriteTime": "2025-06-24T04:48:42.6578291+00:00"}, "ftNyZcgYl3L1oaoW19LqVLMMp+nzqkyHHXX02ZRcOuo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z537psxkb5-rtqo41ax01.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nr6sdeecsv", "Integrity": "veljWMXJcCH7rt7VSDdL+XQqBjvnCbqeh/AyayRKz3g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 7242, "LastWriteTime": "2025-06-24T04:48:42.6513016+00:00"}, "W7Z+XkoMMCDh4LYHhRXd8lC7xUQrG/qoNBmY0V0/DsQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5peanjzhpf-49lyg32can.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7v60bzgxd9", "Integrity": "PUaJImE5ckbLfJYsXFj5cDkMcvJQ9DNVO7MybEkZGvc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 31586, "LastWriteTime": "2025-06-24T04:48:42.5840573+00:00"}, "AkQL4ew35LL93T8ehKd5pSq57uQO2LtLTsGxHQNEKN4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8bprzflbij-psjawytmz8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7sy3fabvn", "Integrity": "pl0sR89QMESvxhxOIVL7R4UjWry/kgPgDunOUEqH7zQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 18143, "LastWriteTime": "2025-06-24T04:48:42.6242841+00:00"}, "dO48iRI0iZ1W7p1suu1rzkCQoaAk7K/JIvENKGUTuns=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\swgmwtsne4-nn6t1rxfu5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mem9qevjnx", "Integrity": "UiSda/h0xpyaOY9R/aCl8liHhr0KKwdVbQwt/YwKPaI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 10555, "LastWriteTime": "2025-06-24T04:48:42.4801851+00:00"}, "Xe3DpRVIfH6ORbpkENxq2+xceeGa+UGvZjS8ouqRqSM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\axunml5apk-9jxobawljc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xn6u2l8nng", "Integrity": "w4xwZbTj0hb1Rhh27gZ4B8fZ1+8yjJI/X9ZBgLiFkZM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 44984, "LastWriteTime": "2025-06-24T04:48:42.4741835+00:00"}, "ndGQmKAiI1YhA/wliSgoGOk0H1Ekrnz3w47+PEzvNk4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\778te4j2e9-rrwevpsa36.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7149sngwk3", "Integrity": "q9ThFzz9q2gD0EwjqEHL5Fmlgen7IfOeVJAau8oBer8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 6939, "LastWriteTime": "2025-06-24T04:48:42.4170349+00:00"}, "nlWYpdvnSfXb18LI9md9Q50p76JyBDmXLran4+CteKU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gqrdkmv4gr-ac0n5txzyd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uvuyz0cttl", "Integrity": "JiyT/6xYYmK0R8a5TbJh0Mws8KUfEYg/jc1xqE0dlxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 12139, "LastWriteTime": "2025-06-24T04:48:43.5728909+00:00"}, "4BBtvSOcIBUw1ES3KE2V/9P9xRo9gdXjIaCmUicZHpc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4ziudi5er1-kqt05iowt5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "86wjlbs9zy", "Integrity": "oGp+2AS6Y5IPJZiieXlusOVrWezQMf+BeNYJtcIo5+A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5340, "LastWriteTime": "2025-06-24T04:48:43.5628491+00:00"}, "8qr6tBSncOH1UwqGEveBQa4YQY077gz5kByb+/H07Zs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\01kj3kzu05-avt2ugss8m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qon64aop3f", "Integrity": "amlyJTqDmydz+OtgLjiXqZby3pdbHW+DGGA1Fi2te5E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 41421, "LastWriteTime": "2025-06-24T04:48:43.495031+00:00"}, "bVJ6brfkG6g2f9OKs5A6kmIHRmtJ40oRjjDwyAIvMu8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\om386z7hvl-ypu8t1a3th.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g53eoah1ck", "Integrity": "jCiqprAiO3CT8Vhgk//euU3zKduq8BvF1YsC1FAdgJ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 15450, "LastWriteTime": "2025-06-24T04:48:43.3094451+00:00"}, "uSaa1DkYWOF7k5grRPuy/aIUsx3TbojPiyriFqjiHBU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1cs6asnscm-dfc7iaw959.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77ioaax9bw", "Integrity": "3Yu3Wa4h3iHEArbBOf7yb+QtRKmJEgAHS2vrZ/8uUK0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 110803, "LastWriteTime": "2025-06-24T04:48:43.2557813+00:00"}, "NYx+cS/LOMUrvqCLyZUDZCE2ChOKADCh9V6cxT8NgBA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\k90ia90a0l-r4fmndj4lr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pzv3gw1kc9", "Integrity": "S5uyDL49nDbBrz5McBV7euPXQ+lMDVjZXoZqpIw7q5g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19101, "LastWriteTime": "2025-06-24T04:48:43.6241836+00:00"}, "RlbvuZnoVECV3SPiN316vwnMObZAQSww12alZrIqJkQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\raqtb0g8tk-r5wuytek4x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yw8digvpjd", "Integrity": "YjA/Rkd9FOdVKIeRjWV+/jFIwK8Jn+80stxpPPbLNKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 20344, "LastWriteTime": "2025-06-24T04:48:43.5995187+00:00"}, "05a0XM5Vo8HZqwlJVlTNNasETvNW2Mub+LW2AXDoEz4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\6a5ovfauzx-dr9ustd9mn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q6e2yjy63c", "Integrity": "rh0k7q0Rj7nJIy9awxK3sNC8ZxQmsbkwNyFdZJvwRqM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 50155, "LastWriteTime": "2025-06-24T04:48:43.5788933+00:00"}, "hezzLgXmXk+RRtIYYOxO5tA9b/qU9MtpLPe/OO2Iy5o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\o4a5y23fdq-yskp2l2j28.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "38gm5sg4jg", "Integrity": "oxAXb9LEPCVRpK6lfJMaCLud8cT9BwAyeIR11RgTH+I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 19930, "LastWriteTime": "2025-06-24T04:48:43.5577848+00:00"}, "DnHDjifII5ljTj+xsF5OrefRuHJuwUJVDhc+B32wes4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\32t05zddb7-7jakql04zz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "484huxgmuc", "Integrity": "pLEkmUQ+d4hzc1xvlvmUVKxzzLq/ZGEqzHWRbEzoqv0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 86883, "LastWriteTime": "2025-06-24T04:48:43.4994947+00:00"}, "+mJIO+HjzkKXDFGEFArqc5HT5brkcRAh1c6jaKaXCUA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wqbo3t8krt-v19ocl650f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2l1l0tmhvx", "Integrity": "Ri8EMIJeDG0rCy+8iYC88ausg6ADxlqjh7cIHeEYXfw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 213340, "LastWriteTime": "2025-06-24T04:48:43.3812102+00:00"}, "sScsg6YezgO5RuP4Ji9Hy9lrONS527q82/4FDq9G6q4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9rp1nsr78a-lyr9te5dpd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ge1qryld7y", "Integrity": "mNy0m8/GIFsF687byrnByu5FydFGY1KQdU9W8nj7CFg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2254, "LastWriteTime": "2025-06-24T04:48:43.184346+00:00"}, "/LYqiupJK9zI+Z0ieXwk4/UeDbMfmHNP3CwumXxibW8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5oprngc3na-rqikjp4z06.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ag89dak36", "Integrity": "l17a+x9KegTblfc489R0R9q9yjDIBn5fG8op2SGjJmA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2199, "LastWriteTime": "2025-06-24T04:48:43.125577+00:00"}, "dTY8I4kTWKmoSLMBoNgxtVwTiifaSVfZjPhyj2S8m7g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\2yj9ptca6s-78mrke9rwp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nn9ju4tano", "Integrity": "XYq8n+UkgUOCvfxP8h2dW4858UaTyqsM10uBPtr8PxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 10481, "LastWriteTime": "2025-06-24T04:48:43.194899+00:00"}, "AiUJZxucxf+fBGoz3ronpQ0+X19EIOQUDWVBVEP84OA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\y9x9709oxs-6jayxq6dso.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i58mqzxche", "Integrity": "xqjDP+M7TxdNllx85RenwK2rx7+V/2slBz8qQGDm1oI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5304, "LastWriteTime": "2025-06-24T04:48:43.0532787+00:00"}, "eihIRXU9zYr5vdyANG1LYJYrXEuebWOzYf5LuDWLUkc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\yqou8ak97v-s21p6d0e1y.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3mjomyzpge", "Integrity": "tXgFpGSAfgq1a9xA2NFRuMqz30+bxV4dmwS1Hrdr1Sw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16342, "LastWriteTime": "2025-06-24T04:48:42.9296359+00:00"}, "NJby657lxrzOJeVpI26kbMdSe4z/bPOq8httg+he3qE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gmtjrsjpjr-bg69h2q1tx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16zmobxf9k", "Integrity": "iU/cpgWhMyQffbAdpduvPLrhY6Kj07nzKAhgBY+dM8I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 8776, "LastWriteTime": "2025-06-24T04:48:42.7633807+00:00"}, "kSD2UOG7Tk6ko4sezvfiXbrPeMMJ4ATpQUuXeO/eGao=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ohnw08v0s7-5i7u2kz8gq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fw5w18ce27", "Integrity": "IRnwVEiXfK+xZ68TNQVJOP2qXguSZXVzrwQxtqPX2aI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2292, "LastWriteTime": "2025-06-24T04:48:43.0742018+00:00"}, "RK/xYWRwb6tGwTnPY1hp4HFOIyrlaDNAnWgBmjwP6f4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cumen4e1px-2y53qsfelr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gyhsstey2d", "Integrity": "GCb8bFKPLaznzN82U9esvWP6yOzad8UP7FjWlXgi/Bk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8195, "LastWriteTime": "2025-06-24T04:48:42.9371692+00:00"}, "kUPdejeIRkqk/24UZbYAXproVz+oDGYadLBf2fxQNr8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\26zimhthez-tx889edwyf.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iyfkzprjym", "Integrity": "NvtWlUdNbuqFIqDe7wn6eig1pzv3ubGVrST6af8kdLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2178, "LastWriteTime": "2025-06-24T04:48:42.8040529+00:00"}, "I7CFumsTl3Zuzo/Y7OTNxMEcHQXpKhSUI+UF1GGB/GE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\l7ml98ds2n-pak789pfhc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qkpig6b8z0", "Integrity": "+ukRzCWyHHI2UMNy8eSTc0+qoj4k6ks7GLIi1AxlSxE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 5594, "LastWriteTime": "2025-06-24T04:48:42.6618322+00:00"}, "XPpwzzrw2iUum15AZ8ogNI26QPO349C5ZD7u5MPkvxc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\y3zikztta2-1trkjj9toj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7zerdc7jwm", "Integrity": "bW4/Q896W7rggpSIkcnzzXK8sF2zS+Nh3Ye+FkOe+Hg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 7897, "LastWriteTime": "2025-06-24T04:48:42.5283016+00:00"}, "xWKIhDra2K+2L5zCRHSig5N0zdshMSfqXxXOUTlWGNI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lvyrlugiaz-whf02me0s4.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mj82s2fn4h", "Integrity": "witmYMa9fX1LWLEnnSkpxc6qMBPvH9UJ56UtpV6UDiw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 42208, "LastWriteTime": "2025-06-24T04:48:42.4651091+00:00"}, "aGvOPs4E2Tn1uU9n57LYPUBvWgor/HGwerLMOAeIfPU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rgl88w1it9-kqgep265ab.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xeynh5pdza", "Integrity": "Bz0JVrr6Hx3RVKbXH1DvlPSwZ8U6urLUZ7YsDLMN1gk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12136, "LastWriteTime": "2025-06-24T04:48:42.6877222+00:00"}, "Ox8S7wtZ0DTbNiSrdA2F2eVc16DbLz3sA5yuLinpeC4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3zji9tv399-ofyky8esh0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ar5txxss3p", "Integrity": "Zmd9bHK3cFt6k9sNgJuREnTS4aEPzxzeMV3m8wfc3tc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1990, "LastWriteTime": "2025-06-24T04:48:42.4706148+00:00"}, "8dAYVoIdpybykda60tbR5omMvz6vlZrvhN/nRNmlvK0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\smvg5f5rpt-xpehz1u5xg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lrfpf212tn", "Integrity": "75zD1Q2ROvS4abvFcfUGIvIRXrYph4uh95ApImW/haU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 6272, "LastWriteTime": "2025-06-24T04:48:42.4322413+00:00"}, "HFMHZj0Epvu3cPP7QU0r0xHrfPCfOCm0BScUT3zjZtQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\giup3hxlyd-8bmqvi5to8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cokylgo4j", "Integrity": "NI6MBIZGKXJj72bc+IDjKDG1nTaRS62UMU7HhUe9B8k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2253, "LastWriteTime": "2025-06-24T04:48:42.4312327+00:00"}, "lY+pmWxc/P2KGRpBahgSRV7Dgqy3AONV0XmphBpKvvc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z98cfng326-m90ww2zviv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rzfub7enaa", "Integrity": "d7OmwmD+uY4Y5zF+IHR/ufDjDASRFWpMQdWoQNnL9XY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2172, "LastWriteTime": "2025-06-24T04:48:43.4658815+00:00"}, "7+kTA7Tnt7T67eNtyvuV53lkhe25v03vPrlJoyXylDA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zr3rxoo55n-g2zbr73a2g.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n0siig7ezj", "Integrity": "qR+bdN+x8r/qd5YeDAZ+iMiMEFXx4Tv3NyBVNkAQYLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2286, "LastWriteTime": "2025-06-24T04:48:43.3179592+00:00"}, "NGw4I8bR4NUdxLviy0rknCq3BKOLV1osGIo5nP4vdeg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\57agg9smmy-k6z943nrpy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wwmvnmo5v8", "Integrity": "wNS9nQ6taI4WIOuWvLhHiNUbHnQHmIghxoU8nE3oEJY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 9800, "LastWriteTime": "2025-06-24T04:48:43.300412+00:00"}, "YZfYmdOV2Vv4bpG216lVSISYN5Jt7IArKqdkGndSRMo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\c4ro1hju42-l2i13om05z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "llifbqlkm7", "Integrity": "FxH7+EkE20SRBfrCFEYpuNyzYsGghnW5dtgaWF6zV1I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35455, "LastWriteTime": "2025-06-24T04:48:43.2143986+00:00"}, "w2uf7p4S2XRhuNrx2C9LYVT22JqjbApC675YCpa4SWk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\leomrziixf-n0jfd8l0iz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e8pz0poje4", "Integrity": "glFWtRMdwoHgsHoIBoafQ9XMwA2fuglIBmfgBwp/YiU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2439, "LastWriteTime": "2025-06-24T04:48:43.1446085+00:00"}, "AlZHFweGIv7NgRtrvd1xDn+JOTsd4EnTiWtivOMR23A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9yxv7vuua-2ad51ju9aa.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "509vpylrg1", "Integrity": "VdBBtw0OhR0il0Y7j/j74swMAJJeki+tg0z1+PPkO5I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3836, "LastWriteTime": "2025-06-24T04:48:43.5050842+00:00"}, "qPaeK58Hu3mudHk021lv7xYuAXqjuT+AC5N0zYlrVAQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\eh357harcp-tvnls6hcxf.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rx3m03fkr3", "Integrity": "b5nlulk0mFhxWADtDot28bUdoRYNnpcyO5azIJrwZU4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 23209, "LastWriteTime": "2025-06-24T04:48:43.4668811+00:00"}, "YD+Slvj5fhzxTOfzNISiYTcJe1Tiw801TWgKbIgGjKI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\binqqlz1sy-jg8c5ekqx6.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s6x2cf719o", "Integrity": "fBPaTZHEVK3+migohaE/Yy7OInHRLWHB65yYdDGyU8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2501, "LastWriteTime": "2025-06-24T04:48:43.3452529+00:00"}, "UTw3yoVEnDR0exZ3k0sPlb+AhLk8tuvT0OW5ahQqG88=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\77au2vjfld-5howj2x4lt.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wvxse30j4g", "Integrity": "dOsRzhaWV96BCY1Ycm/u3ezIaiKAEip0IFSzwd7BEhg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 19528, "LastWriteTime": "2025-06-24T04:48:43.2572917+00:00"}, "ReLE57KKyUWX0Vd+hEK19dEXgymrqlaCyWlxMcCc5As=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\67lx4f78cj-4s4qj16jn0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ppekvym6e6", "Integrity": "g1Gm59Wb7OPwIQWb1m47EmGE4BTjE9iAGpVQHhl4oAs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2175, "LastWriteTime": "2025-06-24T04:48:43.1520682+00:00"}, "7A+AwaQmm2uh+02gUxGCbOmVUD4qMmyOgmwraYa46H4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wdlm54q4fw-p6vx4lif6u.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "68rv787b56", "Integrity": "aq2dt+iaq7WiLm/s8AJSq5QXr2/yJGGxRlNGEE4/DTw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9389, "LastWriteTime": "2025-06-24T04:48:43.08818+00:00"}, "o6q1pL7no9Vj7+S/nz0zoVyXOzOWoj79vIt0vK5LqZc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gdmgran5y7-lx3knuy0pm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f5s2sv31se", "Integrity": "A+OYlrweyDYZMJtkPvPPwP8ovZi4Vnu7KRsklvBzE/Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7337, "LastWriteTime": "2025-06-24T04:48:43.0553552+00:00"}, "6M2NnqnAyBHCochYWcg00f8TOzh+b5EirfmPjQVFLX0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1lzqee7hvd-2qdjhg82pw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "23acjr4ix4", "Integrity": "Dr0M5EqmQmZUYgBlWroUISYdU2GJ8StHTbVINDBa+wI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 15502, "LastWriteTime": "2025-06-24T04:48:42.9976717+00:00"}, "kyp30t8z5ahSWl144Z3N6ykqo1L2S3TdL5tCKTprgcw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sxppidzjgj-gqyf43a3pe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xg2f6930e", "Integrity": "JVlsgLgvw81DErZ//g8X8LzfJ00vxra/ga7xkeXaoYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 4541, "LastWriteTime": "2025-06-24T04:48:43.1191249+00:00"}, "xUROYaTniM9xYgWWECc9P9eFB/1iUEQBX3xeib2pWOc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fnpgxujk9y-imdaogz3ij.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcwtmo1qly", "Integrity": "BfHo7tj/hczINWO90hVez350NzD6tdEZi6SlqcGFjrI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 65136, "LastWriteTime": "2025-06-24T04:48:43.0752005+00:00"}, "aB/Ras/CVM7S4R/erjoPt5Mk5lDLdPFSkz6u0jLRNRg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xnalvtn1mb-d6r1qmhtiw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvzr24ljx7", "Integrity": "Z9CpJlO4WNouGPgRrPDcjuDoHzfGHW/bserGn1p8g1k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2268, "LastWriteTime": "2025-06-24T04:48:42.9936615+00:00"}, "WxSk2K2xNig1qz+02tfcPTp9B0IXqwLYtLdJKK0SToA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\m3qtwn3ns2-rl77dmc4g8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uyur41mm43", "Integrity": "ht3ZdJI0WNtVtJcrhwwLiyOMTQ+OpzS69T1k0fmrc/E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2386, "LastWriteTime": "2025-06-24T04:48:42.9286368+00:00"}, "aJfsFa/ghrcK9Uxgb3nzQwSwX88TWe3ZJPCfw7c7qH8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9wtv5r574i-s4jbqeso3o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3bvzjuovbv", "Integrity": "P/R5dma6QtkpD2X1HHRTI7GIQ2YqYGADg5QWyAlI0Po=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 4990, "LastWriteTime": "2025-06-24T04:48:43.3189581+00:00"}, "B/ofvFBa9Lmym9k+P93Ahv1V1Vxqeg0p3EEec1uwrGo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4wpiy2o4d3-ezdljfc37x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1kbrxf305t", "Integrity": "QQA3GmNzeH8/4+MCBZsdYvQPNEx6TpX7uWm32xE/0Qs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2060, "LastWriteTime": "2025-06-24T04:48:43.240544+00:00"}, "AroOIAyQVmmbO96kmgUzmsaoSbkpnyfZUYc02jK55Ew=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bz3avbczq2-ptdxkmw326.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kwtgxwtrsw", "Integrity": "nDMAP5IcmxPRQpsAAzy/gzBXjAXYGeg7nPilM8qpsc8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 376217, "LastWriteTime": "2025-06-24T04:48:43.1740538+00:00"}, "1Q7aEQTeLdJXkqYopFJdugkhdAasG0AVQU5vbxRfLU0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1umd06yrfb-ayoozo91sk.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1g2486v15h", "Integrity": "nwo2dAyBjQryhcbmVd3111QTbC9rpGkycx7WQ4W43ZY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4537, "LastWriteTime": "2025-06-24T04:48:42.7991986+00:00"}, "0UnUC2Ffs8m9QpVaQXTQZGnpAbSRiQOfjWgstpm6WKc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9316qi1ji-zsobxitq9m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d6ofa9jyov", "Integrity": "Jeax3i9g1YYJWZVGLjEmM8xaFqAkqCqsGfBR6Jm45go=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19796, "LastWriteTime": "2025-06-24T04:48:42.8070528+00:00"}, "TyACglhBG29YJobYlpLsPXXxcADgGZvWAnZKZbi6T7g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hiyuk85oo1-pteo5cxcfm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9b1mghd7aq", "Integrity": "JTS4wqD8bFYTW1Fo9vgCykNZWTY3CRpmz/FQOH8k56I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3101, "LastWriteTime": "2025-06-24T04:48:42.6258023+00:00"}, "BktflUAd3bn32q6hVVC9y5jd2T58FgVBKETVmRi5Ctw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\c39t6asywb-yq2dti153n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pc57z9h8i5", "Integrity": "JxKSollRWK6jo5rUQi7mmn7LCQ96uSQQXWiPSxaSZyw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2554, "LastWriteTime": "2025-06-24T04:48:42.6967648+00:00"}, "N/kQkBiDqK/+EGrcmeUho6ftJoHAoIrJaQY7Wz9suho=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\byt3nyxy2q-69bwpm0gd5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tq490w1kqh", "Integrity": "RRCVYG6YWtl4WNHmCVJKob1znUxxZbfnqqKlaviV5Rs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 118082, "LastWriteTime": "2025-06-24T04:48:42.5890577+00:00"}, "VKJnO+yuvZ0GR4uh4s6LlAAUqHVsr6R/B4vFXGYM4U8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9ssd939fn0-m498x7yd6j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "837<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "ZH6tDts3+2tnHmoD4S0vQ2l7jiJOy7RbAs2vWS3eiD8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13073, "LastWriteTime": "2025-06-24T04:48:42.4741835+00:00"}, "tQrOlCEFreSFhlIhe/GfcOnuhJgyXctPV8BHgo9yjTA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cap10iz261-hsnsliye9b.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0ie2psq76", "Integrity": "QCcx0t5WO62E20gJZxrNEppbnOIEnPtqR1hZ5N68wiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6783, "LastWriteTime": "2025-06-24T04:48:42.4196999+00:00"}, "QYNPDWn4/NcQI/fUfmU+lydth6PweAU2iPX550a7KIg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3paocsqo5e-2a2uywtzte.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fwnhdpwzz1", "Integrity": "wXsA0FaKmHTFMLHbczraksN2of0eY54lAfqJVIx9P2o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2572, "LastWriteTime": "2025-06-24T04:48:42.9341749+00:00"}, "LjlZR8EJU1GtAH+rau2mcLGVQTjf2UoeddSEIR3+NCI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n257kqytbc-v5hyanf0mc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "niyp9u8mcl", "Integrity": "/1sa3aIsEe1TEKgmj+tIgYOKwY1bq3R7NPLEh9XefPc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 35253, "LastWriteTime": "2025-06-24T04:48:43.1575807+00:00"}, "uv/yGSOZrwDkzIq5CgjtkGfZupOD32PvhJ+yDHPUYtY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jimaxq9mi6-y7qnt2sca0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "li1iq7ygyg", "Integrity": "0zEo5lrQWh6+txzgwvVdTPiPXiZqR79gmRerXXVMchM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 38924, "LastWriteTime": "2025-06-24T04:48:43.1052724+00:00"}, "WOT6BJosz+1HWqH8fykpWW3SAvZWyGTlAvgPDPavzBA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7a5kyzpvkv-exlzuq35jp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6hy5rtbzb5", "Integrity": "yjw08JXnHUBEc52XbpXu+/lyNXBOE4Tgrgdq38DoBCQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16046, "LastWriteTime": "2025-06-24T04:48:43.0711865+00:00"}, "Z03CtFKz/zeyQ9X/LVfbo9az7V5jsokepBVj6NPNST4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xde021tovc-f2o09bw51d.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eru1ovbsaf", "Integrity": "BgpD+YeFXdnEP++X5Xvqjprt2EM8VCbTn8lkOvvbA7Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14141, "LastWriteTime": "2025-06-24T04:48:42.9492041+00:00"}, "fdsXuXNxGcQrlVE3/r5df8MikLD5OnqhPt0rDnW2kS8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vzisnnpkmx-4aax14grby.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z0niuoovrs", "Integrity": "E8P7Pf0zmSlS4j8kydrgnYSVRbpqn+L/gEcKgQPjMAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 95472, "LastWriteTime": "2025-06-24T04:48:43.157075+00:00"}, "ktLVnJfWtRkNdXvkKyK3S1WwqMedVzHyV7H7EdQtoT4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8pinc6rjf7-lkbadpelqi.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9qpme5af0r", "Integrity": "P3I7FG1BUJbHvh2BX7b8/0YTpTIkbrJLdNwHTDhZY8s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 32297, "LastWriteTime": "2025-06-24T04:48:43.0261428+00:00"}, "zRHAT60ILtkUB7em9xRM6KELaZftI67j9qK7eeIOfmg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rmczs60qrj-j17trnwz0f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t3s60ixqlz", "Integrity": "yevotpKa4rlbryHPLCgGPBkUFEYvprY6z8pD/xDq5Ck=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2098, "LastWriteTime": "2025-06-24T04:48:42.9757936+00:00"}, "r80d4Xcexm5iqPrgyrTaniMmd9ypFQxo/ACYHsoN8SE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzjo06rbey-r3dkwup91o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wglxhp15vu", "Integrity": "8O9il9SXVU9Y3JRErxXP2HO7RfvF2udbHFCpbzvyHbg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2097, "LastWriteTime": "2025-06-24T04:48:42.7865712+00:00"}, "yumDMMGRj98qUldMIzvMsof8WXyZlj+2n4YArIvwAV4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\yi1kr6g9b3-n74qy90ozc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "30bot3nya8", "Integrity": "s2ahtY6Js1ojFc/LQs0mcx0ogFexM9hdz/y161ZkdOs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 8541, "LastWriteTime": "2025-06-24T04:48:43.2924012+00:00"}, "FJbQ702JImvqT+1/DNUOhRxkE1koP8lfG2DKeqPqANg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0khw4724ee-af1hnqw24k.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "okx3d7tgl0", "Integrity": "SLpXSkFQCEnE9t6BsF+VKhnyxi083qPULeM/9Ji+Q6A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2200, "LastWriteTime": "2025-06-24T04:48:43.1469299+00:00"}, "Hjh1W3npLPABtUMpucgKtWhhuTu+Twf9NZmy1lcsM8U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ojmli06k3u-uosabjs4t4.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhhu59cna9", "Integrity": "Dh0g3MCEcTxNMfxwZGn2FqF1s1GpGId+0biDE1Sw8vY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2852, "LastWriteTime": "2025-06-24T04:48:43.0789183+00:00"}, "N9mOqVNh88WsBDTYx0oTvwhZaL5sZJa23XxIfdTEVe0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\egcrwen6o7-rr2wy4asdd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "19rhglwuhx", "Integrity": "y1fXFjCYAnmS0Kc12Nb6r8YfXyZldWf2/OpKkKw6EBM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 166939, "LastWriteTime": "2025-06-24T04:48:43.0352134+00:00"}, "Kjou67evQf+def08G2JmQQVkS2hjiey9FqRrB99nIbo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzxdtik6zo-1hj4jqau9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9bkwerc34n", "Integrity": "KzdDoMv7JoD6QG9Efhg7xgLDqgA1Mubg8pakNIWTd0c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 131200, "LastWriteTime": "2025-06-24T04:48:43.650778+00:00"}, "qxDyyv8NpsWoKXVngi1bE9tQHfikec3IgXZFktNU+uw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\e97ynr2icq-iudrcw56e1.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cbvypw07go", "Integrity": "CMzRd155p5hsb9RsAB5w0e7pOoS0xg2okJH6wC5TZJk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 29719, "LastWriteTime": "2025-06-24T04:48:43.5688472+00:00"}, "y4aycrdu6LAsekDLcoMt5ynrOuSE4kyMt1DTilBwx28=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\md53uouqm4-4usqb2x1su.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Licensing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Licensing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nymawklkr5", "Integrity": "v9LYceMEWLp1kzr+f0T0USsmoNniv7p8AGLFfGCQGFE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Licensing.wasm", "FileLength": 24051, "LastWriteTime": "2025-06-24T04:48:43.471922+00:00"}, "cx4y/yE5emX75gMmlQguqnZXZwrhc+UKpWAOwsPIuRM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\98ec87va6q-g0hxkhx4x3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.ExcelExport.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.ExcelExport.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4f6apytcw1", "Integrity": "AJCFC2Pqvdw/GD1Gkt/os2ZJWAcIued9L6HIjHeRkp4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.ExcelExport.Net.wasm", "FileLength": 28923, "LastWriteTime": "2025-06-24T04:48:43.3548291+00:00"}, "vhag1S08cYjDB3QlEIozduFTNbeK1mFK7dhKtTKDl84=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zojyde6k3s-11z9idvov7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Themes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Themes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9iukb0h1kh", "Integrity": "cRLPL2Q57th+yyw2/9PBK19vorcgIcyZj0TDF1jRp1I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Themes.wasm", "FileLength": 1755, "LastWriteTime": "2025-06-24T04:48:43.2544015+00:00"}, "ipwH7Tl1Yp1WpOB2oTgkQiAKz5dXy7wSU1ultB496Go=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\w25zlm6clu-4z51p4oqs7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.SplitButtons.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.SplitButtons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gsp8gk5ckq", "Integrity": "kGQnVaUnPb9r3mtX5ssbfAjRg6XR54g6+xmXJp0tGIM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.SplitButtons.wasm", "FileLength": 35675, "LastWriteTime": "2025-06-24T04:48:43.1979017+00:00"}, "7X3OOIYkqB9sQVBkKjOfL85DG7RJ6/snuYSCmvLxUOk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jkmpkjk4xk-pcebwu17mj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Spinner.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Spinner.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9n1gemzepc", "Integrity": "VQKMLBx/3l8cEiooPVjX4wl2MjZK6j3TRcPd8aslfEY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Spinner.wasm", "FileLength": 20991, "LastWriteTime": "2025-06-24T04:48:43.1014091+00:00"}, "jn4XwOUkhDTUoTloGu08HOZ67hxQbmXzeq3PBi3PEJc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xsb0ndz1hr-7uae7ijupc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Schedule.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Schedule.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wszogpubm", "Integrity": "COXx/y6+q19shI6UEA79oHeqXcXQ4+IA8/TstpCggdc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Schedule.wasm", "FileLength": 322664, "LastWriteTime": "2025-06-24T04:48:43.0139182+00:00"}, "auPJSzT3hBIAUmMSAGYmtukOpR8Nihks7moPS663tDA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dwdcdv3wg8-7ksm1zkb1a.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Popups.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Popups.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5sxqodejev", "Integrity": "e8bvI/FFfp22BtOBJhZZ4ClFMW7/W7446ODHMbF3PEs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Popups.wasm", "FileLength": 50629, "LastWriteTime": "2025-06-24T04:48:42.7633807+00:00"}, "Mjo8QSyCP1F+98wholBC/kItzOSpWcKmpp5igIXaTMs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wgslxn3apm-yarh891x5f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Notifications.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Notifications.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lwtv2c719", "Integrity": "PZEimOu5xOABOKsE9gbNrXTketn5gWTgs4AvOt2aXw4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Notifications.wasm", "FileLength": 31431, "LastWriteTime": "2025-06-24T04:48:42.6947653+00:00"}, "8fNPLXCzsnFipAh1m+SyhalhwBMKB8ZaHsIg8gXULaM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cp5mb4volr-5v0k8dfq0z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Navigations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Navigations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yx60uf77an", "Integrity": "Js1zNfWitbEiO8677U9fWmb6EO810Gc7wzPRfZHZWfA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Navigations.wasm", "FileLength": 285456, "LastWriteTime": "2025-06-24T04:48:42.7360194+00:00"}, "JCHY7YyNNxcIEsefr7aPx41S8nDl1KJBKXCNJWpo+io=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\h6h4qv19aa-vei9li4mx3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Lists.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Lists.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jwi117nege", "Integrity": "Dd2QyhqLa6bjHqT3FQivDO1sF9QJMUoQ/lzak//drM4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Lists.wasm", "FileLength": 40049, "LastWriteTime": "2025-06-24T04:48:42.5538601+00:00"}, "/cCZ/nCoTbSWKjpWEUUIq0H8VI977pKoDX9aFWkcdnk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xnicbo8o6w-9027wsoep3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Inputs.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Inputs.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2pck3dc7ep", "Integrity": "usv/fCLyJFipe2YV6J/sbcUbt6OuimeVL+xPwxTurmo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Inputs.wasm", "FileLength": 225184, "LastWriteTime": "2025-06-24T04:48:42.5283016+00:00"}, "drbo1hAHGooSMdWsF0A0D4SSjXpcoA+eZhgWlJKIHnM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3bohcw7c8u-3b3recd4c3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.DropDowns.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.DropDowns.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4xkphrrnbs", "Integrity": "KKP+UEyqZLYfWhqKcyTbmwqtGzvAXiCiCmens/OmU/c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.DropDowns.wasm", "FileLength": 267926, "LastWriteTime": "2025-06-24T04:48:42.6127724+00:00"}, "TwgF76X1cj+RTCL9nrXGw82QS2az9Um/XWBS5jM62dw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lcyu9ghdos-ldhtchhorc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "spu2wylyl7", "Integrity": "AECOTvY4/0LvEj0lBielluQuEqa0IVsqz2Jg7K3nVKc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Data.wasm", "FileLength": 129621, "LastWriteTime": "2025-06-24T04:48:43.3024351+00:00"}, "TAIU/Bn5rUVAoVVgIt8htIE5VreWL68B03p7SgZQiSI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jydetqvzvc-tb4icbsua9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "khsv1q0k1z", "Integrity": "CBVxhDdHMnzQmrd/LMpzQVWP9StZULDZPq+vGwArMQk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Core.wasm", "FileLength": 108873, "LastWriteTime": "2025-06-24T04:48:43.1320973+00:00"}, "Rmffdx/ezXadGK614EpfPydNGJGbaIQcS49OMZF0Z2k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pxxrbnr171-76z3t3ul0w.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "adowunaa6s", "Integrity": "ogKIQZS23c6NxlSnR/P8Bc4hSMjUxKJHMu8tF4WBsKU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Calendars.wasm", "FileLength": 181536, "LastWriteTime": "2025-06-24T04:48:43.006+00:00"}, "qEDWQiM0qm6MByaLTJa3uiTkLUpM+E8LYZqRrwsxKxI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rrp3ygrbh4-t3di59eis6.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Buttons.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Buttons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7klbf6wegy", "Integrity": "5wg6iRs8y4BMsOZWgbQUqC+zIx1CIV9rGYwK3zfZClg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Buttons.wasm", "FileLength": 64659, "LastWriteTime": "2025-06-24T04:48:42.7783233+00:00"}, "qytbjjgTBcfUDRmMhJDkXvv9d08E83VhaPHAC9of0VI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ah5uj9fkaq-tr42ods1qv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qibamu24ww", "Integrity": "Lnnec2bcbNOfFQK9VYlTVxUF8DXy6CbINjLyVzg9GZ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 6764, "LastWriteTime": "2025-06-24T04:48:42.8221513+00:00"}, "wKJuY7Iy0BrfQgORvOubtBDL3T/9TaK31WmNSsp+OxA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ldqgtsc41p-ae1qwufxjk.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xq5kas6hv", "Integrity": "dqz4oJ04lK2Swcgh3kNZ40yIAlv1JsGNeTCEQZQM3sk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24036, "LastWriteTime": "2025-06-24T04:48:42.738021+00:00"}, "bAF+skFj13cHpM27wCRIhSkwo0KlBhhoszPPZ6LSaU8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ta7m7j2gei-lsakbjp1fg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwlb4s9h23", "Integrity": "VXif+d8llcvt+N2pU6LUABQr1EUvnwTg27PGFGjJoWo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 14733, "LastWriteTime": "2025-06-24T04:48:43.5383034+00:00"}, "6udg+/t32bypmkwGSRE+1GTjrhbmv99IzBx5X4Im8FQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sx4xzqaqm5-jt8xzja2dj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2h4o821w0v", "Integrity": "WnOZRQAyyjOv8sTLVpC29t7cLD/gYEUsRWah0QSLSuk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 23295, "LastWriteTime": "2025-06-24T04:48:43.4668811+00:00"}, "ci7f71JFfw2FtKHkz3350lR9bot08llFb6vVnM9jK78=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rvv7resapu-tz325eqvv5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "76x5eflkem", "Integrity": "L/EpLGuZe59Ju8jspXqvtC8hdyOL8Zrhe8lxopsvj6w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 23619, "LastWriteTime": "2025-06-24T04:48:43.2994136+00:00"}, "P+4oBaUWOWcaFFHOppiSkhPO13BVWUGwfqJjjLb1l58=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\r2q3mj3u9h-xlpspxuy08.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny3r3g6nhq", "Integrity": "z48FFALZ2sAP4Fd5H7/RhhuPDZBP1f3ES8esZSke/qU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 18424, "LastWriteTime": "2025-06-24T04:48:43.2231925+00:00"}, "fYg+TRx6dZUiLR7eY5IaD0VY+trznXjsN2mE2Y3sjFg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3wl2w0d9cv-i464dwxnbb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bbtd3i9alo", "Integrity": "p1Ah/YODlnwQ4s7t24etOtyb4hdzr3YlCHH3s8gUCH8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16109, "LastWriteTime": "2025-06-24T04:48:43.1575807+00:00"}, "7QgMsQk9+ueOcNDS2/06696nCt1rpsqR1APIdg2LNOw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pyud5iqoj0-rpvltkbyzt.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gmno2wz14c", "Integrity": "mL9aDIgzoCBBugdOwscAnV2L14lXopq1fPoBppkHjc0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 16310, "LastWriteTime": "2025-06-24T04:48:42.9657635+00:00"}, "AhS4H49XWftGhsMHD0RCx7j3399AS4GBQt5q+JU+B+s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rzc0mkqxf8-1c7ksbormu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvw1zdn8s9", "Integrity": "pyOZoIFEM9t5FDCjL1vt7pFHGrJ/aCpe5ncDLhyScEs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5094, "LastWriteTime": "2025-06-24T04:48:42.7802719+00:00"}, "YI1RJvLXfj//g+HhuhJNTWDVoy1hrsl8xfbXeM5DxNY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fg6rcqyzob-kgyjb8k43h.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "56nyq42peo", "Integrity": "MwyC9p6nt0mGMqIypm+SnvG+21YdrXDmlVaZDNsWJeA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 20683, "LastWriteTime": "2025-06-24T04:48:42.6957639+00:00"}, "EE4UREa418UKdXlBoebhGv+d2fcdWRlKWZ1cCydXyQ8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vhswmu8kpf-xqsu2wsvba.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6msqh3xb8j", "Integrity": "XoDoAbTIxo5MKAxsmkcf9azi6O5OLViGGKrBi2qMlgY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 35118, "LastWriteTime": "2025-06-24T04:48:42.7938932+00:00"}, "473ue3f/7tNfq93v9LFh01pJ9lYT/a8RvdX7eFZFcGA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\luw69m5zpj-yy6f57640l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vliov49hve", "Integrity": "M1N3wrmu41ddGz5INp3pKS70tYR/Y+Xqu+oZ9rZqjZ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 7509, "LastWriteTime": "2025-06-24T04:48:42.6637656+00:00"}, "YklPTpCsJL7LB6ja0cj+RS//e9SASQmh/m+O/tmho0k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f0p7mhideg-en8mb8dgz5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8oobv0w90v", "Integrity": "WO+uRYcj3Zb9HIK7aDnF+ZYPe+fyAeKo2LMHDHQRlOI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 7680, "LastWriteTime": "2025-06-24T04:48:42.6247905+00:00"}, "bqYd0xYG6GmAyEm2x+CicbTZsMUzeo6c1UtFZggTllY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qq3w8mqh2l-0r3amze666.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dz6cxvyzbz", "Integrity": "WSuabncDxkAB8fqRIdPNHPgeAGnmfkzqrcBXgplQMGQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 13809, "LastWriteTime": "2025-06-24T04:48:42.5765192+00:00"}, "6ARM0gyJvUEx95cR4Ew6XImSoqI9urKMPUxUH2Erio8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qmrh3cyln1-8kr5d0tjmo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tvlzg9p4s", "Integrity": "SKcKAQ6unQQmWOLud3+yjljdvRq3k5HjYUL0Z0Ex8QM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 7793, "LastWriteTime": "2025-06-24T04:48:42.551396+00:00"}, "zhWQXabEkOH2z9LDSNg+DR56/knKAbOhHis1YS8hTCw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sd4w29iblj-4njtqvtvgx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ehpzq00vvn", "Integrity": "woWY7cPpxRwo/ZlBGIpiuVyrCcNVURoJEClmhSxYIT0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15095, "LastWriteTime": "2025-06-24T04:48:42.5353442+00:00"}, "EpnPgH7GguhEyLpBcRCW1lJ27Tgzk2p4CTEUhwRZi/I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1jvnjphhuc-txus4zzmh1.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f996fn2i64", "Integrity": "ncBx47S75+HEGz5/+1KAk8NqZREAOIw1j0LT7UeLir0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2409, "LastWriteTime": "2025-06-24T04:48:42.5283016+00:00"}, "auRLvOhHl/vLQdvWC0kR71KyG0l7kZ03z6iXGCT188g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\99xx5yiczq-3uudqrjyld.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aw1545j549", "Integrity": "2sDM+GiLqv0h1K5ltpSprM5ypTpQKddIZ/qeY7ES1Pg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 46105, "LastWriteTime": "2025-06-24T04:48:42.5117538+00:00"}, "YGdgjhT1Y9EbvFR8YCEI961nHJiBE7kkScpoABZf4hI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1gdl188rtq-y9j53ldofb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "01u0wu79e2", "Integrity": "BKCzLjnDV40Tikw9Al6lUk5mePr4/nHVzrNv6at/I4A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 65400, "LastWriteTime": "2025-06-24T04:48:42.4961006+00:00"}, "aWsWQ5U76TiUYRp3wVjZuBStdLYOxWfJSkge08BaaIk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f813afr1vj-i47vxqdqw2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iifampbfuf", "Integrity": "9kglTrQpUWHbUZooUGNxTRs8OZ+RQDXsyCrgBlLse3c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16301, "LastWriteTime": "2025-06-24T04:48:42.4651091+00:00"}, "z7ok2jU/il/hW3ta1tl1qaMMxKNAebqLrZJt/TeOkt8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5j8dxmne5b-mv535bwyet.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ijom068", "Integrity": "+V2Lg1Jy/5iLhIkZHMvZU1uFPbr2UWCvWIR1xl5s8EE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 19025, "LastWriteTime": "2025-06-24T04:48:42.46611+00:00"}, "fAxl8J/rEAZvKq+xl6GnmnRhblKocyvpsUOhkPuSrkA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dy5lkhjkn1-xtjqgewnsy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ypltwg0or", "Integrity": "mTNQIg/OZDt0Q0dAy7dBwiQu2PV2LviEQDjvUWc6sJA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 17685, "LastWriteTime": "2025-06-24T04:48:42.4429431+00:00"}, "KCpXNksQM/a2+9GaT1AoSbKFpr7I03vFDqwMjdW/BkU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f17o09ymz1-vbl3iftxmx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2bq4ped28k", "Integrity": "Ll/BngIPnHtO6rbjdkXKhF2EYc27bIegGy+JdEoDUjs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 129423, "LastWriteTime": "2025-06-24T04:48:42.5077296+00:00"}}, "CachedCopyCandidates": {}}