{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["bRdsss6XflihppLTqqx8AmhVF1ixBoWlL5ybVldq1dg=", "PcCZgmbZQ1/SbX4Ihc8UuqGqpDzhsj74nqoC3Kz21N0=", "K6b9NcyXWScI5bl2NP7h9fN7d75LU6ZNhxFnaeyYaek=", "HJdIBoephfrntIsm+LtcjfYa+aTFjy4ETcsNRWbOjs0=", "yD1ODTvrNcpFov1BCCGGj1FIOhuUznR1/Sk33IiAn+Q=", "/o0Bf3wenAT+DzYe8cwHK2w5Rh/K1ANKNkINLRzqZ6Y=", "/lN4jpq4KkXxZGrrOb/8pdLa0LyOYzoYVPem3vTPJUM=", "WPWMnVJQBoETMRjULX+yUOYKfq4vvOk3zTrl/CC5K6E=", "Ddn0RlhMbYzXK3yRHiLW2kiBPEA2R7Z065LqiUDziYY=", "C9NhH63taFcb0Weg4CqcvQM4GjV/LGIvHZ+mFrSs5BE=", "G7pHaVkAN3c6HRGGYB1U8tUeAGAzaK9K5RLwwRjHzog=", "ofIv6gUomfnAK48Rd3Y5KQPNFt/W0hvkqrGNvwpqthk=", "9Vo5/Q9BpftZjAQGxIh9LtDAvizHBrwu2nHvfogGKAY=", "Z/QQIHH20/4lgfogAp5qH0InWX8h+tnUKdjPGpc84hU=", "YyLtXkZ2QEGu5UHCadIfncOq1fP7aqG0L8gxF2cD6B0=", "WMIGIC9331CSFgDJlGDto1ABPcqkXSIbb+EsAxH2Ij0=", "QPreKICWpnZEoPPM3vxTsGNhu+KqPTLzHtw6VPTlaVY=", "fyD1RPj1tL1oKhjezyTniVuOAKI+FeXkSl1GJLowjiw=", "SKAiPOnZeYWr2bNelKSDiTmQdhzUo3aaRjz8rj28ZeQ=", "8ixwQWEIFImipR8giR2ZyC9MP7ke76KpIpx0Ir0j3Do=", "/S2KuBQklx3K0LK1wo0sKJfmebSv4rufp0e8pMlMQ9M=", "se7CYqXLdaBXiWgpiYfVL1HWBmTjfYH787TwbMp48G4=", "RHNqO9AfMa9IwH51KKC/3XAhIPCsrIiulxpBr5r77hQ=", "GIZ5EGT3mt9FIR+YIo6wWFBnhRCOFy7O8iPVxptOsZk=", "+wEhsc8KrF18m6iz8YGhnkZ9WqP4diSv/2bSAudpDJE=", "F2w1Fulo3rSPdtI0nDf5ZwVdtSV9Ys5dWZctaOuCSTA=", "EedNvZIfp7jcggSunVbbtcSTxqkzy4AKbpkNwLeW6yQ=", "6s851I8ngnPt9lB23lX3MlZ+zdXJVKTZVLHv5KCmITc=", "FM55QgkWfDTWBGCdXeey9Dg3iAnsXuKgoNfamAA947Y=", "58H5jLbO/G2Pyzb+wXAxJHJsAZ6AjilAIU72G2XyA2s=", "YCc9qd0odbWmhe0D6gcN+CFjePS9IKsIX2uT+/4ryVk=", "xvdz6M+wCS7htVyandE/pOJ9bTlmALR9YyNZrxteCJU=", "6OjsEQaYqqKb6UFO2T+pJ2orON2AnUmNkunCF1lhtk0=", "tS13giC7rciPILertJISqJ61EPXQlF4lqX0jVhp4eXc=", "4xPnjBHtDgRHM/DIpQzgYKtHwa9fNmA/rtqdgruf9do=", "lag6pUmpXA3yS59RsJhOE4iBt3ThUMKmbMwJgFxnJHA=", "TFl/tK9ce/ySxaDtHDKkQonLmkR7SGCsut537lH4VuU=", "RVnCl9SktG2/nJFNn23q1OMTbWnBKHC+c5DKSCtyhLQ=", "Bcw86lRN9Qd6ZPfuYrK5e0IW5fRg7EBSAc7DdygLqk4=", "IKmGsuC2hJVB9AkG5rz4OZ8wvrqZzU2/D8HrxKa0ft4=", "vAw892XvMQSagdeIbf6h78n6f3RfDDicN14xoSldqNc=", "8RSyZyRjntmGNNfTuKzmdqGhtT1rvQ6mFx0b6I15Ny0=", "/zGWzgRZ3TW14HX1DNyC0DMj0GsoEio+Q2asMAnclGU=", "oPwYPRMwshsvRljKOrFguQeL6hcyS3dyCDDs0lIOLp0=", "SL15MnugAmY+XJiKJKaQ80gOfZb1O7ayGQN66IEjH+o=", "VZkjmZ+oFjMBJjhH9aiikz+BeY09XfwA+xpah8jsBzQ=", "sTSDlhZIsWwyFyTky93U8/N9eNRXcqyVbUeX6T2oO8M=", "FroFjIQN7lWpt9je7iHdpPsH/CT9eC5OBW23FHgQpGI=", "HIuOTP/yK1EEDXet7yj/pzuBUSlcScWTNLvqcjQB38A=", "gHxzOaFNb8QIb6JZtTA7Db++OsXIxa3HLzvEIBCyEVo=", "M+Frd6VukzT7uAJJ7EAdNNu6v8dI2kLTkNbkCcFf+0M=", "103TjXcjxO348Rh2QQf1izYvaln6fZbmRGB+XaojdVc=", "altOYoB2inMPnApwzathNMnr58xHzeZhjlD9jU5io0Y=", "rNhgwziAQnk/aKWFIt+E3SC+mj347uQyZGmxLPChpH0=", "IC2bpW9LXvDRFaANYeknUewSP6gvlQh8w6EW9nTznk4=", "rCYutKREnGcvSA2b+YMCIC81SdZ8HMRCsIsUr2vBVUU=", "vsGezvC8U7g3ntlLSBjUgXrE8ad8KFcpb2pdN2Q9Uac=", "ZGg0He6ajPf/nPR5/mmNa5KDsKksmw6XntQWx5FnQMc=", "QsYJmk9Qf5TU/Jsw6HT3mKltDLalDKvBwZRXl3WfM/I=", "ik/1ReATc6V0uDlGIWecFBsGIhyjL7OsB+vMXcOqUfM=", "/AgMUCbkkwe0+E8yQUDRuTnsc5io4B2VM1zO91OdrL0=", "gVohlQJEXijGcaNqtqXKHOqD9xE6rgZ8EDUwS3U12eY=", "4rrv01ID8erykJXNu8c0eJOCFQ2d80R9BGQzi1RITus=", "a295+isHpWnxGbmltS2WoaqZhGqKfIeaO1Js4BA667g=", "xYdvyxi1Iyh9emxCZvGsU+Xt9QJKe3OCGdkvBvW9nxE=", "7FFtLmd60uqXLjIP7MAcfhh3EPUNyar0S54X8E5x8xE=", "LbtrKxZFK7DJbZ/qHNwXodmrr6AhVbWo3fTkUDAvqKo=", "XNuwf6dLL+RIdAxwIWaIG3DLz1+4SMlOymw/wbsP+Jw=", "bVshNDaob7ZcToysIlZKSxuvt11Exs8gR+mNWEWFS1I=", "DsBp8hpLjDcIUgeDhDcwIu+pcuAN/ecPycozd8gyRIo=", "o4REcO5U70lcBWbTlxthO3osqeraiAH/GzsFz6b1WKQ=", "zfTdP5U+NBsDqlY8+fv+6PfGIf6SA63Hm4TVwVWb2EM=", "jxV1xpPemtEBGH3+yncJffB+rHIhB7/Wu3sTLlyMYV8=", "Y0Wm1cfuEF4YV01if9W8aRHbqXRJ0RbBpGMHi9bMWBU=", "E1lmcTW+4azlcej5WED3G3SaBEOqp/BZeUHbETOvfJ4=", "sKh5ATu43reWVc1YhMJNOwefl62P+ugJVUWwQk2BoNI=", "Fd/jXts9t8uQWCbyNrHd5tNetzCbkwBkvITCPDBSj5Q=", "qVjrcmeO0Ty+eB2hwVeaRee15R9Mfb1aP9nfOeliYQE=", "4p+fC7WuBLbxmJSAmJd26cEx71JORX+PAaaHBeLRhF4=", "5RaFlHPn4CwAzWzK3sRd3xZWrp5Sd13Jg+Gt/TDtOdg=", "bu6FZO3Z/oWWwEt/rrS85irnF/F29ji71jCgd+V+g20=", "VakQ+gt4ErL0raZJHZy//mZHebrw7b3RUnCddeTe++I=", "85BYkI4B/M4rUp7IV5R83KRRLDS8rFvBau3SjqT9BR0=", "XNcba/36UNqhPrTyb/sh+/ngDQ8xSHwSGChNSF7k8i4=", "CfFsToyXfM2xYollEh9Qroi0wkNEdhgaiEwG5jhi0qw=", "HXveldMorb4s/PzObAXFnMD4XArmDhxX8lOh5leqyw0=", "DQSYp4rmL9FwYtca0ue2pI6tEV/DRjDXOCieejE7re4=", "+Opl4u1nz0tnCPJBvHrjl+TLFYLB4bsek+qk2PLqtm0=", "QB4dX7oE8UbpdeahQpz7H2RBFz7aQVfDn+jznRcfIlg=", "+DBO2flcl5MorczS0UKrJe1Ew5XD2B4vUu2sUEficE8=", "aMkqSUtImzzYDTYo/gGJTXmZEFm5TXX6svh1LLKxLpM=", "m6M/88/e9wrMZMXVkHNJmsNnu2rUaB5FN2jlilHE6Mw=", "ZsYb8DtP8Xd4jpKpdM2nX9zK4gI6KonRw0/GH+j9SRM=", "BynIQVxGqSJ/j2IXwVhZgUozR6CYxcuS6lo8LxDinn4=", "xjAtxc1oKF03OrKp7eEWqhsEkHjk/VoMf9sx6nYEWnQ=", "FZAR9BV2RxhyImygJxuJSOq+lm3sNY+4hQYul3m8/dM=", "AxBv1evB+dc8HI2VH4laNNOn3rkbUH0Q9KENUUMZujI=", "0kDno1g+rS59VkK9I1XOXEPjbrlvZyhHpykqnj1EP0g=", "NE8PeWcVbWssPkpmuy2pU9RgEX0k5L5rC7C+3LTDfqI=", "6cv8JjlT6kPj58ReW5L17vxe3m5RDf1Gx8bLA+kq6bc=", "N9ccdv44vMCyuNHzQcVvus9xm/aHOqQT8PAtby2iPX4=", "QFOAxQ7Fzqovg41Cuf9ND6RrhFA/+AU3voIe3VMzty4=", "hy/mM/NauTuzORcKVXuvK+0CmzuYFn4VU5/QKVTbocM=", "AE8uNtV+Ts3fw0oLFx1I9VBjDQiuCJu1hN/qrMl7jB0=", "SVWuPhTIyAnmD6tESYk6yVoc7k6wn92t69aShubCCeo=", "vPnG5oSQ52WCWtRmWhKj+rpf2msfN6SImT2ANX1/5LA=", "CH9VO7tEHfauO9r2bxeijGpavAF+jUmulf//DFpJgo0=", "3c4EMhpRs3OQmVJqeuXWTwSTPwTeO/k4yTqfAc2F+As=", "zGRcODjWExBBirSFTB0FKcsg71XdxyIWqK96YrCIGKg=", "wCL+DKkn6IuPS7dAFTlS1OqpBqSMTylWH4dK1Xpwes8=", "lsupcCWKRxy4iXquVDduFevnCXXsEDZx0ymDeSPDeRc=", "vc9xCJtmLH9rfik8D0ddlS2vetD/8CtAr0Ny0UwSRkE=", "gGU0XbZrv65IS+Sg/XD1w7sdQkZ5msTqWxYTsIxqZJw=", "dkShDIsdXtv1xSa7GrRQ175UnYsiibXHx9Sr/A1MWEE=", "wprN1+PguHo9cI2nVoAq5BRMUXGehaLNm92ejtBhPeY=", "78SbArzLzDaCmKnCitQwvJBczfXB14NvnoyBqgfwWic=", "teBQs1PvthaerTcOtElhxSpGDUWZsvKuZ5+BbMBgiXM=", "Ci9XvpDIA+gOvs/KsWOuhoqnV4+jmadyKbAxA88gM2E=", "w1mDKnPePgCmzSvATThr8rsEnaTjKY13ejo6bnrad6Y=", "1LqRFYSUOCZ5+QbstgCpEIwOpkOIMDf7c/Oek/lRXzw=", "rxPZAusxIX/t6HeQsQeD4pb2m1Gncpu+0Zc+UqN2qLw=", "/DR7jjD37iIm6+IONKX+xg1nHw2CF80aZTKhO9MeEfM=", "lzr5Evlm8BPmrnTup1Zk6o4khZMj5EliUIBmeEXfOzk=", "9Qsi8GGZqyosCRwks4jGxTLi+58CwVo77+A8J4jM24A=", "Ehh4/D7i7tj6oMyzcNin8lVthLZr+orBEP6mhrrsHPY=", "Ldl4FwtW6/zqgC/8WAWTnZ9e+h1ZSkEGjnjvLRYd2Ms=", "cwSRFbarZTYS9MDA/S41QA8OrV/NkRRAHlnyL+Nknyc=", "rMflJCPJdiM4L9DAgbmq16ZK+4JJYg7zBMzgw9NAbGA=", "6bue7zc1TNVaNcHABNGI297592oIpdyQOldtgC+2RjQ=", "IPULj8zx8JLOkuQRaAuRw0APsXMguztCEvRx/yljEoM=", "hPVlue+EwSxqeiiG03ZiNgHaJLdwoqY7JrvXcyaD7IU=", "wB/A0nW3gjrMmrvZwP+G7rGThhzzH+gYFMiYjjAW1Bk=", "usKe+EngMdZtsKmlMAdVRictJ6Dy7T1QMUY6gW0FZq0=", "Xyqt3RB9Yw2i2WmG+9Y2W2WpZ3IjVdZJ0KgCzt+AHGk=", "RUKyJSn84z/ASOhLItCu6ZrTec7NbwrqMQinfijsJpY=", "5kRiOPbtw/xMJM6gwLWfIL5WbhZhTRAqzWLQ6O6N4zA=", "puLjFKNyx4+y9+Vd3+tYlzjrRISEEct8E0lElACJRoM=", "CVZn4ATC6ax34wtE8GFlBRYB1w7UPdnHMNrsMoqjESo=", "OdkxdrtptIKN2BJISAEHaWvXfiIWzGe41zZm/P6vue0=", "3KRhd60e7rW1Ab7rkKT064wvecJYY0TPONqOZ/s/CCM=", "4a1YVjw8kWgU5uhOn6M1ZAgSyUJwzRusorVMmsyrOJw=", "BiiBhcRTlbJuAe/auZ347Ab7ZIh8ryb/x5KNabbc394=", "vjM/qx6P5Am6kuB5IU8v/uGe3qOVeaPixRyWHGlLhfs=", "M2OZ/yHaAZLLA/yl0dn1UDKyJqEkQv3C+1QsRkJYhrc=", "Oy8B0Kfy32PabViNDjsHK6LLBymEz1/SgfgjPMWHdws=", "3HFIUub0v9kB9KZwJbWNXe7Rd5xPR45ksr/fdVDO5Zs=", "6umwjie9jGvvwI0s18K8/gV4u9f3y2sFnYY0BdBCU2M=", "DUFMlFrd3Apg7693feUsLX0nU75/ijbEIdsKGWwCKlU=", "kkiLl4Kq0fBDKbqrfGhilt7Ke6xTER9eoY87kc6KA5o=", "e2kvVIWjkX8lXqdHuJarw5rjrVg0OG6rDb9lcOEEfWg=", "19Fap83g53wgveuDXdCUases8idr05l6m5qpTwoUQZ0=", "h9FkdEuk1cH059hvLnqWvRsXjNosjPHs0W6eDPB92fM=", "+oeQo3QC3bJs5CtoGHUJSV2ftS6vIjcm8o0gviIBdog=", "jILA72NjsRwuvtQdjGQaU+nBbAWV73XC9u7z8aL729U=", "ioRlEtOmZy1Gm7QQ3rjCiQT7J+r7gYJKFlrc79bVoVg=", "ZrZ+PAraV3+Zs1KTvwetoRjfRI2Xo/Y9NcKkNpjQIb0=", "qPuyMF7PJy7u99wCHPzKqEAjtiW2+NjoejNPNhehCkY=", "NKsvDGO/RSBzDWziWv5NuIuWEEEj9y6duoM0NuyCDE4=", "67fsMEE5niG1tyvsXglPeeI0slD/JhrrpFINHZ4Gs2g=", "guhii5bIvTa71rGutr2FLJLxsJKEsKfs/OW5MV0r9Ng=", "qRBkgywIOhDGvyZJiuvWUBd+3+IL1vOf9ArwfKc5/r0=", "2lEr9xPOJ5f4nmaGZ2zMx+y/qOcI9dMqFwUyullLKlc=", "PKYAOHGem2a20BWTXBj6sw5v+RRAGDrl6ZNM95HvrUs=", "TRN5Q5WjCjVRmmb8P6t0basvHQw/q1o0UMfo/CfHuBg=", "H9Hcl577pAfBP46X2Yr7Pm4nGUYj3uq1sgVvGOdu8Y8=", "eginsADixVq7CY5vMh1b+LCRtlJTj/LyH8ZeBENtQ0Y=", "THyEuo1RUhisEIwbCwgSGSqN4Ef9PxA07vgouPdtTj0=", "jJClw0ZIAFlbIuH0/9IPoh+y64i06PmUMXpoLGs35sg=", "7YquxP6r6xLFJ9YNN0/mgdzGiJScYGxDylZgj4APOX0=", "YnkWkti5q9+gicSh6OT95n0N2e7b4OdljcZ/7gGSjmU=", "xipcqtEdt0m7ECG9X27oWUcWXCRxTOOyuwCcp+PqSks=", "+HqERaQ/9ZJjl/o4u2Tw5L9iIIHSZ3kdhpOJqOFWVIs=", "s17GdLZx7t4pX6taKmAAhvx5kVjioSV0QSYszPIoMjs=", "T0eKwEd21upfeERFp/Gjjb12oAp+JuLwWxkBiQwA0Qw=", "1/cOHvBELGjJNdyYQnxY+8vX/+g+Fie/XRl/fvXQJpA=", "MxvoFzqa+5URGMFJQ0MdpEIFfxwxe7QlJlPw7fmYaNg=", "IYo+oC7ZD6VUNn6hbEn9z+lcLBSNil+y+zXlpxkI2TU=", "ByVMAwYEV97KFo32nNV9X89bjDSVkXjwxc/pO5zsaLM=", "WIsqGe+ln3xlg/RLCDA9vXYhxR6CrMW2zF6Fcqk0uls=", "flMmsPgTa7TT3dHwm6UHlmDng0ADwBDKhCuf/sJbKHI=", "ZyHDWo+pXtv6z5O9DTH3nr5VGleijxtNjE1EJLJU5Po=", "LM5rl1aviXHlfvs44VbGtl/CqZmIYe+w42WjJkp1Rmo=", "xsOkJXyVlYFjj5U9JnEUsiNGhSAy6TdtUCIBD1mDUsc=", "0nJizzehEPP+L1aWBR29CxNVLPVMX144PisdJ0Msyhw=", "fzXMoobAQZYrelnQ2OPzZ8g0iSMYo7u7FoxeNeOk2Ys=", "adTIl8BGv3FH+XgF1w4TMiPH0GpGrwtzZsZXr2aZpGw=", "5VGirRoufy78AW3QmSZWAtOQhqIC7B/MM9LN5+lEbgY=", "pXO8r46Wh0T1obBSTu1a6UDsaEC95tTMShOgJbkXQDg=", "yXzatxDHgfgmcOwGsPpNMzlx3sIX0jlXUD9fNM5H18c=", "lyS1giH4F7uGELcAkUEhlooWbvV6NlWGD41Cb28TnP0=", "VSdlDplHKSzqsuBEPipAWxCxzvRGlK6xBMxXqviDAm8=", "sAwhgOiLswtv40KqEPubqM4D5di24VsKXCHtXp7gK6I=", "MB6mw6FiKGP4oGN90+UEOmMmbBeTcLgqI+PInLYP+LU=", "/N21tYSiPyZMbxhdJgaWU8bDzSy5CnsUDVqmpcq7LEI=", "o/f7tLY3IVk2B8uE5VTM1y8UZwdl+lpuDWdvGfCgCxs=", "Im0eSZguUXNJ+whdehbAn3RpGaPx5sjatML2hq6bAJ0=", "RZlDz37H2uePlpK202oFMb1o2xDLPg1p6WRGrJXDQ4k=", "x56cs9uV+qZI15HkpLa2wSA8EHfOEIKJMbuB7kLtgj4=", "+TMbMTfhIvbTvQIivvSTYmFSTbpol3HLjjxRYJD3xI4=", "X1qJPY3PxLaLMPpSB7HwsIsZ9FlSDVqJsh3HFb6ZPOY=", "txQHkl4E/pyFVDNl6mYBHn/VUwq3jkXLO4ZMpo1OBsA=", "HHstldlyjUidfFog2DZuKGqMJB7uy7HN/Km6coLfWaA=", "50nUCRvRB+yZWlDOkYC35AlysOjaLIdxvzXDQIzhVoQ=", "3Vua1Tm4M9/EX+5uPbdSxJjPfjS4L4W02s5iUSY1pPU=", "O7RV3F+EUZSnHLmzz4N7n1ChUvXECd1TUCp71JdmT2M=", "g6QJYlx0+rpJN4htB6O2kiZb8Tz7/Gu8VJn9HnoZTJw=", "tK70dxUcoBDdOwja8e0BBTeWNe+0wlP/q4RsM2DCIlo=", "JK3TDOzpaWFx93bFWqz1R24yYDX1xj2UYV5yviEQmbk=", "TKBj6SjvGIQU0w2HlI3SA63Zqm54SIWi+0QoZExZuaY=", "EaoVBqtXh4kKyU9Bx1bI00KhZMMswKg2RhvRHZ7RkbY=", "e90vZTvxISRG4EjZsT+ZMV3E/detreWysq1hWQy29s4=", "gyfKnFt57T25rRc/KT9Ru1QzXC+ACK+in2X6A1cn8SU=", "TXIgBuI6rtrXxNl8BHuvZhHWA2aLeeliWdXcuj2NVb4=", "Vq1DFgvyigoJeVyHCf2o/WMCxzobJ+QoRdPB8EwLgV8=", "CJZg5Tu+4QHHnqJWbu1fbzIOYrIRs8GXb3cD6kM071o=", "N6tqc8cl+su0BQVMxcj0aL1qDCjEww0uiLp3CpIx/qU=", "EchoibOnF3A/wlQM1ambbTusyda2keRr9rZ2A+8S4AE=", "+weF0jmdg0FWv5y69OfI46R8OEc/iqRrhSsuNou4vgg=", "hYm4LiI74ZLDSj1yuaZoPb1Zrpk+wQjmBHz/VJ4n4kI=", "hngQul6pC/8l5N9i+GtBDdLlw5S560zXCqqO9ZYFRlA=", "/Y8rzViMuoR2d0IEJQjH9+msG0phsokFRJu2Kr4LOZE=", "S3X08Pkek+Q0A5NgomGDQlihhd2hp/CADiNXIrYkXZI=", "TY7x1cPynA+sfiveGwcicweQwwwLKUrxmqCam1rKr/Q="], "CachedAssets": {"bRdsss6XflihppLTqqx8AmhVF1ixBoWlL5ybVldq1dg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5j8dxmne5b-mv535bwyet.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ijom068", "Integrity": "+V2Lg1Jy/5iLhIkZHMvZU1uFPbr2UWCvWIR1xl5s8EE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 19025, "LastWriteTime": "2025-06-24T04:30:27.2076117+00:00"}, "PcCZgmbZQ1/SbX4Ihc8UuqGqpDzhsj74nqoC3Kz21N0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dy5lkhjkn1-xtjqgewnsy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ypltwg0or", "Integrity": "mTNQIg/OZDt0Q0dAy7dBwiQu2PV2LviEQDjvUWc6sJA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 17685, "LastWriteTime": "2025-06-24T04:30:27.2270978+00:00"}, "K6b9NcyXWScI5bl2NP7h9fN7d75LU6ZNhxFnaeyYaek=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f17o09ymz1-vbl3iftxmx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2bq4ped28k", "Integrity": "Ll/BngIPnHtO6rbjdkXKhF2EYc27bIegGy+JdEoDUjs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 129423, "LastWriteTime": "2025-06-24T04:30:27.2744977+00:00"}, "HJdIBoephfrntIsm+LtcjfYa+aTFjy4ETcsNRWbOjs0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f813afr1vj-i47vxqdqw2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iifampbfuf", "Integrity": "9kglTrQpUWHbUZooUGNxTRs8OZ+RQDXsyCrgBlLse3c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16301, "LastWriteTime": "2025-06-24T04:30:27.3244881+00:00"}, "yD1ODTvrNcpFov1BCCGGj1FIOhuUznR1/Sk33IiAn+Q=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1gdl188rtq-y9j53ldofb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "01u0wu79e2", "Integrity": "BKCzLjnDV40Tikw9Al6lUk5mePr4/nHVzrNv6at/I4A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 65400, "LastWriteTime": "2025-06-24T04:30:27.3765878+00:00"}, "/o0Bf3wenAT+DzYe8cwHK2w5Rh/K1ANKNkINLRzqZ6Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\99xx5yiczq-3uudqrjyld.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aw1545j549", "Integrity": "2sDM+GiLqv0h1K5ltpSprM5ypTpQKddIZ/qeY7ES1Pg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 46105, "LastWriteTime": "2025-06-24T04:30:27.4074014+00:00"}, "/lN4jpq4KkXxZGrrOb/8pdLa0LyOYzoYVPem3vTPJUM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1jvnjphhuc-txus4zzmh1.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f996fn2i64", "Integrity": "ncBx47S75+HEGz5/+1KAk8NqZREAOIw1j0LT7UeLir0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2409, "LastWriteTime": "2025-06-24T04:30:27.4260288+00:00"}, "WPWMnVJQBoETMRjULX+yUOYKfq4vvOk3zTrl/CC5K6E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sd4w29iblj-4njtqvtvgx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ehpzq00vvn", "Integrity": "woWY7cPpxRwo/ZlBGIpiuVyrCcNVURoJEClmhSxYIT0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15095, "LastWriteTime": "2025-06-24T04:30:27.4555752+00:00"}, "Ddn0RlhMbYzXK3yRHiLW2kiBPEA2R7Z065LqiUDziYY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qmrh3cyln1-8kr5d0tjmo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tvlzg9p4s", "Integrity": "SKcKAQ6unQQmWOLud3+yjljdvRq3k5HjYUL0Z0Ex8QM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 7793, "LastWriteTime": "2025-06-24T04:30:27.4653256+00:00"}, "C9NhH63taFcb0Weg4CqcvQM4GjV/LGIvHZ+mFrSs5BE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qq3w8mqh2l-0r3amze666.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dz6cxvyzbz", "Integrity": "WSuabncDxkAB8fqRIdPNHPgeAGnmfkzqrcBXgplQMGQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 13809, "LastWriteTime": "2025-06-24T04:30:27.4729088+00:00"}, "G7pHaVkAN3c6HRGGYB1U8tUeAGAzaK9K5RLwwRjHzog=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f0p7mhideg-en8mb8dgz5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8oobv0w90v", "Integrity": "WO+uRYcj3Zb9HIK7aDnF+ZYPe+fyAeKo2LMHDHQRlOI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 7680, "LastWriteTime": "2025-06-24T04:30:27.4924777+00:00"}, "ofIv6gUomfnAK48Rd3Y5KQPNFt/W0hvkqrGNvwpqthk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\luw69m5zpj-yy6f57640l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vliov49hve", "Integrity": "M1N3wrmu41ddGz5INp3pKS70tYR/Y+Xqu+oZ9rZqjZ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 7509, "LastWriteTime": "2025-06-24T04:30:27.5415311+00:00"}, "9Vo5/Q9BpftZjAQGxIh9LtDAvizHBrwu2nHvfogGKAY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vhswmu8kpf-xqsu2wsvba.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6msqh3xb8j", "Integrity": "XoDoAbTIxo5MKAxsmkcf9azi6O5OLViGGKrBi2qMlgY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 35118, "LastWriteTime": "2025-06-24T04:30:27.5713124+00:00"}, "Z/QQIHH20/4lgfogAp5qH0InWX8h+tnUKdjPGpc84hU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fg6rcqyzob-kgyjb8k43h.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "56nyq42peo", "Integrity": "MwyC9p6nt0mGMqIypm+SnvG+21YdrXDmlVaZDNsWJeA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 20683, "LastWriteTime": "2025-06-24T04:30:27.5915771+00:00"}, "YyLtXkZ2QEGu5UHCadIfncOq1fP7aqG0L8gxF2cD6B0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rzc0mkqxf8-1c7ksbormu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvw1zdn8s9", "Integrity": "pyOZoIFEM9t5FDCjL1vt7pFHGrJ/aCpe5ncDLhyScEs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5094, "LastWriteTime": "2025-06-24T04:30:27.6214901+00:00"}, "WMIGIC9331CSFgDJlGDto1ABPcqkXSIbb+EsAxH2Ij0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pyud5iqoj0-rpvltkbyzt.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gmno2wz14c", "Integrity": "mL9aDIgzoCBBugdOwscAnV2L14lXopq1fPoBppkHjc0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 16310, "LastWriteTime": "2025-06-24T04:30:27.6374391+00:00"}, "QPreKICWpnZEoPPM3vxTsGNhu+KqPTLzHtw6VPTlaVY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3wl2w0d9cv-i464dwxnbb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bbtd3i9alo", "Integrity": "p1Ah/YODlnwQ4s7t24etOtyb4hdzr3YlCHH3s8gUCH8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16109, "LastWriteTime": "2025-06-24T04:30:27.6613923+00:00"}, "fyD1RPj1tL1oKhjezyTniVuOAKI+FeXkSl1GJLowjiw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\r2q3mj3u9h-xlpspxuy08.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny3r3g6nhq", "Integrity": "z48FFALZ2sAP4Fd5H7/RhhuPDZBP1f3ES8esZSke/qU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 18424, "LastWriteTime": "2025-06-24T04:30:27.695573+00:00"}, "SKAiPOnZeYWr2bNelKSDiTmQdhzUo3aaRjz8rj28ZeQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rvv7resapu-tz325eqvv5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "76x5eflkem", "Integrity": "L/EpLGuZe59Ju8jspXqvtC8hdyOL8Zrhe8lxopsvj6w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 23619, "LastWriteTime": "2025-06-24T04:30:27.7688146+00:00"}, "8ixwQWEIFImipR8giR2ZyC9MP7ke76KpIpx0Ir0j3Do=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sx4xzqaqm5-jt8xzja2dj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2h4o821w0v", "Integrity": "WnOZRQAyyjOv8sTLVpC29t7cLD/gYEUsRWah0QSLSuk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 23295, "LastWriteTime": "2025-06-24T04:30:27.7941439+00:00"}, "/S2KuBQklx3K0LK1wo0sKJfmebSv4rufp0e8pMlMQ9M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ta7m7j2gei-lsakbjp1fg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwlb4s9h23", "Integrity": "VXif+d8llcvt+N2pU6LUABQr1EUvnwTg27PGFGjJoWo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 14733, "LastWriteTime": "2025-06-24T04:30:27.866475+00:00"}, "se7CYqXLdaBXiWgpiYfVL1HWBmTjfYH787TwbMp48G4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ldqgtsc41p-ae1qwufxjk.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xq5kas6hv", "Integrity": "dqz4oJ04lK2Swcgh3kNZ40yIAlv1JsGNeTCEQZQM3sk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24036, "LastWriteTime": "2025-06-24T04:30:27.9019082+00:00"}, "RHNqO9AfMa9IwH51KKC/3XAhIPCsrIiulxpBr5r77hQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ah5uj9fkaq-tr42ods1qv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qibamu24ww", "Integrity": "Lnnec2bcbNOfFQK9VYlTVxUF8DXy6CbINjLyVzg9GZ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 6764, "LastWriteTime": "2025-06-24T04:30:27.9139527+00:00"}, "GIZ5EGT3mt9FIR+YIo6wWFBnhRCOFy7O8iPVxptOsZk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rrp3ygrbh4-t3di59eis6.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Buttons.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Buttons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7klbf6wegy", "Integrity": "5wg6iRs8y4BMsOZWgbQUqC+zIx1CIV9rGYwK3zfZClg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Buttons.wasm", "FileLength": 64659, "LastWriteTime": "2025-06-24T04:30:27.6901274+00:00"}, "+wEhsc8KrF18m6iz8YGhnkZ9WqP4diSv/2bSAudpDJE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pxxrbnr171-76z3t3ul0w.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "adowunaa6s", "Integrity": "ogKIQZS23c6NxlSnR/P8Bc4hSMjUxKJHMu8tF4WBsKU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Calendars.wasm", "FileLength": 181536, "LastWriteTime": "2025-06-24T04:30:27.8024278+00:00"}, "F2w1Fulo3rSPdtI0nDf5ZwVdtSV9Ys5dWZctaOuCSTA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jydetqvzvc-tb4icbsua9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "khsv1q0k1z", "Integrity": "CBVxhDdHMnzQmrd/LMpzQVWP9StZULDZPq+vGwArMQk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Core.wasm", "FileLength": 108873, "LastWriteTime": "2025-06-24T04:30:27.882798+00:00"}, "EedNvZIfp7jcggSunVbbtcSTxqkzy4AKbpkNwLeW6yQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lcyu9ghdos-ldhtchhorc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "spu2wylyl7", "Integrity": "AECOTvY4/0LvEj0lBielluQuEqa0IVsqz2Jg7K3nVKc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Data.wasm", "FileLength": 129621, "LastWriteTime": "2025-06-24T04:30:28.0054519+00:00"}, "6s851I8ngnPt9lB23lX3MlZ+zdXJVKTZVLHv5KCmITc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3bohcw7c8u-3b3recd4c3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.DropDowns.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.DropDowns.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4xkphrrnbs", "Integrity": "KKP+UEyqZLYfWhqKcyTbmwqtGzvAXiCiCmens/OmU/c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.DropDowns.wasm", "FileLength": 267926, "LastWriteTime": "2025-06-24T04:30:27.3098325+00:00"}, "FM55QgkWfDTWBGCdXeey9Dg3iAnsXuKgoNfamAA947Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xnicbo8o6w-9027wsoep3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Inputs.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Inputs.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2pck3dc7ep", "Integrity": "usv/fCLyJFipe2YV6J/sbcUbt6OuimeVL+xPwxTurmo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Inputs.wasm", "FileLength": 225184, "LastWriteTime": "2025-06-24T04:30:27.4434338+00:00"}, "58H5jLbO/G2Pyzb+wXAxJHJsAZ6AjilAIU72G2XyA2s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\h6h4qv19aa-vei9li4mx3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Lists.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Lists.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jwi117nege", "Integrity": "Dd2QyhqLa6bjHqT3FQivDO1sF9QJMUoQ/lzak//drM4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Lists.wasm", "FileLength": 40049, "LastWriteTime": "2025-06-24T04:30:27.514878+00:00"}, "YCc9qd0odbWmhe0D6gcN+CFjePS9IKsIX2uT+/4ryVk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cp5mb4volr-5v0k8dfq0z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Navigations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Navigations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yx60uf77an", "Integrity": "Js1zNfWitbEiO8677U9fWmb6EO810Gc7wzPRfZHZWfA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Navigations.wasm", "FileLength": 285456, "LastWriteTime": "2025-06-24T04:30:27.7172265+00:00"}, "xvdz6M+wCS7htVyandE/pOJ9bTlmALR9YyNZrxteCJU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wgslxn3apm-yarh891x5f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Notifications.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Notifications.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lwtv2c719", "Integrity": "PZEimOu5xOABOKsE9gbNrXTketn5gWTgs4AvOt2aXw4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Notifications.wasm", "FileLength": 31431, "LastWriteTime": "2025-06-24T04:30:27.7613077+00:00"}, "6OjsEQaYqqKb6UFO2T+pJ2orON2AnUmNkunCF1lhtk0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dwdcdv3wg8-7ksm1zkb1a.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Popups.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Popups.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5sxqodejev", "Integrity": "e8bvI/FFfp22BtOBJhZZ4ClFMW7/W7446ODHMbF3PEs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Popups.wasm", "FileLength": 50629, "LastWriteTime": "2025-06-24T04:30:27.8034273+00:00"}, "tS13giC7rciPILertJISqJ61EPXQlF4lqX0jVhp4eXc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xsb0ndz1hr-7uae7ijupc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Schedule.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Schedule.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wszogpubm", "Integrity": "COXx/y6+q19shI6UEA79oHeqXcXQ4+IA8/TstpCggdc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Schedule.wasm", "FileLength": 322664, "LastWriteTime": "2025-06-24T04:30:28.0245445+00:00"}, "4xPnjBHtDgRHM/DIpQzgYKtHwa9fNmA/rtqdgruf9do=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jkmpkjk4xk-pcebwu17mj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Spinner.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Spinner.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9n1gemzepc", "Integrity": "VQKMLBx/3l8cEiooPVjX4wl2MjZK6j3TRcPd8aslfEY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Spinner.wasm", "FileLength": 20991, "LastWriteTime": "2025-06-24T04:30:27.944056+00:00"}, "lag6pUmpXA3yS59RsJhOE4iBt3ThUMKmbMwJgFxnJHA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\w25zlm6clu-4z51p4oqs7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.SplitButtons.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.SplitButtons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gsp8gk5ckq", "Integrity": "kGQnVaUnPb9r3mtX5ssbfAjRg6XR54g6+xmXJp0tGIM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.SplitButtons.wasm", "FileLength": 35675, "LastWriteTime": "2025-06-24T04:30:27.9771829+00:00"}, "TFl/tK9ce/ySxaDtHDKkQonLmkR7SGCsut537lH4VuU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zojyde6k3s-11z9idvov7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Themes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Themes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9iukb0h1kh", "Integrity": "cRLPL2Q57th+yyw2/9PBK19vorcgIcyZj0TDF1jRp1I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Themes.wasm", "FileLength": 1755, "LastWriteTime": "2025-06-24T04:30:27.9874778+00:00"}, "RVnCl9SktG2/nJFNn23q1OMTbWnBKHC+c5DKSCtyhLQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\98ec87va6q-g0hxkhx4x3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.ExcelExport.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.ExcelExport.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4f6apytcw1", "Integrity": "AJCFC2Pqvdw/GD1Gkt/os2ZJWAcIued9L6HIjHeRkp4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.ExcelExport.Net.wasm", "FileLength": 28923, "LastWriteTime": "2025-06-24T04:30:28.0414276+00:00"}, "Bcw86lRN9Qd6ZPfuYrK5e0IW5fRg7EBSAc7DdygLqk4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\md53uouqm4-4usqb2x1su.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Licensing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Licensing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nymawklkr5", "Integrity": "v9LYceMEWLp1kzr+f0T0USsmoNniv7p8AGLFfGCQGFE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Licensing.wasm", "FileLength": 24051, "LastWriteTime": "2025-06-24T04:30:28.0672664+00:00"}, "IKmGsuC2hJVB9AkG5rz4OZ8wvrqZzU2/D8HrxKa0ft4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\e97ynr2icq-iudrcw56e1.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cbvypw07go", "Integrity": "CMzRd155p5hsb9RsAB5w0e7pOoS0xg2okJH6wC5TZJk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 29719, "LastWriteTime": "2025-06-24T04:30:28.1045002+00:00"}, "vAw892XvMQSagdeIbf6h78n6f3RfDDicN14xoSldqNc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzxdtik6zo-1hj4jqau9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9bkwerc34n", "Integrity": "KzdDoMv7JoD6QG9Efhg7xgLDqgA1Mubg8pakNIWTd0c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 131200, "LastWriteTime": "2025-06-24T04:30:28.2256923+00:00"}, "8RSyZyRjntmGNNfTuKzmdqGhtT1rvQ6mFx0b6I15Ny0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\egcrwen6o7-rr2wy4asdd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "19rhglwuhx", "Integrity": "y1fXFjCYAnmS0Kc12Nb6r8YfXyZldWf2/OpKkKw6EBM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 166939, "LastWriteTime": "2025-06-24T04:30:28.2966218+00:00"}, "/zGWzgRZ3TW14HX1DNyC0DMj0GsoEio+Q2asMAnclGU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ojmli06k3u-uosabjs4t4.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhhu59cna9", "Integrity": "Dh0g3MCEcTxNMfxwZGn2FqF1s1GpGId+0biDE1Sw8vY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2852, "LastWriteTime": "2025-06-24T04:30:28.311986+00:00"}, "oPwYPRMwshsvRljKOrFguQeL6hcyS3dyCDDs0lIOLp0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0khw4724ee-af1hnqw24k.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "okx3d7tgl0", "Integrity": "SLpXSkFQCEnE9t6BsF+VKhnyxi083qPULeM/9Ji+Q6A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2200, "LastWriteTime": "2025-06-24T04:30:28.3218452+00:00"}, "SL15MnugAmY+XJiKJKaQ80gOfZb1O7ayGQN66IEjH+o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\yi1kr6g9b3-n74qy90ozc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "30bot3nya8", "Integrity": "s2ahtY6Js1ojFc/LQs0mcx0ogFexM9hdz/y161ZkdOs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 8541, "LastWriteTime": "2025-06-24T04:30:28.3293626+00:00"}, "VZkjmZ+oFjMBJjhH9aiikz+BeY09XfwA+xpah8jsBzQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzjo06rbey-r3dkwup91o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wglxhp15vu", "Integrity": "8O9il9SXVU9Y3JRErxXP2HO7RfvF2udbHFCpbzvyHbg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2097, "LastWriteTime": "2025-06-24T04:30:28.3348858+00:00"}, "sTSDlhZIsWwyFyTky93U8/N9eNRXcqyVbUeX6T2oO8M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rmczs60qrj-j17trnwz0f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t3s60ixqlz", "Integrity": "yevotpKa4rlbryHPLCgGPBkUFEYvprY6z8pD/xDq5Ck=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2098, "LastWriteTime": "2025-06-24T04:30:28.34143+00:00"}, "FroFjIQN7lWpt9je7iHdpPsH/CT9eC5OBW23FHgQpGI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8pinc6rjf7-lkbadpelqi.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9qpme5af0r", "Integrity": "P3I7FG1BUJbHvh2BX7b8/0YTpTIkbrJLdNwHTDhZY8s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 32297, "LastWriteTime": "2025-06-24T04:30:28.3677565+00:00"}, "HIuOTP/yK1EEDXet7yj/pzuBUSlcScWTNLvqcjQB38A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vzisnnpkmx-4aax14grby.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z0niuoovrs", "Integrity": "E8P7Pf0zmSlS4j8kydrgnYSVRbpqn+L/gEcKgQPjMAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 95472, "LastWriteTime": "2025-06-24T04:30:28.4379439+00:00"}, "gHxzOaFNb8QIb6JZtTA7Db++OsXIxa3HLzvEIBCyEVo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xde021tovc-f2o09bw51d.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eru1ovbsaf", "Integrity": "BgpD+YeFXdnEP++X5Xvqjprt2EM8VCbTn8lkOvvbA7Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14141, "LastWriteTime": "2025-06-24T04:30:28.459712+00:00"}, "M+Frd6VukzT7uAJJ7EAdNNu6v8dI2kLTkNbkCcFf+0M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7a5kyzpvkv-exlzuq35jp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6hy5rtbzb5", "Integrity": "yjw08JXnHUBEc52XbpXu+/lyNXBOE4Tgrgdq38DoBCQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16046, "LastWriteTime": "2025-06-24T04:30:28.0797838+00:00"}, "103TjXcjxO348Rh2QQf1izYvaln6fZbmRGB+XaojdVc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jimaxq9mi6-y7qnt2sca0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "li1iq7ygyg", "Integrity": "0zEo5lrQWh6+txzgwvVdTPiPXiZqR79gmRerXXVMchM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 38924, "LastWriteTime": "2025-06-24T04:30:28.1170645+00:00"}, "altOYoB2inMPnApwzathNMnr58xHzeZhjlD9jU5io0Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n257kqytbc-v5hyanf0mc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "niyp9u8mcl", "Integrity": "/1sa3aIsEe1TEKgmj+tIgYOKwY1bq3R7NPLEh9XefPc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 35253, "LastWriteTime": "2025-06-24T04:30:28.1535402+00:00"}, "rNhgwziAQnk/aKWFIt+E3SC+mj347uQyZGmxLPChpH0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3paocsqo5e-2a2uywtzte.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fwnhdpwzz1", "Integrity": "wXsA0FaKmHTFMLHbczraksN2of0eY54lAfqJVIx9P2o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2572, "LastWriteTime": "2025-06-24T04:30:28.1923728+00:00"}, "IC2bpW9LXvDRFaANYeknUewSP6gvlQh8w6EW9nTznk4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cap10iz261-hsnsliye9b.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0ie2psq76", "Integrity": "QCcx0t5WO62E20gJZxrNEppbnOIEnPtqR1hZ5N68wiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6783, "LastWriteTime": "2025-06-24T04:30:27.2110625+00:00"}, "rCYutKREnGcvSA2b+YMCIC81SdZ8HMRCsIsUr2vBVUU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9ssd939fn0-m498x7yd6j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "837<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "ZH6tDts3+2tnHmoD4S0vQ2l7jiJOy7RbAs2vWS3eiD8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13073, "LastWriteTime": "2025-06-24T04:30:27.2372326+00:00"}, "vsGezvC8U7g3ntlLSBjUgXrE8ad8KFcpb2pdN2Q9Uac=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\byt3nyxy2q-69bwpm0gd5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tq490w1kqh", "Integrity": "RRCVYG6YWtl4WNHmCVJKob1znUxxZbfnqqKlaviV5Rs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 118082, "LastWriteTime": "2025-06-24T04:30:27.3300625+00:00"}, "ZGg0He6ajPf/nPR5/mmNa5KDsKksmw6XntQWx5FnQMc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\c39t6asywb-yq2dti153n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pc57z9h8i5", "Integrity": "JxKSollRWK6jo5rUQi7mmn7LCQ96uSQQXWiPSxaSZyw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2554, "LastWriteTime": "2025-06-24T04:30:27.3513144+00:00"}, "QsYJmk9Qf5TU/Jsw6HT3mKltDLalDKvBwZRXl3WfM/I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hiyuk85oo1-pteo5cxcfm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9b1mghd7aq", "Integrity": "JTS4wqD8bFYTW1Fo9vgCykNZWTY3CRpmz/FQOH8k56I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3101, "LastWriteTime": "2025-06-24T04:30:27.3602199+00:00"}, "ik/1ReATc6V0uDlGIWecFBsGIhyjL7OsB+vMXcOqUfM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9316qi1ji-zsobxitq9m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d6ofa9jyov", "Integrity": "Jeax3i9g1YYJWZVGLjEmM8xaFqAkqCqsGfBR6Jm45go=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19796, "LastWriteTime": "2025-06-24T04:30:27.4322849+00:00"}, "/AgMUCbkkwe0+E8yQUDRuTnsc5io4B2VM1zO91OdrL0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1umd06yrfb-ayoozo91sk.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1g2486v15h", "Integrity": "nwo2dAyBjQryhcbmVd3111QTbC9rpGkycx7WQ4W43ZY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4537, "LastWriteTime": "2025-06-24T04:30:27.4904695+00:00"}, "gVohlQJEXijGcaNqtqXKHOqD9xE6rgZ8EDUwS3U12eY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bz3avbczq2-ptdxkmw326.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kwtgxwtrsw", "Integrity": "nDMAP5IcmxPRQpsAAzy/gzBXjAXYGeg7nPilM8qpsc8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 376217, "LastWriteTime": "2025-06-24T04:30:27.7202839+00:00"}, "4rrv01ID8erykJXNu8c0eJOCFQ2d80R9BGQzi1RITus=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4wpiy2o4d3-ezdljfc37x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1kbrxf305t", "Integrity": "QQA3GmNzeH8/4+MCBZsdYvQPNEx6TpX7uWm32xE/0Qs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2060, "LastWriteTime": "2025-06-24T04:30:27.7723444+00:00"}, "a295+isHpWnxGbmltS2WoaqZhGqKfIeaO1Js4BA667g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9wtv5r574i-s4jbqeso3o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3bvzjuovbv", "Integrity": "P/R5dma6QtkpD2X1HHRTI7GIQ2YqYGADg5QWyAlI0Po=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 4990, "LastWriteTime": "2025-06-24T04:30:27.8218334+00:00"}, "xYdvyxi1Iyh9emxCZvGsU+Xt9QJKe3OCGdkvBvW9nxE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\m3qtwn3ns2-rl77dmc4g8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uyur41mm43", "Integrity": "ht3ZdJI0WNtVtJcrhwwLiyOMTQ+OpzS69T1k0fmrc/E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2386, "LastWriteTime": "2025-06-24T04:30:27.8503449+00:00"}, "7FFtLmd60uqXLjIP7MAcfhh3EPUNyar0S54X8E5x8xE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xnalvtn1mb-d6r1qmhtiw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvzr24ljx7", "Integrity": "Z9CpJlO4WNouGPgRrPDcjuDoHzfGHW/bserGn1p8g1k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2268, "LastWriteTime": "2025-06-24T04:30:27.8634743+00:00"}, "LbtrKxZFK7DJbZ/qHNwXodmrr6AhVbWo3fTkUDAvqKo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fnpgxujk9y-imdaogz3ij.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcwtmo1qly", "Integrity": "BfHo7tj/hczINWO90hVez350NzD6tdEZi6SlqcGFjrI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 65136, "LastWriteTime": "2025-06-24T04:30:27.9179533+00:00"}, "XNuwf6dLL+RIdAxwIWaIG3DLz1+4SMlOymw/wbsP+Jw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sxppidzjgj-gqyf43a3pe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xg2f6930e", "Integrity": "JVlsgLgvw81DErZ//g8X8LzfJ00vxra/ga7xkeXaoYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 4541, "LastWriteTime": "2025-06-24T04:30:27.9390188+00:00"}, "bVshNDaob7ZcToysIlZKSxuvt11Exs8gR+mNWEWFS1I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1lzqee7hvd-2qdjhg82pw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "23acjr4ix4", "Integrity": "Dr0M5EqmQmZUYgBlWroUISYdU2GJ8StHTbVINDBa+wI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 15502, "LastWriteTime": "2025-06-24T04:30:27.9652178+00:00"}, "DsBp8hpLjDcIUgeDhDcwIu+pcuAN/ecPycozd8gyRIo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gdmgran5y7-lx3knuy0pm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f5s2sv31se", "Integrity": "A+OYlrweyDYZMJtkPvPPwP8ovZi4Vnu7KRsklvBzE/Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7337, "LastWriteTime": "2025-06-24T04:30:28.0064542+00:00"}, "o4REcO5U70lcBWbTlxthO3osqeraiAH/GzsFz6b1WKQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wdlm54q4fw-p6vx4lif6u.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "68rv787b56", "Integrity": "aq2dt+iaq7WiLm/s8AJSq5QXr2/yJGGxRlNGEE4/DTw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9389, "LastWriteTime": "2025-06-24T04:30:28.0885841+00:00"}, "zfTdP5U+NBsDqlY8+fv+6PfGIf6SA63Hm4TVwVWb2EM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\67lx4f78cj-4s4qj16jn0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ppekvym6e6", "Integrity": "g1Gm59Wb7OPwIQWb1m47EmGE4BTjE9iAGpVQHhl4oAs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2175, "LastWriteTime": "2025-06-24T04:30:28.1221097+00:00"}, "jxV1xpPemtEBGH3+yncJffB+rHIhB7/Wu3sTLlyMYV8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\77au2vjfld-5howj2x4lt.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wvxse30j4g", "Integrity": "dOsRzhaWV96BCY1Ycm/u3ezIaiKAEip0IFSzwd7BEhg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 19528, "LastWriteTime": "2025-06-24T04:30:28.1452755+00:00"}, "Y0Wm1cfuEF4YV01if9W8aRHbqXRJ0RbBpGMHi9bMWBU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\binqqlz1sy-jg8c5ekqx6.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s6x2cf719o", "Integrity": "fBPaTZHEVK3+migohaE/Yy7OInHRLWHB65yYdDGyU8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2501, "LastWriteTime": "2025-06-24T04:30:28.1965878+00:00"}, "E1lmcTW+4azlcej5WED3G3SaBEOqp/BZeUHbETOvfJ4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\eh357harcp-tvnls6hcxf.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rx3m03fkr3", "Integrity": "b5nlulk0mFhxWADtDot28bUdoRYNnpcyO5azIJrwZU4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 23209, "LastWriteTime": "2025-06-24T04:30:28.230711+00:00"}, "sKh5ATu43reWVc1YhMJNOwefl62P+ugJVUWwQk2BoNI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9yxv7vuua-2ad51ju9aa.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "509vpylrg1", "Integrity": "VdBBtw0OhR0il0Y7j/j74swMAJJeki+tg0z1+PPkO5I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3836, "LastWriteTime": "2025-06-24T04:30:28.2549902+00:00"}, "Fd/jXts9t8uQWCbyNrHd5tNetzCbkwBkvITCPDBSj5Q=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\leomrziixf-n0jfd8l0iz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e8pz0poje4", "Integrity": "glFWtRMdwoHgsHoIBoafQ9XMwA2fuglIBmfgBwp/YiU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2439, "LastWriteTime": "2025-06-24T04:30:28.2649512+00:00"}, "qVjrcmeO0Ty+eB2hwVeaRee15R9Mfb1aP9nfOeliYQE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\c4ro1hju42-l2i13om05z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "llifbqlkm7", "Integrity": "FxH7+EkE20SRBfrCFEYpuNyzYsGghnW5dtgaWF6zV1I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35455, "LastWriteTime": "2025-06-24T04:30:28.2966218+00:00"}, "4p+fC7WuBLbxmJSAmJd26cEx71JORX+PAaaHBeLRhF4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\57agg9smmy-k6z943nrpy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wwmvnmo5v8", "Integrity": "wNS9nQ6taI4WIOuWvLhHiNUbHnQHmIghxoU8nE3oEJY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 9800, "LastWriteTime": "2025-06-24T04:30:28.310986+00:00"}, "5RaFlHPn4CwAzWzK3sRd3xZWrp5Sd13Jg+Gt/TDtOdg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zr3rxoo55n-g2zbr73a2g.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n0siig7ezj", "Integrity": "qR+bdN+x8r/qd5YeDAZ+iMiMEFXx4Tv3NyBVNkAQYLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2286, "LastWriteTime": "2025-06-24T04:30:28.318887+00:00"}, "bu6FZO3Z/oWWwEt/rrS85irnF/F29ji71jCgd+V+g20=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z98cfng326-m90ww2zviv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rzfub7enaa", "Integrity": "d7OmwmD+uY4Y5zF+IHR/ufDjDASRFWpMQdWoQNnL9XY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2172, "LastWriteTime": "2025-06-24T04:30:28.344695+00:00"}, "VakQ+gt4ErL0raZJHZy//mZHebrw7b3RUnCddeTe++I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\giup3hxlyd-8bmqvi5to8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cokylgo4j", "Integrity": "NI6MBIZGKXJj72bc+IDjKDG1nTaRS62UMU7HhUe9B8k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2253, "LastWriteTime": "2025-06-24T04:30:27.2026129+00:00"}, "85BYkI4B/M4rUp7IV5R83KRRLDS8rFvBau3SjqT9BR0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\smvg5f5rpt-xpehz1u5xg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lrfpf212tn", "Integrity": "75zD1Q2ROvS4abvFcfUGIvIRXrYph4uh95ApImW/haU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 6272, "LastWriteTime": "2025-06-24T04:30:27.2190597+00:00"}, "XNcba/36UNqhPrTyb/sh+/ngDQ8xSHwSGChNSF7k8i4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3zji9tv399-ofyky8esh0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ar5txxss3p", "Integrity": "Zmd9bHK3cFt6k9sNgJuREnTS4aEPzxzeMV3m8wfc3tc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1990, "LastWriteTime": "2025-06-24T04:30:27.2270978+00:00"}, "CfFsToyXfM2xYollEh9Qroi0wkNEdhgaiEwG5jhi0qw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rgl88w1it9-kqgep265ab.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xeynh5pdza", "Integrity": "Bz0JVrr6Hx3RVKbXH1DvlPSwZ8U6urLUZ7YsDLMN1gk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12136, "LastWriteTime": "2025-06-24T04:30:27.3745351+00:00"}, "HXveldMorb4s/PzObAXFnMD4XArmDhxX8lOh5leqyw0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lvyrlugiaz-whf02me0s4.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mj82s2fn4h", "Integrity": "witmYMa9fX1LWLEnnSkpxc6qMBPvH9UJ56UtpV6UDiw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 42208, "LastWriteTime": "2025-06-24T04:30:27.4146249+00:00"}, "DQSYp4rmL9FwYtca0ue2pI6tEV/DRjDXOCieejE7re4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\y3zikztta2-1trkjj9toj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7zerdc7jwm", "Integrity": "bW4/Q896W7rggpSIkcnzzXK8sF2zS+Nh3Ye+FkOe+Hg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 7897, "LastWriteTime": "2025-06-24T04:30:27.4287578+00:00"}, "+Opl4u1nz0tnCPJBvHrjl+TLFYLB4bsek+qk2PLqtm0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\l7ml98ds2n-pak789pfhc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qkpig6b8z0", "Integrity": "+ukRzCWyHHI2UMNy8eSTc0+qoj4k6ks7GLIi1AxlSxE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 5594, "LastWriteTime": "2025-06-24T04:30:27.4515654+00:00"}, "QB4dX7oE8UbpdeahQpz7H2RBFz7aQVfDn+jznRcfIlg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\26zimhthez-tx889edwyf.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iyfkzprjym", "Integrity": "NvtWlUdNbuqFIqDe7wn6eig1pzv3ubGVrST6af8kdLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2178, "LastWriteTime": "2025-06-24T04:30:27.4653256+00:00"}, "+DBO2flcl5MorczS0UKrJe1Ew5XD2B4vUu2sUEficE8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cumen4e1px-2y53qsfelr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gyhsstey2d", "Integrity": "GCb8bFKPLaznzN82U9esvWP6yOzad8UP7FjWlXgi/Bk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8195, "LastWriteTime": "2025-06-24T04:30:27.4904695+00:00"}, "aMkqSUtImzzYDTYo/gGJTXmZEFm5TXX6svh1LLKxLpM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ohnw08v0s7-5i7u2kz8gq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fw5w18ce27", "Integrity": "IRnwVEiXfK+xZ68TNQVJOP2qXguSZXVzrwQxtqPX2aI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2292, "LastWriteTime": "2025-06-24T04:30:27.5031069+00:00"}, "m6M/88/e9wrMZMXVkHNJmsNnu2rUaB5FN2jlilHE6Mw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gmtjrsjpjr-bg69h2q1tx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16zmobxf9k", "Integrity": "iU/cpgWhMyQffbAdpduvPLrhY6Kj07nzKAhgBY+dM8I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 8776, "LastWriteTime": "2025-06-24T04:30:27.511611+00:00"}, "ZsYb8DtP8Xd4jpKpdM2nX9zK4gI6KonRw0/GH+j9SRM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\yqou8ak97v-s21p6d0e1y.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3mjomyzpge", "Integrity": "tXgFpGSAfgq1a9xA2NFRuMqz30+bxV4dmwS1Hrdr1Sw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16342, "LastWriteTime": "2025-06-24T04:30:27.5239342+00:00"}, "BynIQVxGqSJ/j2IXwVhZgUozR6CYxcuS6lo8LxDinn4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\y9x9709oxs-6jayxq6dso.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i58mqzxche", "Integrity": "xqjDP+M7TxdNllx85RenwK2rx7+V/2slBz8qQGDm1oI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5304, "LastWriteTime": "2025-06-24T04:30:27.5496854+00:00"}, "xjAtxc1oKF03OrKp7eEWqhsEkHjk/VoMf9sx6nYEWnQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\2yj9ptca6s-78mrke9rwp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nn9ju4tano", "Integrity": "XYq8n+UkgUOCvfxP8h2dW4858UaTyqsM10uBPtr8PxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 10481, "LastWriteTime": "2025-06-24T04:30:27.5755034+00:00"}, "FZAR9BV2RxhyImygJxuJSOq+lm3sNY+4hQYul3m8/dM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5oprngc3na-rqikjp4z06.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ag89dak36", "Integrity": "l17a+x9KegTblfc489R0R9q9yjDIBn5fG8op2SGjJmA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2199, "LastWriteTime": "2025-06-24T04:30:27.5915771+00:00"}, "AxBv1evB+dc8HI2VH4laNNOn3rkbUH0Q9KENUUMZujI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9rp1nsr78a-lyr9te5dpd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ge1qryld7y", "Integrity": "mNy0m8/GIFsF687byrnByu5FydFGY1KQdU9W8nj7CFg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2254, "LastWriteTime": "2025-06-24T04:30:27.6030974+00:00"}, "0kDno1g+rS59VkK9I1XOXEPjbrlvZyhHpykqnj1EP0g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wqbo3t8krt-v19ocl650f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2l1l0tmhvx", "Integrity": "Ri8EMIJeDG0rCy+8iYC88ausg6ADxlqjh7cIHeEYXfw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 213340, "LastWriteTime": "2025-06-24T04:30:27.7020229+00:00"}, "NE8PeWcVbWssPkpmuy2pU9RgEX0k5L5rC7C+3LTDfqI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\32t05zddb7-7jakql04zz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "484huxgmuc", "Integrity": "pLEkmUQ+d4hzc1xvlvmUVKxzzLq/ZGEqzHWRbEzoqv0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 86883, "LastWriteTime": "2025-06-24T04:30:27.777345+00:00"}, "6cv8JjlT6kPj58ReW5L17vxe3m5RDf1Gx8bLA+kq6bc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\o4a5y23fdq-yskp2l2j28.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "38gm5sg4jg", "Integrity": "oxAXb9LEPCVRpK6lfJMaCLud8cT9BwAyeIR11RgTH+I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 19930, "LastWriteTime": "2025-06-24T04:30:27.8218334+00:00"}, "N9ccdv44vMCyuNHzQcVvus9xm/aHOqQT8PAtby2iPX4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\6a5ovfauzx-dr9ustd9mn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q6e2yjy63c", "Integrity": "rh0k7q0Rj7nJIy9awxK3sNC8ZxQmsbkwNyFdZJvwRqM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 50155, "LastWriteTime": "2025-06-24T04:30:27.8634743+00:00"}, "QFOAxQ7Fzqovg41Cuf9ND6RrhFA/+AU3voIe3VMzty4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\raqtb0g8tk-r5wuytek4x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yw8digvpjd", "Integrity": "YjA/Rkd9FOdVKIeRjWV+/jFIwK8Jn+80stxpPPbLNKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 20344, "LastWriteTime": "2025-06-24T04:30:27.8978724+00:00"}, "hy/mM/NauTuzORcKVXuvK+0CmzuYFn4VU5/QKVTbocM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\k90ia90a0l-r4fmndj4lr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pzv3gw1kc9", "Integrity": "S5uyDL49nDbBrz5McBV7euPXQ+lMDVjZXoZqpIw7q5g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19101, "LastWriteTime": "2025-06-24T04:30:27.9169535+00:00"}, "AE8uNtV+Ts3fw0oLFx1I9VBjDQiuCJu1hN/qrMl7jB0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1cs6asnscm-dfc7iaw959.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77ioaax9bw", "Integrity": "3Yu3Wa4h3iHEArbBOf7yb+QtRKmJEgAHS2vrZ/8uUK0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 110803, "LastWriteTime": "2025-06-24T04:30:27.9662167+00:00"}, "SVWuPhTIyAnmD6tESYk6yVoc7k6wn92t69aShubCCeo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\om386z7hvl-ypu8t1a3th.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g53eoah1ck", "Integrity": "jCiqprAiO3CT8Vhgk//euU3zKduq8BvF1YsC1FAdgJ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 15450, "LastWriteTime": "2025-06-24T04:30:27.9854769+00:00"}, "vPnG5oSQ52WCWtRmWhKj+rpf2msfN6SImT2ANX1/5LA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\01kj3kzu05-avt2ugss8m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qon64aop3f", "Integrity": "amlyJTqDmydz+OtgLjiXqZby3pdbHW+DGGA1Fi2te5E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 41421, "LastWriteTime": "2025-06-24T04:30:28.0044522+00:00"}, "CH9VO7tEHfauO9r2bxeijGpavAF+jUmulf//DFpJgo0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4ziudi5er1-kqt05iowt5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "86wjlbs9zy", "Integrity": "oGp+2AS6Y5IPJZiieXlusOVrWezQMf+BeNYJtcIo5+A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5340, "LastWriteTime": "2025-06-24T04:30:28.0280217+00:00"}, "3c4EMhpRs3OQmVJqeuXWTwSTPwTeO/k4yTqfAc2F+As=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gqrdkmv4gr-ac0n5txzyd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uvuyz0cttl", "Integrity": "JiyT/6xYYmK0R8a5TbJh0Mws8KUfEYg/jc1xqE0dlxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 12139, "LastWriteTime": "2025-06-24T04:30:28.0582243+00:00"}, "zGRcODjWExBBirSFTB0FKcsg71XdxyIWqK96YrCIGKg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\778te4j2e9-rrwevpsa36.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7149sngwk3", "Integrity": "q9ThFzz9q2gD0EwjqEHL5Fmlgen7IfOeVJAau8oBer8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 6939, "LastWriteTime": "2025-06-24T04:30:27.2026129+00:00"}, "wCL+DKkn6IuPS7dAFTlS1OqpBqSMTylWH4dK1Xpwes8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\axunml5apk-9jxobawljc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xn6u2l8nng", "Integrity": "w4xwZbTj0hb1Rhh27gZ4B8fZ1+8yjJI/X9ZBgLiFkZM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 44984, "LastWriteTime": "2025-06-24T04:30:27.2226897+00:00"}, "lsupcCWKRxy4iXquVDduFevnCXXsEDZx0ymDeSPDeRc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\swgmwtsne4-nn6t1rxfu5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mem9qevjnx", "Integrity": "UiSda/h0xpyaOY9R/aCl8liHhr0KKwdVbQwt/YwKPaI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 10555, "LastWriteTime": "2025-06-24T04:30:27.2311518+00:00"}, "vc9xCJtmLH9rfik8D0ddlS2vetD/8CtAr0Ny0UwSRkE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8bprzflbij-psjawytmz8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7sy3fabvn", "Integrity": "pl0sR89QMESvxhxOIVL7R4UjWry/kgPgDunOUEqH7zQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 18143, "LastWriteTime": "2025-06-24T04:30:27.2546421+00:00"}, "gGU0XbZrv65IS+Sg/XD1w7sdQkZ5msTqWxYTsIxqZJw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5peanjzhpf-49lyg32can.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7v60bzgxd9", "Integrity": "PUaJImE5ckbLfJYsXFj5cDkMcvJQ9DNVO7MybEkZGvc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 31586, "LastWriteTime": "2025-06-24T04:30:27.2945051+00:00"}, "dkShDIsdXtv1xSa7GrRQ175UnYsiibXHx9Sr/A1MWEE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z537psxkb5-rtqo41ax01.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nr6sdeecsv", "Integrity": "veljWMXJcCH7rt7VSDdL+XQqBjvnCbqeh/AyayRKz3g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 7242, "LastWriteTime": "2025-06-24T04:30:27.3274992+00:00"}, "wprN1+PguHo9cI2nVoAq5BRMUXGehaLNm92ejtBhPeY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0prwepgyfd-300sh8z8ui.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ee6hj5lchi", "Integrity": "prE4z0kV0rwaqLHEDAOIDQ21xglM0hFsisHTidgW9xU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 22456, "LastWriteTime": "2025-06-24T04:30:27.3571947+00:00"}, "78SbArzLzDaCmKnCitQwvJBczfXB14NvnoyBqgfwWic=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\12jxpcjsau-o9b03xt26m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2tg12glemn", "Integrity": "qqIbhoUpTxWiHD2FYyneT+mabxlksEDlNNRVttluOs8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 13342, "LastWriteTime": "2025-06-24T04:30:27.3808461+00:00"}, "teBQs1PvthaerTcOtElhxSpGDUWZsvKuZ5+BbMBgiXM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\876btrwotz-i1l90qe767.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "db52nw0so2", "Integrity": "aX6Czvy2bWobhB9gZsBcEZkxqQw8sB8CO52znepjKi8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10638, "LastWriteTime": "2025-06-24T04:30:27.4176355+00:00"}, "Ci9XvpDIA+gOvs/KsWOuhoqnV4+jmadyKbAxA88gM2E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rb0r3wv541-ckahycs9oy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zojeka79o6", "Integrity": "zfgwasG5Wqe80sBTey2LPKjKwtA/P67J7bG4RyDW1fY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5611, "LastWriteTime": "2025-06-24T04:30:27.4485943+00:00"}, "w1mDKnPePgCmzSvATThr8rsEnaTjKY13ejo6bnrad6Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n9ufvg6zor-12eg0vhcpm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y830a1zy97", "Integrity": "cq6zyP5y2mpG2a+gaK4TPe2NohRa+61dX1XphyXo74w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 16048, "LastWriteTime": "2025-06-24T04:30:27.4673308+00:00"}, "1LqRFYSUOCZ5+QbstgCpEIwOpkOIMDf7c/Oek/lRXzw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\s007umfqyr-tl58hhwwm9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2ebllpfgpt", "Integrity": "Fqo5F5okh6+1LaodfcG32bl5Kc/Q/0lMwgjk8Hj5cxo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 31154, "LastWriteTime": "2025-06-24T04:30:27.5198069+00:00"}, "rxPZAusxIX/t6HeQsQeD4pb2m1Gncpu+0Zc+UqN2qLw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\momr4swqrq-s42jx0duup.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "peimhjwjlv", "Integrity": "saOft7q0HakPdXdg8F0yKzrLc5L47V5VaAyINLdnWhI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2736, "LastWriteTime": "2025-06-24T04:30:27.5604646+00:00"}, "/DR7jjD37iIm6+IONKX+xg1nHw2CF80aZTKhO9MeEfM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zlfrj5nqwi-a1ph0cw4zn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ad2x663mk", "Integrity": "QpcVARPeINasJbo3DgR/iduaglKuEALRJ8r8Bo+q+n8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2259, "LastWriteTime": "2025-06-24T04:30:27.6159797+00:00"}, "lzr5Evlm8BPmrnTup1Zk6o4khZMj5EliUIBmeEXfOzk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0lktitq3ad-s0hlxxu97u.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4ka65cgi8", "Integrity": "ZD4xPO4qztMhOo8cuRrA35wsEViKaxMJf/6/FyU2Lbk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2023, "LastWriteTime": "2025-06-24T04:30:27.6613923+00:00"}, "9Qsi8GGZqyosCRwks4jGxTLi+58CwVo77+A8J4jM24A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hmt6yadul1-cfb8wdf8bw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhbrd5g2p5", "Integrity": "9lPQEYhKUE0lwHNsgmMclDwfvwso1aKf6cS3bMGw38A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 12607, "LastWriteTime": "2025-06-24T04:30:27.699596+00:00"}, "Ehh4/D7i7tj6oMyzcNin8lVthLZr+orBEP6mhrrsHPY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tqgnr7mkgy-pf4dzgp6et.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jfrip8650v", "Integrity": "oStP6XLcdz/5EfQPc9LYBRGrgqjm5i6VYyEDljdc9Lg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 301144, "LastWriteTime": "2025-06-24T04:30:27.8817938+00:00"}, "Ldl4FwtW6/zqgC/8WAWTnZ9e+h1ZSkEGjnjvLRYd2Ms=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qm6lsqstgu-3mrenqpwlz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8i35yc22yc", "Integrity": "AK2YY7vBkmfd61XozZugjgFjiiqQ9+lFTQOfju1uJpU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 41146, "LastWriteTime": "2025-06-24T04:30:27.9219801+00:00"}, "cwSRFbarZTYS9MDA/S41QA8OrV/NkRRAHlnyL+Nknyc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\kt41y9oyfq-nudbvebtzm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ubgu6qjwu", "Integrity": "EUqF8NOJ+jTFMJ9zNxnXyY4Z0RushGQ4xMWXukydvow=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 58586, "LastWriteTime": "2025-06-24T04:30:28.0074563+00:00"}, "rMflJCPJdiM4L9DAgbmq16ZK+4JJYg7zBMzgw9NAbGA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\07zc013blp-tbmcprtln3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ipjnodiyxp", "Integrity": "ejAVdjqjPJSREmeWoKoFWCthv0S1pfawXAGQ0PCtL3U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1062979, "LastWriteTime": "2025-06-24T04:30:28.5865627+00:00"}, "6bue7zc1TNVaNcHABNGI297592oIpdyQOldtgC+2RjQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tesx50ibi3-824m7y8iv9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1izng5m8z", "Integrity": "6DJGUFZR8n2Mves782YEbmp6DAE78QTi5yTiiDmEfIE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 12103, "LastWriteTime": "2025-06-24T04:30:28.6014048+00:00"}, "IPULj8zx8JLOkuQRaAuRw0APsXMguztCEvRx/yljEoM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0t5acjtnsc-5e6t7jey7n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oglg669tnk", "Integrity": "WME+orWFAK6pzqfLprop1rImI5xc8lTN10592bCWA1Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2270, "LastWriteTime": "2025-06-24T04:30:28.6121779+00:00"}, "hPVlue+EwSxqeiiG03ZiNgHaJLdwoqY7JrvXcyaD7IU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4b9lgapm59-zyak9ezx99.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdk7a3e3e4", "Integrity": "5zihI5BBHgZ6C9wCUHWqtcYbht5v6PGpTIdMtM5GGe4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2229, "LastWriteTime": "2025-06-24T04:30:28.6261614+00:00"}, "wB/A0nW3gjrMmrvZwP+G7rGThhzzH+gYFMiYjjAW1Bk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lawfelcw3p-vqk9iwwdi8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jwgopog89g", "Integrity": "bXtSY/cj+SHug4/IkWHIAmGKk4/0fWqz+o1KvV5Gndg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 26864, "LastWriteTime": "2025-06-24T04:30:28.6437406+00:00"}, "usKe+EngMdZtsKmlMAdVRictJ6Dy7T1QMUY6gW0FZq0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lc1a89fsfa-7whwneqdab.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bqh2hkphpo", "Integrity": "vegRt2qgkUyawGZjWk9QvdAM8Ql6QUV8uDOzTAjXYb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2140, "LastWriteTime": "2025-06-24T04:30:28.6646716+00:00"}, "Xyqt3RB9Yw2i2WmG+9Y2W2WpZ3IjVdZJ0KgCzt+AHGk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ksef9slfq8-1fm33xfb4x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77vv86mi5w", "Integrity": "s2+E2apeU/8UtRBgyWXPjk6JSsoklkkKiblSMsIIqx0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 180470, "LastWriteTime": "2025-06-24T04:30:28.7542295+00:00"}, "RUKyJSn84z/ASOhLItCu6ZrTec7NbwrqMQinfijsJpY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dlqy23dccu-kcvkdr4alb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "55m1xc85z7", "Integrity": "WVd92+USYOsvgF1RgSbHvExhgdzRFshDmc1F6gdFxBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2354, "LastWriteTime": "2025-06-24T04:30:28.7688164+00:00"}, "5kRiOPbtw/xMJM6gwLWfIL5WbhZhTRAqzWLQ6O6N4zA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\58erqdvtin-gpga8vsymd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmxrtxfzjp", "Integrity": "AvR1RokpATMyFYF92UFBck8yadV27MEZe1mQw5y+IPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5661, "LastWriteTime": "2025-06-24T04:30:27.2056147+00:00"}, "puLjFKNyx4+y9+Vd3+tYlzjrRISEEct8E0lElACJRoM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\odya9nxts3-abr508h4gv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bluxglujkc", "Integrity": "4cJH9oWzScvfq5sPDwLqijLKv4Kbn+eFNlihP39BDIE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2450, "LastWriteTime": "2025-06-24T04:30:27.2201184+00:00"}, "CVZn4ATC6ax34wtE8GFlBRYB1w7UPdnHMNrsMoqjESo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lhpdkrosjy-fjhf6hjcqm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e45jqid7h2", "Integrity": "Uu3HYh+pHjWKEq6sJhvrE4tH03XtN4W8kxSrUBQapyQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2110, "LastWriteTime": "2025-06-24T04:30:27.2422629+00:00"}, "OdkxdrtptIKN2BJISAEHaWvXfiIWzGe41zZm/P6vue0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bqol8tplix-1zqopzubo2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uwb3h5iqqo", "Integrity": "8htJbKBtKuj6YHTaWR0isn5Y9fRY17VTyW5F5u+k0Q8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2227, "LastWriteTime": "2025-06-24T04:30:27.2703394+00:00"}, "3KRhd60e7rW1Ab7rkKT064wvecJYY0TPONqOZ/s/CCM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iwjvmfxqu3-nilb9xe1yl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvqbudk76z", "Integrity": "x44Wd45OmcsG3qNwVpj6F+7mk93lzssVclI1rAt7B+0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7620, "LastWriteTime": "2025-06-24T04:30:27.2827944+00:00"}, "4a1YVjw8kWgU5uhOn6M1ZAgSyUJwzRusorVMmsyrOJw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9m1cnur73y-xpg3jz0dn2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "58jyewk2fk", "Integrity": "K1wD4MTMOw+faqanguKuQgdh2La9vlHySq9iZs2pqsA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2118, "LastWriteTime": "2025-06-24T04:30:27.30072+00:00"}, "BiiBhcRTlbJuAe/auZ347Ab7ZIh8ryb/x5KNabbc394=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fw6jev8eke-83ia4yffoo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nk5xnik6d8", "Integrity": "d1mEv0Rt6WBdfpQL4rwhIoaDS6qWWmej9hJxIqnLTh0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3034, "LastWriteTime": "2025-06-24T04:30:27.4954767+00:00"}, "vjM/qx6P5Am6kuB5IU8v/uGe3qOVeaPixRyWHGlLhfs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xklfolbr8t-nbdqcvny6q.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "psyrbwnqwz", "Integrity": "F7z0D/oOHYmW0zj+p/WLcJjjicVz6wdx2buDaInw5W8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2964, "LastWriteTime": "2025-06-24T04:30:27.5080564+00:00"}, "M2OZ/yHaAZLLA/yl0dn1UDKyJqEkQv3C+1QsRkJYhrc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\nmba0r7o2l-apxeirum8l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0lwizuzr7t", "Integrity": "dKDYYhuZlnAhq5RZjmRjP/EsRLJWttjadZlXsM+HIEk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2192, "LastWriteTime": "2025-06-24T04:30:27.5138811+00:00"}, "Oy8B0Kfy32PabViNDjsHK6LLBymEz1/SgfgjPMWHdws=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vb7m02hyiv-bhp1yeu8fb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "exl3s6xjb9", "Integrity": "wtX+hXuUbcxGbmCDJRNlpESnKEId7ErP/IOBoEvCJ7A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 34087, "LastWriteTime": "2025-06-24T04:30:27.5465325+00:00"}, "3HFIUub0v9kB9KZwJbWNXe7Rd5xPR45ksr/fdVDO5Zs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5ctps1xp05-mvr25z4kt0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "agwxpzbopm", "Integrity": "dghGeAbBTXa1lrqBOZ7liVUBAR7r8HnYYw+2JkCUW/8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2147, "LastWriteTime": "2025-06-24T04:30:27.5687741+00:00"}, "6umwjie9jGvvwI0s18K8/gV4u9f3y2sFnYY0BdBCU2M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\royp2e729g-qk5bzw2lz9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw02lop9x2", "Integrity": "7CljJtoJ76Wy2TbElyvQjYTmJhCAfK9b6HGkQwgew8o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 20369, "LastWriteTime": "2025-06-24T04:30:27.5822252+00:00"}, "DUFMlFrd3Apg7693feUsLX0nU75/ijbEIdsKGWwCKlU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pexc7mp10a-luafopex6b.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pu1yxqgj95", "Integrity": "H5mIEJSMzvO1gBcLb9KNWSHdMWfZ7p6gbLV+wuXVVyA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2638, "LastWriteTime": "2025-06-24T04:30:27.5980277+00:00"}, "kkiLl4Kq0fBDKbqrfGhilt7Ke6xTER9eoY87kc6KA5o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7gj2dpgwqs-j22agkcn9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2rvq94iv4", "Integrity": "0XgMu/eU9YU73vu82zjWMvSSvqS5VfvoG50jYZHW31I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2310, "LastWriteTime": "2025-06-24T04:30:27.613607+00:00"}, "e2kvVIWjkX8lXqdHuJarw5rjrVg0OG6rDb9lcOEEfWg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\kbgplu5rwn-2k5z9g2rmq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tdrmp9329y", "Integrity": "354Er12tThW7oA0QVo5k3B3EcPpXERhtb8dlAPCtH0g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 45362, "LastWriteTime": "2025-06-24T04:30:27.6404786+00:00"}, "19Fap83g53wgveuDXdCUases8idr05l6m5qpTwoUQZ0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\j28a3ppivi-7e6tdrrvk0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k0ghmblhyu", "Integrity": "rCKyYStmdYCL3Rb5QMndexQo+r4CtD7OkZKREMn1LCc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 23893, "LastWriteTime": "2025-06-24T04:30:27.6663939+00:00"}, "h9FkdEuk1cH059hvLnqWvRsXjNosjPHs0W6eDPB92fM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jyffsad4v5-ophjv8dnro.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgipc54347", "Integrity": "RBa+y94ve8y1iS8PDXaSh/V96m4T0/iqzcyeL7E12w4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2252, "LastWriteTime": "2025-06-24T04:30:27.6860922+00:00"}, "+oeQo3QC3bJs5CtoGHUJSV2ftS6vIjcm8o0gviIBdog=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7xl7opbj6m-f6qrsh5ggz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1qcl35ck5", "Integrity": "5n/RRLbNsyNW3ibvrcqQcQtwj/VpOyqKM6Bb/MIqQLc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5410, "LastWriteTime": "2025-06-24T04:30:27.7152257+00:00"}, "jILA72NjsRwuvtQdjGQaU+nBbAWV73XC9u7z8aL729U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pv91aidw81-768xxo33e0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sbm9t2lxqb", "Integrity": "SO7rAf74h99cMMuws/ncxK+EwAJXVdgQJ5sAp1KSbXU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2554, "LastWriteTime": "2025-06-24T04:30:27.7564154+00:00"}, "ioRlEtOmZy1Gm7QQ3rjCiQT7J+r7gYJKFlrc79bVoVg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\nm3k1vr2b0-nolure4ku9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zo7gpxk5f", "Integrity": "0WF3VkObva6Wa3u4Vat19CvEfUrVJRidnq2b5GDlf6s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2489, "LastWriteTime": "2025-06-24T04:30:27.7623056+00:00"}, "ZrZ+PAraV3+Zs1KTvwetoRjfRI2Xo/Y9NcKkNpjQIb0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mv2ne30sfa-l12i2hq2kq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h8ipoprl0m", "Integrity": "kTRdqnkmDvNToK+j8y8JoBEkgz9+TJWxiZpZFGnsZ9I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10440, "LastWriteTime": "2025-06-24T04:30:27.8024278+00:00"}, "qPuyMF7PJy7u99wCHPzKqEAjtiW2+NjoejNPNhehCkY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xm0a60puc7-93x5jg3mkp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uaiucbm1pv", "Integrity": "JvCs1LG2508Ne/b8JNQOKhGfyds6lO3lbhO4gfDAoFY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 16254, "LastWriteTime": "2025-06-24T04:30:27.8415044+00:00"}, "NKsvDGO/RSBzDWziWv5NuIuWEEEj9y6duoM0NuyCDE4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fyv9wlngmq-2cfqx8u8uj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5716h6abnu", "Integrity": "kTQaOlLKJ55KtOlrilATCAL1Itqma1FiPT6QGGiyGtA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 15709, "LastWriteTime": "2025-06-24T04:30:27.8850263+00:00"}, "67fsMEE5niG1tyvsXglPeeI0slD/JhrrpFINHZ4Gs2g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qrxu2zwgr5-wyelmb42ye.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ozm5870g9d", "Integrity": "pIfsvI61HYodf9Y9VWYojaWDL65wjkRFjcxpTvEazOA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2695, "LastWriteTime": "2025-06-24T04:30:27.9096772+00:00"}, "guhii5bIvTa71rGutr2FLJLxsJKEsKfs/OW5MV0r9Ng=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ja52lhp99e-dst12xa6e5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "afv0r8aju3", "Integrity": "Fho5O/9o0iNDe3nq9dL374I8HzMvgHYf0XKv1mVHHes=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2459, "LastWriteTime": "2025-06-24T04:30:27.944056+00:00"}, "qRBkgywIOhDGvyZJiuvWUBd+3+IL1vOf9ArwfKc5/r0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\g8d1ll62re-6ei8na5yhg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3vzd0bntcf", "Integrity": "MU3xjGMY8vD4xnfZH0irqgRDlIBPJurjfIRHMdFgLrE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2322, "LastWriteTime": "2025-06-24T04:30:27.9560496+00:00"}, "2lEr9xPOJ5f4nmaGZ2zMx+y/qOcI9dMqFwUyullLKlc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\plvxa4s6ga-3eb8c3pfwe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t50l0nov1q", "Integrity": "EfBffVPWpe58xruJwJsNlZ9rzwMqGZx5/IAZ2lmbNLo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2267, "LastWriteTime": "2025-06-24T04:30:27.9688234+00:00"}, "PKYAOHGem2a20BWTXBj6sw5v+RRAGDrl6ZNM95HvrUs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\arw8hu9x14-171oaphs01.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cscfdewx0f", "Integrity": "hKxOOBQ/fSCsL2LrAMqY/2sULobybRjt4W14A9awCE0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2197, "LastWriteTime": "2025-06-24T04:30:27.2011532+00:00"}, "TRN5Q5WjCjVRmmb8P6t0basvHQw/q1o0UMfo/CfHuBg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\il592650ow-1hrv129abp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tt4ixubui0", "Integrity": "8RH9fKYh+mRXmKintECdQg+YOjEP4UyJL72KP9CV1AA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2324, "LastWriteTime": "2025-06-24T04:30:27.2096249+00:00"}, "H9Hcl577pAfBP46X2Yr7Pm4nGUYj3uq1sgVvGOdu8Y8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ced8wcd8cu-atf14r4yhw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7l50o3qupp", "Integrity": "S0DHA/lyKN91O8N20+vhKfUiDaVYpC1L1K5kilpR13c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2652, "LastWriteTime": "2025-06-24T04:30:27.2216798+00:00"}, "eginsADixVq7CY5vMh1b+LCRtlJTj/LyH8ZeBENtQ0Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\eh7hhep0d3-ac8qf4w6lx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n18lxojb0f", "Integrity": "+yyjpud2HwQ+OGPCdI/UqCTj+pb8244ITQWUvwfpbRw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 177338, "LastWriteTime": "2025-06-24T04:30:27.2945051+00:00"}, "THyEuo1RUhisEIwbCwgSGSqN4Ef9PxA07vgouPdtTj0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jyu1zp9qgf-nqh8s5tb94.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ax3au530m6", "Integrity": "XnRIHwY2UCoiFriXyUFvCdkZICFGHZEtMa9F8lvzbWw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 10532, "LastWriteTime": "2025-06-24T04:30:27.3233081+00:00"}, "jJClw0ZIAFlbIuH0/9IPoh+y64i06PmUMXpoLGs35sg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\p1o86wnkwn-61u88luwrz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bolqu0i7zp", "Integrity": "xVHyKZDBFzSye02g1tUAgVfqXS4Q71WdcXJ7u5BfzII=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2151, "LastWriteTime": "2025-06-24T04:30:27.352836+00:00"}, "7YquxP6r6xLFJ9YNN0/mgdzGiJScYGxDylZgj4APOX0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ylntj2h544-kkdt47cvjv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xz07b795u7", "Integrity": "M8ityBtfy534MWphTysAcVhdDUpR9qBJU8TgCh6Pqmw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2184, "LastWriteTime": "2025-06-24T04:30:27.8503449+00:00"}, "YnkWkti5q9+gicSh6OT95n0N2e7b4OdljcZ/7gGSjmU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xgis4ptx7x-dhjbngzvrh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c4pgkkmsgv", "Integrity": "LKdHfLw90xpicOQyO0W2P3QJlBkiuIVnXgB7U6/Nh8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2952, "LastWriteTime": "2025-06-24T04:30:27.8729358+00:00"}, "xipcqtEdt0m7ECG9X27oWUcWXCRxTOOyuwCcp+PqSks=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8801tn65l9-9c7ym8z6z0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxdot1nk1o", "Integrity": "f63be/hihAtZ17hZpOPi6kiNztHMkZyzgQOztyfKpvQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2529, "LastWriteTime": "2025-06-24T04:30:27.9043418+00:00"}, "+HqERaQ/9ZJjl/o4u2Tw5L9iIIHSZ3kdhpOJqOFWVIs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\v8iorw1ao4-74uvd51634.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dgxvrqhl7c", "Integrity": "41GOXv3/61q6PAei8t3AYDrEns2K97f8ymoD3ZV1qyI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2295, "LastWriteTime": "2025-06-24T04:30:27.9149537+00:00"}, "s17GdLZx7t4pX6taKmAAhvx5kVjioSV0QSYszPIoMjs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f6wx6gd1gt-su7r23l034.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0rv36z1ex9", "Integrity": "hHojCn0xiRCwWyLrslxq6PClbL7a/Q2omlr8wJOxKcc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 514592, "LastWriteTime": "2025-06-24T04:30:28.0159026+00:00"}, "T0eKwEd21upfeERFp/Gjjb12oAp+JuLwWxkBiQwA0Qw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7x27t74nhm-1yccpcq7id.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zjhdfezxp8", "Integrity": "H3FtXAVgj7swGReRVg1moh35L+hmMgm+2VXR9Lkelvs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2255, "LastWriteTime": "2025-06-24T04:30:28.0301564+00:00"}, "1/cOHvBELGjJNdyYQnxY+8vX/+g+Fie/XRl/fvXQJpA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ppfdnigb7q-55ue31v3pl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00652p439q", "Integrity": "oPNYwXcRoWl7uya/HXXYKERu94fKm0CZJ8hFL0CQFZk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2228, "LastWriteTime": "2025-06-24T04:30:28.0587344+00:00"}, "MxvoFzqa+5URGMFJQ0MdpEIFfxwxe7QlJlPw7fmYaNg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qdvfj0qa9d-akbtg6mu9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j4oh12472a", "Integrity": "nYIailQEVdO/2dsRaY1NdIhXFXX7uJcdj6Ucd719EiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23478, "LastWriteTime": "2025-06-24T04:30:28.0797838+00:00"}, "IYo+oC7ZD6VUNn6hbEn9z+lcLBSNil+y+zXlpxkI2TU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cn4p7z7pyl-c5gx1pj8rz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b1rbk16ps0", "Integrity": "YcohLbWIfGPVOKk2RB5lRX3xuPV9jkO8yA9rXZfUDBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 200887, "LastWriteTime": "2025-06-24T04:30:28.2415118+00:00"}, "ByVMAwYEV97KFo32nNV9X89bjDSVkXjwxc/pO5zsaLM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ipm780nllg-l1kk2a4a0t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qd7nlb140s", "Integrity": "4il47+VZ7DWYRBb7ei/QcLUSSnRYRpW6GrgUevs64i0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 149516, "LastWriteTime": "2025-06-24T04:30:28.0290315+00:00"}, "WIsqGe+ln3xlg/RLCDA9vXYhxR6CrMW2zF6Fcqk0uls=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jef7gsrqq7-1qhdretbuh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hphvgmocym", "Integrity": "m+yGXO8YqCr6OeBaPgpxUw5KkhVZfiu/UFDLmIz62Lk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 19241, "LastWriteTime": "2025-06-24T04:30:28.0632737+00:00"}, "flMmsPgTa7TT3dHwm6UHlmDng0ADwBDKhCuf/sJbKHI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\laazswwohk-609xepspin.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8j4xfz7b8", "Integrity": "b4ces5UKtHhTT4focWQsQQ4peWgUs91fjW2pXqwd5qw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2300, "LastWriteTime": "2025-06-24T04:30:28.0797838+00:00"}, "ZyHDWo+pXtv6z5O9DTH3nr5VGleijxtNjE1EJLJU5Po=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n4x5zi1hy5-emm1p2vibe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1o27p37ksp", "Integrity": "v+EzzPbHp6+i/owi3b7X51qyihZHGtRtET6End+DclI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 73778, "LastWriteTime": "2025-06-24T04:30:28.1740287+00:00"}, "LM5rl1aviXHlfvs44VbGtl/CqZmIYe+w42WjJkp1Rmo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\awerrgejpl-68n5xxnez7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o0e1d0n0id", "Integrity": "OJpuywD4IWo13VL/M7DVr0mfCvWbnrbURkle2C5SC9E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2285, "LastWriteTime": "2025-06-24T04:30:28.1871849+00:00"}, "xsOkJXyVlYFjj5U9JnEUsiNGhSAy6TdtUCIBD1mDUsc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8nxszxol5d-zchzosoc8t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u4udsecrro", "Integrity": "zu3s3rgiI/1CU8rvuvxPstyBDrDdSOOmmY5JscBhKIY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21575, "LastWriteTime": "2025-06-24T04:30:28.2518849+00:00"}, "0nJizzehEPP+L1aWBR29CxNVLPVMX144PisdJ0Msyhw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ezx0izcezf-mzcae7sc1w.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zt10t3qa5g", "Integrity": "R1Ji6MJf1BoMC8mughzPcjuSJd2ofWbxqP3nMQIfnhE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2547, "LastWriteTime": "2025-06-24T04:30:28.2714794+00:00"}, "fzXMoobAQZYrelnQ2OPzZ8g0iSMYo7u7FoxeNeOk2Ys=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\x959zs3735-av82mpp65m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1wbw7xae8", "Integrity": "+KKEcm6UbopA25E3V1r0Hz9bpPELXokumhb07RvsNAg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2324, "LastWriteTime": "2025-06-24T04:30:28.2839302+00:00"}, "adTIl8BGv3FH+XgF1w4TMiPH0GpGrwtzZsZXr2aZpGw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4pab78fge1-03z9epkgeu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8685rjzp8e", "Integrity": "YLD6OG8G5k/hY1CmycBl2TwwBXlGeAf4pkos3ZLQ/xI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2249, "LastWriteTime": "2025-06-24T04:30:28.293558+00:00"}, "5VGirRoufy78AW3QmSZWAtOQhqIC7B/MM9LN5+lEbgY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hlv81p94us-tcb84raxfr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ir72gb1chy", "Integrity": "zr8zlBl43Vc05ZggODe9cCOPjq/dkWM58HYX7ofISKQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2122, "LastWriteTime": "2025-06-24T04:30:28.3099752+00:00"}, "pXO8r46Wh0T1obBSTu1a6UDsaEC95tTMShOgJbkXQDg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lx4q04c6vo-j0x2amkbr8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azdx7qdg1d", "Integrity": "+lvTsQQjzQqjIL0b1dDp2L70w9zLut+pm9Xgk05SGto=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14271, "LastWriteTime": "2025-06-24T04:30:28.322856+00:00"}, "yXzatxDHgfgmcOwGsPpNMzlx3sIX0jlXUD9fNM5H18c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ym2sc23egi-3mr0k4ql8l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "07vh6x72bn", "Integrity": "LJISaxlzzsg2/QrGJ9sXmDlBOSkXiCgKIZYTyakiYxU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 51105, "LastWriteTime": "2025-06-24T04:30:28.3490721+00:00"}, "lyS1giH4F7uGELcAkUEhlooWbvV6NlWGD41Cb28TnP0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\e0p52hsb7u-tebc67toix.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7o4w1c655", "Integrity": "d6jaxiV4B/7ivYnqIJEugtxkYC0Ke2ySkqb530zduCc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2360, "LastWriteTime": "2025-06-24T04:30:27.2026129+00:00"}, "VSdlDplHKSzqsuBEPipAWxCxzvRGlK6xBMxXqviDAm8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\axs689n286-mylfx8o0as.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gb3ss7nrty", "Integrity": "zEfMTLs8gexhvPxrVpWgS4sJWpHVlQvdO3NhEX6Apdw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2168, "LastWriteTime": "2025-06-24T04:30:27.2175507+00:00"}, "sAwhgOiLswtv40KqEPubqM4D5di24VsKXCHtXp7gK6I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9l7ueuc70-wj73nmpklo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3pvi7gcbsb", "Integrity": "CWjbsZdyuHfxzuNNN3TW5gU+J85Ei81pEUkRqOOaZjw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 8370, "LastWriteTime": "2025-06-24T04:30:27.2442637+00:00"}, "MB6mw6FiKGP4oGN90+UEOmMmbBeTcLgqI+PInLYP+LU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cajl2o1e7z-fxengc3wyj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m0kte91qtt", "Integrity": "lix+/U0hL690/ReuuOA+NURVSh0IVSPJh0gK2WN+aj0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2111, "LastWriteTime": "2025-06-24T04:30:27.2649784+00:00"}, "/N21tYSiPyZMbxhdJgaWU8bDzSy5CnsUDVqmpcq7LEI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sadg8p8gec-nphd9f66p0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zzh8nxx6oi", "Integrity": "HzgJnoptUX805KkbZZpCJGQ1DPanHrSj/0of3xNCaYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2266, "LastWriteTime": "2025-06-24T04:30:27.272499+00:00"}, "o/f7tLY3IVk2B8uE5VTM1y8UZwdl+lpuDWdvGfCgCxs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzgeww6nnf-8cl47m4jkh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yoebce8vme", "Integrity": "IuOAYSA7zT4MekVsU+fb6GR/9hV5tJSlCUI8cf4HB74=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2197, "LastWriteTime": "2025-06-24T04:30:27.303112+00:00"}, "Im0eSZguUXNJ+whdehbAn3RpGaPx5sjatML2hq6bAJ0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bdjfymnqrw-3yhl940msb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sfnr940nm4", "Integrity": "aKii++1t3eIFbfJ8kxXNztrAmbDf5lCnEWFPG2OiWPE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4009, "LastWriteTime": "2025-06-24T04:30:27.3491674+00:00"}, "RZlDz37H2uePlpK202oFMb1o2xDLPg1p6WRGrJXDQ4k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gwugds5sw2-75yss9zhoy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u69fk6s3z8", "Integrity": "ujcxkCNExrbgBUktEcT8sIcBnXCAqDQpFYtDsYr/DkQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2228, "LastWriteTime": "2025-06-24T04:30:27.3725241+00:00"}, "x56cs9uV+qZI15HkpLa2wSA8EHfOEIKJMbuB7kLtgj4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3kjfjnpc9x-i6esjxgs2i.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8k<PERSON><PERSON><PERSON><PERSON>", "Integrity": "OP+3WHGSLQ714XTMeRxcZYvBQFKxupqkANRGFfci+hI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2385, "LastWriteTime": "2025-06-24T04:30:27.3922276+00:00"}, "+TMbMTfhIvbTvQIivvSTYmFSTbpol3HLjjxRYJD3xI4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ddncr1chdl-5xzb5f7wvr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3q3icw7s5t", "Integrity": "W1+mdvDOSqtF5vDo7RbvpKKHcGBwhHhtZyebziHx+/U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2482, "LastWriteTime": "2025-06-24T04:30:27.4146249+00:00"}, "X1qJPY3PxLaLMPpSB7HwsIsZ9FlSDVqJsh3HFb6ZPOY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tb1rghhwn6-uk4ktf4545.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkoq6vbpco", "Integrity": "7ZB8I+L31+JpElgTdmIsmbYiHU8Hq1NNWq3E2hpxfnQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2308, "LastWriteTime": "2025-06-24T04:30:27.4387031+00:00"}, "txQHkl4E/pyFVDNl6mYBHn/VUwq3jkXLO4ZMpo1OBsA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\uh9y7ps724-ngnkh62a7t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qipu9hrpsw", "Integrity": "sln492p7YGgUlXl75mNzNha0rQHV+hKkNEaF22Mv3Ro=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2348, "LastWriteTime": "2025-06-24T04:30:27.482979+00:00"}, "HHstldlyjUidfFog2DZuKGqMJB7uy7HN/Km6coLfWaA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dc9ab4adpy-tyrk4ywyu5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqur2gucz9", "Integrity": "azbHFvj0G2FOt+zpbeKcqhQJDzyG/31RNStqOIkWSHk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2843, "LastWriteTime": "2025-06-24T04:30:27.4964778+00:00"}, "50nUCRvRB+yZWlDOkYC35AlysOjaLIdxvzXDQIzhVoQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7qt3udra8g-ekfbtxon3o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mazk4w0jj", "Integrity": "f0TlS7a0kYBeQmpINNfz0Ay5ZCIVQoqSbfhAaFIHInY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4201, "LastWriteTime": "2025-06-24T04:30:27.5168815+00:00"}, "3Vua1Tm4M9/EX+5uPbdSxJjPfjS4L4W02s5iUSY1pPU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7ycekvj0yv-4isf5pcuol.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wecjxff8x6", "Integrity": "aO2H3P36KeeCoaSb5fPLtgdR3F6Si+/dCry8yVAOyAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11741, "LastWriteTime": "2025-06-24T04:30:27.5735034+00:00"}, "O7RV3F+EUZSnHLmzz4N7n1ChUvXECd1TUCp71JdmT2M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xw3xqrs1gn-u7plvjpova.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0t6qsm<PERSON><PERSON>", "Integrity": "9BjRdvYM2FEgBAfyV2JzkFm8EfyJfyI6Rpdt2w1ZHmg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2513, "LastWriteTime": "2025-06-24T04:30:28.0797838+00:00"}, "g6QJYlx0+rpJN4htB6O2kiZb8Tz7/Gu8VJn9HnoZTJw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xv769p21qu-epnagkv91l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4kcqoa988", "Integrity": "5IpB6j9TP4oWcspGDVI/FYwRJvK44Ca0Sglh/sypQz4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14744, "LastWriteTime": "2025-06-24T04:30:28.1170645+00:00"}, "tK70dxUcoBDdOwja8e0BBTeWNe+0wlP/q4RsM2DCIlo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iqwuxofz82-u6wau0ktb7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b12xqtnz2p", "Integrity": "cVkmRyxFNqeMxwYJp1BTXbZkqgQXQz6TfXtHtPjyJkA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 25996, "LastWriteTime": "2025-06-24T04:30:28.1902171+00:00"}, "JK3TDOzpaWFx93bFWqz1R24yYDX1xj2UYV5yviEQmbk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0tq3ubgpl3-710onjtcga.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wu8<PERSON><PERSON><PERSON><PERSON>", "Integrity": "WCtnSrphKRoxTEOreGgMgEpNFHYkj/yE2QhdPZPtHmU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1394567, "LastWriteTime": "2025-06-24T04:30:28.826873+00:00"}, "TKBj6SjvGIQU0w2HlI3SA63Zqm54SIWi+0QoZExZuaY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\12gy8c80sd-4rejzitfsn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wcqmrrwexh", "Integrity": "EApuGNk8AvmCwl07TLFJG222JaFOdNOcFoqhIUQeBzM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 11445, "LastWriteTime": "2025-06-24T04:30:28.8413397+00:00"}, "EaoVBqtXh4kKyU9Bx1bI00KhZMMswKg2RhvRHZ7RkbY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mrmnme9fy7-3gypxjnyzv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hllvpd1438", "Integrity": "62MDqadOj+/GIGmhftV1peTz0tId0vCBCXCHa38OKb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 18726, "LastWriteTime": "2025-06-24T04:30:28.862927+00:00"}, "e90vZTvxISRG4EjZsT+ZMV3E/detreWysq1hWQy29s4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f2nmvc6iyo-vpgbraawsm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2hpdksa4ob", "Integrity": "NuanXv5ObHQUgmzahq06eVrXcG+WoMunixO8gmQKGMQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 36160, "LastWriteTime": "2025-06-24T04:30:28.8841615+00:00"}, "gyfKnFt57T25rRc/KT9Ru1QzXC+ACK+in2X6A1cn8SU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\92k36dsjrc-61e7sdubtr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ahan8gc0vy", "Integrity": "zpxDddN4c/cNCws/EZSTYxW//AVvfrNfBVrupVC31kc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1154460, "LastWriteTime": "2025-06-24T04:30:29.2313381+00:00"}, "TXIgBuI6rtrXxNl8BHuvZhHWA2aLeeliWdXcuj2NVb4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mgqvc88zci-ywccyuk0ea.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvhsn92mqz", "Integrity": "e9swblY/HDrM4H+jYQUqr8xKrdMNLra5T0+d7BOz+2c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 64973, "LastWriteTime": "2025-06-24T04:30:28.1025017+00:00"}, "Vq1DFgvyigoJeVyHCf2o/WMCxzobJ+QoRdPB8EwLgV8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\v9ucdunf1s-nmlfvbueei.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilzgw5gy6t", "Integrity": "yhOL83W6Mgf0jJLwHzMZGA4NkEfwQoUJmCpJRya8LBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 100294, "LastWriteTime": "2025-06-24T04:30:28.1871849+00:00"}, "CJZg5Tu+4QHHnqJWbu1fbzIOYrIRs8GXb3cD6kM071o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\90hp2p71hu-tjcz0u77k5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "su9h2nea1m", "Integrity": "JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 333110, "LastWriteTime": "2025-06-24T04:30:28.3728433+00:00"}, "N6tqc8cl+su0BQVMxcj0aL1qDCjEww0uiLp3CpIx/qU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iolod2it8d-tptq2av103.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnxfkgr4e8", "Integrity": "G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 196037, "LastWriteTime": "2025-06-24T04:30:28.4847938+00:00"}, "EchoibOnF3A/wlQM1ambbTusyda2keRr9rZ2A+8S4AE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\j19hqecu3k-lfu7j35m59.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v385ycndre", "Integrity": "S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 317618, "LastWriteTime": "2025-06-24T04:30:27.3154856+00:00"}, "+weF0jmdg0FWv5y69OfI46R8OEc/iqRrhSsuNou4vgg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\i4ozfjtw4i-oyz0vx2fzb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/emcc-props.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\emcc-props.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6rw5k6cf8e", "Integrity": "7t6AVk6lvrWEqY7hRavzlgS107PQ4doQEFxFK3dDtRQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\emcc-props.json", "FileLength": 592, "LastWriteTime": "2025-06-24T04:30:27.3362849+00:00"}, "hYm4LiI74ZLDSj1yuaZoPb1Zrpk+wQjmBHz/VJ4n4kI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0qonx4ygs9-262l5lvhqa.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicCommon.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n89ocf3hya", "Integrity": "OSMyUbqcI6/KyNDK8d8+xAWBInzLW7f2QJlsi+vQ+s0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.wasm", "FileLength": 5426, "LastWriteTime": "2025-06-24T04:46:37.2615138+00:00"}, "hngQul6pC/8l5N9i+GtBDdLlw5S560zXCqqO9ZYFRlA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\esi1du385w-da2bufkpgn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicCommon.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hs7l6f0say", "Integrity": "T4P2tgs6zXXLjeD2uNdKnHvtKJYqwddoWqkFBRqBcmA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.pdb", "FileLength": 7717, "LastWriteTime": "2025-06-24T04:46:37.2707043+00:00"}, "TY7x1cPynA+sfiveGwcicweQwwwLKUrxmqCam1rKr/Q=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tga4xb2zqw-1o0v3a4mvv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hb7b1ohp6a", "Integrity": "M7U8jBf+AF2RkAbNAZuurFbgIvkZE24GyHblYnU3Qbg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 9458, "LastWriteTime": "2025-06-24T04:46:37.3042478+00:00"}, "S3X08Pkek+Q0A5NgomGDQlihhd2hp/CADiNXIrYkXZI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bqkz6kxeo8-sax0uiljhd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicApp.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0e0p7n1cg7", "Integrity": "yP7vPw+qkmbDGYoxrqF9HJvSlgevCHt5QMns6CvzEcE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.pdb", "FileLength": 34380, "LastWriteTime": "2025-06-24T04:46:37.295904+00:00"}, "/Y8rzViMuoR2d0IEJQjH9+msG0phsokFRJu2Kr4LOZE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xvf58668z8-tj6jbuwo08.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicApp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q3avlrthph", "Integrity": "5tVzajbcqPg9w3v2+uv/ezfGxokwvrv7csITP0mD5PU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.wasm", "FileLength": 31630, "LastWriteTime": "2025-06-24T04:46:37.2854851+00:00"}}, "CachedCopyCandidates": {}}