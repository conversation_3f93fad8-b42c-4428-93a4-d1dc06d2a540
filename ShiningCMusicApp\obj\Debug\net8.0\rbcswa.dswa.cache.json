{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["XJ37zCqLhRE6y65Tx8YN9BrV+lymfYGRE8OIAdui7qA=", "F/r+ENtDuoWBUB7ew4qqaq7La+nxFBuEIgMPPjz5JNo=", "oWV+AOvkU+dDztPENfEP9FFjxHGgMQkMKbeqdvDdP8U=", "YMP1x9E62f7DJQ5i4LiV6Psb7RO0mli3JJtooN7Uy1Q=", "qvvVUz6E9IWL7ZEy899gZK7AtfJph7in5sg5WfAYK1Y=", "Oe0qcBhyA2jIrq2laqQcWhLdw1vft6tyORtxPHBvqHk=", "ynu5fJllAqHLydCcsVReBf6lLgEYATDnFIfbkNNGGC0=", "nDX/a3Gghn5ZhusR5rMAN+HXBbJh5PmC5/osIwpdc50=", "ZmBy16R5XrHdzFgZ7zQTxvMq5+QiqHHlOEd01tsx5o8=", "3T+A+gEG9A3ru6TIFXKfww5owi07j5kusFI5X9XELxE=", "2PrtcAB4APQpi8MTL1qJbopeWAoPvl3fbbjl/FN5ibc=", "0HCGJ6Vj6hlQAafPLP0ZtBA7KOFdO61jUJA9VMgqGPI=", "ZboB52FIMk1uAkUfdQpCvOqff0SgVG8DmQgxsnS3iPU=", "HK46iKQBxRmNpfVIklrMHI/uxAj8pA1PAdCGdRRZO0E=", "WvBbw5MMH8n2v21AS2PejJbm4D3K45DbJmVN7cHrC5g=", "pZMoG6OWnkB87fBi7XmnQ4KAXbQGRTva6JGPo9oIkC0=", "BZVqbK7PR/ZOUpodv2q3kpQT+RuRFZdJ8I/RrU/ffec=", "Ai3cE+slKa+jjqMOlEQ/4/hojFhLr6o9y2+fVaIpr4k=", "kOzqLsrUhHeffbJEoZ4KpWFu//gMUazPFQpy+CoFS4c=", "8r2MpPX0Y+j/2LDGYS9KkQ4Lkn5dntVXkqjlvKYSD3o=", "/pJlWrsrs+5PfvWh4ZPzRoH+l9Il5ICexr+4PvJ9dVk=", "Ssf3hFl8vTZgJojPi1TrtFGKt8DG8knXk0wGqHZ29jA=", "fb2rzmLf0D5WGpFjSRoJxtuwmPIs0RGB+Xg7a6JkGv4=", "JtjRTZ7FMlHoweRnzTntADCncWz90GaPTAevf9bluWU=", "FfeIC5Q6/ceiMCDf7GMQaS5Pkb5kWr7XiO6qhjTRTLg=", "qup2qVhxFRMizygjGDDj0mGjRrvvQNo0nxtWQdLLc+M=", "EilvlSEUy8EGiJEIlm/sMdxxds2b1UGR+iL+T2dzYuA=", "r3ka3yGx0TBCfacWc3eWeLLgJifetqICGXr/cNeZ0vc=", "iUTOXuz2IrwbfezoZEkEdo5kKO1RsQ5XIwSCpcTZxDI=", "yAGvmm4Ot0iYyzVWm1Wa5XSqhgUww2xXBgRRdtLCRzQ=", "7R/VfM3TjCkFf6mOFhCUU3lScf3IxSN8QjiFwVZ7lXE=", "OU81wfn/9GNF5XmjjuqJfhdJrgH82qACXOgfZ+kM18U=", "IMilU8SR+dUgpY5gRCjA3xTdItNfi8h4Sip+krD83BE=", "6veoVFNf08Mv6xT3kzERx071R7DTI3FRyofU65NDrko=", "vUNogsuoDpgLpj6/H+vpnkc9ur0MIbMk4tCpWfsrjGc=", "NqeNr3jJFq0WldUq5nhwMkNDdOczwHnLLdWwfcBQzYY=", "8jiqf8nql+u1BUOfhH1Kt66+X20Ez4UiuBSjaI6T710=", "/6rENURRBiIDVtyWGMUr7wmeVUuFNaVgY0Frch6mARQ=", "yKYEzj6XLJOBnZiAZudo4sntjZHuuz+LrM/Ef2q0A8Y=", "u2Y9WpGTiPNlY05L5H7YFTTpdzbqenTwRZce54Armds=", "mO4JM98bJQRLBWEbb1P60W6ZWy0eztSuQyAKQCHAVc8=", "oto0bGU/YFb/LYWoM6ZnWViuUW/eZ6VKuI0QAEq3Gak=", "/B9ud57bBzYrzmolP5jxx5RhU3vW1QrW9wDtslKGVms=", "PjZNaGYbjTURjgAgxO+2gwjFBBCA9OyBinY6fV63LTM=", "+LghxvygG8zodmppIBNpQw0AmvIQ0luk6IX45YNYVp0=", "N/DKfRJlVDkwdD/aHTAp9D2Bjkg2aqOSlxzsoyGv3u8=", "mMwbkbJkXcaWfe/eWOKxsn1sxl0cC8SWRJ8x/m6OHZ4=", "s6li6JvMCZ3as8hQ+NegGkRug9KgEm3as6y6/bkEX+s=", "XPEJKP8JGq60f4CCN+yOXZo4Oxe4IBr0WLQINzAYn4g=", "eJ7bNGegk8pRXitijfES2J9XsVWgeYxvmfn0xML0aTY=", "utQ298dMiNM80PV49zw6fRE7bICyzCyyF1P1s19XjS0=", "eP72YRkhtEyMlUdveWuESUctRO41uzpg4DTizXBpiRg=", "G9BW6ncVduOXPySIHh/L66Fr8uFp2JnM69+ol26Ih8s=", "yP8a6ueIEKuJ2s8tXlugo572RXGs7y1oq15V0+5kZtg=", "qPjp74uuJmjJWBRzzgroO1e22mfs+8MtUbrti7fXfGs=", "ihguYHSypqd6iUqrZkasB/pPqf2s24kDd6PfU9DoGqE=", "XIiQjDLdhHK0nK7P+p/l+P11125449HE30iXLTRCegA=", "eFLkyBswhFtTKvVun5QnSdfk02Pf4ikBqWHMlWdIG70=", "E9Y/R0hsGsDVqkwHPG16GY+5Tb2PA2LRbqtbNvY+J9s=", "9NqtnOY73F7jz8FrZlmaGIxV4gZrGCp2fj/WKtQi+q8=", "biGcWp2Bo+B3YdCaN9Vdc3o+FGRGMgOriVPJqgrOt9Q=", "KZvKX/zlRT8N3geSbKFVLD01KUa4p8sPVGS4Dd3ppTM=", "x9yTSgWPl7IbyNKgkGGpfvUcb/EoYWYqF5PMTxiNv74=", "H8VMU3kFTyxMLNw7TjzinZNAqtsIYvyzVx77g9mvYYw=", "mmoiXSTbhEs1LsKbha3YIMGGOOBuKqUagkMn3Htep3k=", "nmK0sA0X5xEeGkZ85Cb/ffVsRhnFJNHnPw6pSefM8f4=", "fU8mTnPCbcvjL2VgwflsQYPMW2483t6Z9kZ40SlDG7c=", "x0pTmLLqXt3pJAjmasW+s0ThFrYs49gULXR8PU2REM8=", "vFQosOmT37HU6NukhgM6+7/RRCbOqZpd39jve7cW3Lo=", "FqalpLPmt4xL3QHCQbrrkboKel11JY9AFfAh7YJFXCM=", "Kp+NA5RI2Rg5vkgVXtTCUJ/budJkqbT7DhgLhFL8n4Q=", "XKnpA9gEx8h18YngoRnhcLdV1g2Mvj4RFHH7I88ACmU=", "CT/9eizEIclNhRaAu/A5UCEihykjSijUKRtsAosBbts=", "QO4tb7S+DbOxfrlRtiTc+0fvB9OHG57MIlGb6SExDm0=", "mQVI+Xle1X50mn02Gx2pEaGLQ/f7TgdLTcF/Q+isr/Q=", "kipByAukgoueksAeEIFsYfSHf245evi+okNNaTLVIX4=", "fS8wXtl6sD0M97WzdZHkGikyP1uNRul6YknVrCc95Qo=", "syRwCaRECnznw7S+i2y48xJPgOCDsefKEWbz87J03go=", "w2bas8zbycvGWHRKb1SIznUxuXDq6m/dycpWETFoXQE=", "TOSQrzKF+Ibgx7l4jkXms5g1Sif9B/F+Q5NktWhpTTE=", "nM37qD9v5kcPPh6x7y/5mfgt6fQUPbVQ5is7Xwn72+M=", "02DKObDqDSloY4yIffcMLffo1JZNam4kAVLuSxOzy8M=", "D8U38MjwET0nG0a41g9RG8qKV1gyYIFldvYYWFhZTEU=", "g2qspsUWL8muqOBxFBnbrJidMHtW7aRLQAJImYyTGA4=", "ZaagxjA+9k5Ni2/XsPnwus1lpcHCx/iBiTBV+x4nuJg=", "ycTAlPLqK0TlBJbr5n10KJ0UgMebaqORLwUzLM6viAQ=", "EQLzpCOp5bGuLk4p2k0K9smbnyCnScVsG38UpZ49pwo=", "q598o1xfh8EdTSPMi/YKqS96c95NKecIdrxkp2BrnG8=", "MX88w0Q5LhGkBahbbneBNOAymWtqQm2+yvdsovrdB3c=", "JB2Ox3BlT6imAcGQNNR58a+wl2PTXqmdvihaOEhEpME=", "oaShbFHVXCad0ikIel61MdELpaynJ/QeYOVW6uKLK3s=", "1RexsMBtyjz4xb6nz8yjElV3gj3BKbkryrP+plBv538=", "0EJGOAyDfIqSbiFQnlBRkwwznpX2uhDwGZnsIxjzLXo=", "ZB9n2MMbcgrzMJiFFCzwlYnU79ihwqcI9bIlaUm2GTg=", "SJVwxu6Ct/PTWcuiawZiDbVkgkzg7nL8tPBpJ1SUDac=", "qjw6OGG32xu7FFIOIBGqTUEOtWQAqI9DvipAsGwOmf4=", "LP+HDXdfVf7W4646LJHoTtyGlT0Yt3S//kQGW4jGx8E=", "y1C9jxgyd5iAqTSMGvbz6aP57AOgs6Cxkx8e5xwaYLY=", "jFdsw4kMMg0zlDgiNMFbfAqorQ+Y9HDi3AJ0m4O323E=", "IQJhsc96+ni5u+c9XNZqlAIXFPgGu4JRe4Yke9XyXvk=", "RfKeBYxL/wURIEzUg0iK510fTqZtLserfqhHLyUne7g=", "zNnlLvo3JxzhPManpF0/ZENTI1HWJYC7+wQ7nn1c8po=", "u+ua5iNzDZghrYDdKZnGaXQ7uj99dTrGFHulswKSK8o=", "sge8ectkmLq3GcYYXQs1fnZ61pI/7RG9enxhkR5MPts=", "WTFt6OEzNgmDbpbiHuuEVHS2LmWhG3Sf7PYiOKy8pkg=", "J9oQEBbRZYWzsrbXzHqyOvjkXvLroolbmVA6bnqIvHg=", "xcvWUDWkXfJ1jZhjCTzPU8PUUp9G4e9enrzBuesMS7Y=", "azuMeSCQ0Y0bGnyfbldVxD7Qu+7v5hQsiOFav1SrcfU=", "uh0uW0B8FXY8jW2+hnohlaYw1DscWx9VTWBsgWJCKy8=", "U8RQeDVhEqKNHfa45zqWv3DL8+VqvSkCSBXDiWMOlkQ=", "g5mSaO+rheJdWs/VKKRCfRgUnsjW3u07vobd2T8rasA=", "XQOMYCa1kHjlt6SGloXq5pGwIgTiM8YpdEGmXCEfQBU=", "se76SegtkM1unJtMq1bFX9chQ5VUcTSUAkyOr76TLbU=", "UoBs4uIPOsCGybRj75WTkg0YvdxvGj7iCHBiVWdd/Oo=", "gEVYKgA4UntZtZd40BNkjOdilSZAupt+Hid6uEFrNS0=", "amI0vkwDkgTmpMxLd5EYzJjZWOYDOaNpEguBSibQMik=", "7IB2WgQUrPWddadn33WFaVBAx3Bn10+26xEvBlcuTQw=", "0a32q22wSKVzy4XYPZ3LAOWlcDHco6KFx2ZqkhmdUzM=", "HtNkf5Jn5Ajjc6aIUx3P/vs0MFdIflRqbUF1VN0ehvk=", "65Ig1Bju/O+8NNIFbTDLRTZ3oskiQPLj0LKorjUzpi4=", "kQrSsMG2X3koGFL/woReGtrFZASrxGVMeWkBdM0TAM8=", "yZLauQF/qJ5aJqwjUNYaUQClSvp0Mykm4sAvitS4wS8=", "IA7hEk94SG2ObCBH6Ht4qEhWdnaN+65ncD+qpcYU9zk=", "FQTiqSYFK7+hXps0t4c440qouI6KSkjEQKRqKo0o76U=", "mNz6tgm2sVFHGJ6e4U7UX14zZne2aXqFCRtB7fQ2nlg=", "+HibjeeDoUX/1xfW6CUkL+e4tTPBKD2lxgQUSSgRlM0=", "7um8VfD3ha2GUIMdF1WniuF1Im30YWXgCqDCGD2RGLQ=", "XKWF+5orqPC2mVRBr+zjYQLFKlURsEPVyOqS1/npKg8=", "eZ4F5Mz9OBRIWUVv32zOvkgiah3dC8WTQwmnlYsZHIM=", "IEMUf6ri4AdSUeu1DkagQCSPPyanYrDla/qV94UZh+k=", "7wW++n9TLJ/ROcG8Kjkh4MVbmeQaiVNQrsg/lMGXUvQ=", "Z3wXuQx6pip8KoYb+MoN+9IZH3/VTzjVyF+bHM1zORM=", "wmG+M5/gpLPqbNm5/B4JFujjyU8gRuVhiI90bNmXYn4=", "Fumg70EaCQ511mrIqz9Tb8lAm265lnCijUyVWRn6fNc=", "WyPSaWrLrDaElYdNMNM13Uh+foo2YYiDXCV7ylPGisY=", "hKZtAulb1VgfvJ/tk/ydSSXmz/dM3xUebSwQq6OT6YY=", "nRRyAvbCYx9nKTFe+1uqlqj1WTjbAC2K/73e5JdyY2k=", "jFH2Ih+UQfuf6qRpHdWqL/Zbm+8J+wqdF5wtf+oWMsY=", "Kdiz3PIs50g5kYeCNCEZm8dcXS/uh8BbeTjjAapQ5vo=", "02GPp/fKxCpZHslXNK1kSO2whr3DVsUDfChfeHWb7cs=", "UxIO6OrHJV0ZvY3Zjx7+AbzN7r9Gq6ACaDKjYjPqUAo=", "ddw63ljMYfBe4ByoCI6hFAomUe9AsOqXtBY5gI1NTZo=", "C4lX8+kU3J56z3qHY26gEfW7DqDGotLwnGZlNOHGgIE=", "wrEPBJKvPYCZrGHJNUsZ4xfHidg8NIFaPUuxscBlIik=", "bjUYvjlOXejWSYWTuqU4KJtkf2xd8GT1sPdU8g60wi0=", "VXM7gWdYLWa4PoE0ALaTjKYETWLL6GNTcF59VnD1JD0=", "EMSUvdRDzc6M8KMiwN93YL0pdgygRLA4Zp9P1+c0IB8=", "FjGwgR3bRUbx/Sq1KlHRqSEz8YzA/VSQutZyRoj8lGQ=", "Fw2xA2vjmvfHlREy0Xr8/cNlDR9WXlTzBBeUfAyhQ6U=", "s3NqOwnvJxBH/vNWlOYbFueOfniEHzf2vqZ7gpFd1GE=", "lI6YdwN/QpmwOk90oCvHibSF+VTGZcOSKJdswu87WRA=", "VzwZmSV0MWrN6cK/49wmlPQILXyFyOyr+c8WCOtnGIo=", "zFrHAbu8yawCdmtBRr2pBMyyJEjGgLnomHONu6MPt9M=", "dfjG3texyFVLJXsIGOABZ39h/K6ZhWEVxjQGToTMpGc=", "olsK82KeZYNEyOXzjtLj5buPmn8VJ5U56EBxWsNVtxI=", "229Od9yB+F+ph4h2vjfU0hicwN4/qup38PVCFEYJDNE=", "ArLwDjXc+itsEcx/wU9algm/swYOGmxy7G8zY+7HKrA=", "rsMc7VI4PyoWMnbdenKPeAy1/YEz9TZ7cyIHTEVvGT8=", "VbsUVx+z5BVTtnq39+wEaKJXCsajhDYp7rcGWxMwcBg=", "lFDkulVvillks+KmyFLEG6G+xIBfGpcJMYQnW9tUMrA=", "iuLWeUDtEGuER3I31L4DRgFfb0t7rAe7rCcaNRm0ZAk=", "Ljew092sXuByqE0JhMWTkyOKtnMri6+Ql/z61WFJBJI=", "HDElwgqMB5GVCco8Xv7WLVho5OsR8TfsqR5BLjxnA10=", "cyo+moE3B7rLzRs/nEBxFOpztmMpUHe2IzrVP5kXMjQ=", "rxeHet2XlN9kQdw/w6qT7/yDV6Yim92rhnwChc2JXi4=", "ouI+kASjOEVyhwNWixPRdLBRe5ahrOn0LIP/UGtEKb0=", "21IRzMbtldKB5OSZiP9OXBYIMADRKYGTuXVgc0dFwLY=", "nq7k+syR1727Pzot1MIUjiXkEt8V28ZghZ5T/rdgFD4=", "5qcdvRHoJcx8ATj2c45QUMbGLXdxKRvxeSy92WXfFuU=", "NMjU0avXnWsDTM7ezx3q04M+Mxdi90LOHKmM/xfIzpc=", "nvYNf2mC97cmakf1t0/6PDifx1p17QrQVY2ONF2OYeY=", "93jLV5FXnZcL5DCRquboBcsYu236YN4JA03uqj6uO6g=", "G2n8YWf/HH/gPAfp2hlt1nYMNFhWq5k5/ednWfSHazE=", "rH22G4/d5tKtONZSeOZQkN8Jic7RiLiwNV66y2xBe0g=", "Zxr/K+nN+zgzNveIVSN71gxbDxUZKL4/gba6Ayt67qQ=", "bbjM1OnsfBg4nbmBvAp5NRftYPexfbgr6B/kLMz43CM=", "Vp903d/8QK6XwIeyzPckfUp2iQ6gw0b8eDKLomYlhNs=", "QiYVHvMZREprK0xX+rQm3ylNBuJlisZkz55Q3wjdcxk=", "gR11tQz4SHQIkhuKZY5It12bxTqb53L8RkKKlE1uA4A=", "VZluyEEoPGga5jnX25Ix4q9gJMQE22KPoax871c+hpg=", "GrXyvVAlsfqaK402yA0DR22AtV3wAUK1eiVldN3Ht38=", "Yjybe1JNuLrlMcPFqvpYQTLbL5ufAE6XgN8xeQHdI0o=", "EMzLmawnvfSPIvt94DYl6VWpo/nv26Al3vNlNSj0bmU=", "K7QyLSbESyl7yd7rsz4F5OD+ULiehT4E7+MX5hgnVN4=", "eHzlYstXaOa/dfyarfDvgTZZ3+Qg8tj+BU+PIU7PO+0=", "6mv9UhOnt2mb2bBSXaRf4+jJuF2DJv8H1bkMCDJuRcU=", "N7PfvY4CavZXjKnad58W6v4ZQ5sSZejzGUl0OzbjDag=", "Pl9VcqPouai04xVymfzPL2L/SByvXgSLKIgXIc84hik=", "LRrLCi15J4UAGVRSd8uiWkMskOB76tCF8VAq3hnrd6c=", "3MbAyyxktcHgLduldEUuuW+MzrLjie8/5+l0HJayHu0=", "89zfMZ2fCbG4TS1wNVVe9xutOl660qpn5aZTlaWZiYM=", "myv54jkGspqf00+eQBUa9cIpnSwdeL6VF65c2tJn9OQ=", "jq/Fka80S+tA7fDoIkwZFXNnRCllqQ7I0qWg6AMPpKg=", "aXZ1pQAJ256uY22qmAmETA30iTEDJrRN6LeM4KH/5vY=", "uRFuzF4L1iLnwA91S+09CPXALisgFO6GRoBc1Q16fq0=", "SWjw0HZnyLTWzP8BL8gkkcx2/nPznWpNwrTDbhmhr00=", "c4WoKs1YJR170j+OBg5Rg1bC0ubhN6Zd1LsOqOXpWAA=", "B5k6SbYVXxIWvrDagS0mZA8nyQqTc81TH3VVB/v8Rpw=", "WAgM7D2oZQTD0WES+2s26i96lZx4dyXgII4RZ7Kydhk=", "jKTdzn6fGGR5psLmvOYqCFRqwk4GXFtcb2OjChHxEe8=", "0zA5j0qRIZuepAqfUDiNEqXSILUy53ho3LhS6JqD36s=", "zz4iXir6kNoiitg3TizA04RIb293i4UJablH5e+TORk=", "bvwztOaeKi9x8FL95odM5tNrM73sbZlhjPlLtgyi4sk=", "uAD3rhh0oapof+pOc3cGjrRn6upH4FkxtEwlV6iR108=", "WF/Cbs23ioF2Jxqnhpvs/bEOnipuL6zoczb7+LXOHLM=", "qI4D7Iq1zJYMJwj+ij3XlnWSGerrtrAr2hSQj9LkDhc=", "nsLNS3IWU5+3BPtXaJxhMYnZrVXrEqWEAXcIsV9Vc8k=", "AjGHsm2VrRteSnTEi4z5kcH6kSBnq9BZ7iRK5m6gJnU=", "Srg6SeIz3ss6L167LQcLbxcS9YiRq4uJ8z1PreYBJ4s=", "DSIaVizb5/7UgeKxEILL2RQUQ203CdZbx5vjjX1d4+s=", "WxRj6ojouuyJZ18kFRVEIJmFsH7Xm8uARB1lzj+IDE4=", "OxJ/KUyrigjk+tMl5JDW3fC/0mzwJ3qRB6h0gYX0DQ8=", "2k2FAziScgpLYJ3Cnnw0Ou+oBgvVgkcnC7H7qrRTlGE=", "+5J4Yedsjg4/0m/Wgz5mwdKads9PVQjYILst+gbZLFI=", "h0RKg9kJoogP/ZPPBmarw5hBu2BVmbGAsjHCYGpoxQ0=", "OO/VXzKuuPGeqRxEG6HK7A3oKhZtUlvoIsPyAxZe4J0=", "WcKcMFPKNw5jrV8/KYlnDGBxZjHvxIjaz94M2JPqCXk=", "XrVt9wmYnXFkNI3qATGIEHADBBIfS5VgBvfgFFugz/Y=", "mAl99p0q+x6K5vXwMf8mt8KB1NqqprY3rsU05oJJtVU=", "wvlj/m5CGrMZAfO2vfbFt7+VFjRIGI6P5XepObPtnfQ=", "YgqynSLV7lf9KvKyBg/9ljLcastpJaIzfm79W4KTP8o=", "V3+/m3VOWp/bW9kQdwq/GUZnwZzfNlQKziqBXyzXI5I=", "GPpxyzilD84XqFxgqQq0kSgUkkzUhJABx8Osc/GNwic="], "CachedAssets": {"XJ37zCqLhRE6y65Tx8YN9BrV+lymfYGRE8OIAdui7qA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5j8dxmne5b-mv535bwyet.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ijom068", "Integrity": "+V2Lg1Jy/5iLhIkZHMvZU1uFPbr2UWCvWIR1xl5s8EE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 19025, "LastWriteTime": "2025-06-24T01:28:08.2390431+00:00"}, "F/r+ENtDuoWBUB7ew4qqaq7La+nxFBuEIgMPPjz5JNo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dy5lkhjkn1-xtjqgewnsy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ypltwg0or", "Integrity": "mTNQIg/OZDt0Q0dAy7dBwiQu2PV2LviEQDjvUWc6sJA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 17685, "LastWriteTime": "2025-06-24T01:28:08.2938428+00:00"}, "oWV+AOvkU+dDztPENfEP9FFjxHGgMQkMKbeqdvDdP8U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f17o09ymz1-vbl3iftxmx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2bq4ped28k", "Integrity": "Ll/BngIPnHtO6rbjdkXKhF2EYc27bIegGy+JdEoDUjs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 129423, "LastWriteTime": "2025-06-24T01:28:08.3467992+00:00"}, "YMP1x9E62f7DJQ5i4LiV6Psb7RO0mli3JJtooN7Uy1Q=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f813afr1vj-i47vxqdqw2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iifampbfuf", "Integrity": "9kglTrQpUWHbUZooUGNxTRs8OZ+RQDXsyCrgBlLse3c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16301, "LastWriteTime": "2025-06-24T01:28:08.4204272+00:00"}, "qvvVUz6E9IWL7ZEy899gZK7AtfJph7in5sg5WfAYK1Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1gdl188rtq-y9j53ldofb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "01u0wu79e2", "Integrity": "BKCzLjnDV40Tikw9Al6lUk5mePr4/nHVzrNv6at/I4A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 65400, "LastWriteTime": "2025-06-24T01:28:08.4594078+00:00"}, "Oe0qcBhyA2jIrq2laqQcWhLdw1vft6tyORtxPHBvqHk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\99xx5yiczq-3uudqrjyld.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aw1545j549", "Integrity": "2sDM+GiLqv0h1K5ltpSprM5ypTpQKddIZ/qeY7ES1Pg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 46105, "LastWriteTime": "2025-06-24T01:28:08.4908508+00:00"}, "ynu5fJllAqHLydCcsVReBf6lLgEYATDnFIfbkNNGGC0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1jvnjphhuc-txus4zzmh1.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f996fn2i64", "Integrity": "ncBx47S75+HEGz5/+1KAk8NqZREAOIw1j0LT7UeLir0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2409, "LastWriteTime": "2025-06-24T01:28:08.502419+00:00"}, "nDX/a3Gghn5ZhusR5rMAN+HXBbJh5PmC5/osIwpdc50=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sd4w29iblj-4njtqvtvgx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ehpzq00vvn", "Integrity": "woWY7cPpxRwo/ZlBGIpiuVyrCcNVURoJEClmhSxYIT0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15095, "LastWriteTime": "2025-06-24T01:28:08.490292+00:00"}, "ZmBy16R5XrHdzFgZ7zQTxvMq5+QiqHHlOEd01tsx5o8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qmrh3cyln1-8kr5d0tjmo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tvlzg9p4s", "Integrity": "SKcKAQ6unQQmWOLud3+yjljdvRq3k5HjYUL0Z0Ex8QM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 7793, "LastWriteTime": "2025-06-24T01:28:08.5093528+00:00"}, "3T+A+gEG9A3ru6TIFXKfww5owi07j5kusFI5X9XELxE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qq3w8mqh2l-0r3amze666.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dz6cxvyzbz", "Integrity": "WSuabncDxkAB8fqRIdPNHPgeAGnmfkzqrcBXgplQMGQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 13809, "LastWriteTime": "2025-06-24T01:28:08.5269158+00:00"}, "2PrtcAB4APQpi8MTL1qJbopeWAoPvl3fbbjl/FN5ibc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f0p7mhideg-en8mb8dgz5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8oobv0w90v", "Integrity": "WO+uRYcj3Zb9HIK7aDnF+ZYPe+fyAeKo2LMHDHQRlOI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 7680, "LastWriteTime": "2025-06-24T01:28:08.5351153+00:00"}, "0HCGJ6Vj6hlQAafPLP0ZtBA7KOFdO61jUJA9VMgqGPI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\luw69m5zpj-yy6f57640l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vliov49hve", "Integrity": "M1N3wrmu41ddGz5INp3pKS70tYR/Y+Xqu+oZ9rZqjZ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 7509, "LastWriteTime": "2025-06-24T01:28:08.5634736+00:00"}, "ZboB52FIMk1uAkUfdQpCvOqff0SgVG8DmQgxsnS3iPU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vhswmu8kpf-xqsu2wsvba.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6msqh3xb8j", "Integrity": "XoDoAbTIxo5MKAxsmkcf9azi6O5OLViGGKrBi2qMlgY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 35118, "LastWriteTime": "2025-06-24T01:28:08.5900282+00:00"}, "HK46iKQBxRmNpfVIklrMHI/uxAj8pA1PAdCGdRRZO0E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fg6rcqyzob-kgyjb8k43h.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "56nyq42peo", "Integrity": "MwyC9p6nt0mGMqIypm+SnvG+21YdrXDmlVaZDNsWJeA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 20683, "LastWriteTime": "2025-06-24T01:28:08.6042798+00:00"}, "WvBbw5MMH8n2v21AS2PejJbm4D3K45DbJmVN7cHrC5g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rzc0mkqxf8-1c7ksbormu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvw1zdn8s9", "Integrity": "pyOZoIFEM9t5FDCjL1vt7pFHGrJ/aCpe5ncDLhyScEs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5094, "LastWriteTime": "2025-06-24T01:28:08.6153117+00:00"}, "pZMoG6OWnkB87fBi7XmnQ4KAXbQGRTva6JGPo9oIkC0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pyud5iqoj0-rpvltkbyzt.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gmno2wz14c", "Integrity": "mL9aDIgzoCBBugdOwscAnV2L14lXopq1fPoBppkHjc0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 16310, "LastWriteTime": "2025-06-24T01:28:08.6316772+00:00"}, "BZVqbK7PR/ZOUpodv2q3kpQT+RuRFZdJ8I/RrU/ffec=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3wl2w0d9cv-i464dwxnbb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bbtd3i9alo", "Integrity": "p1Ah/YODlnwQ4s7t24etOtyb4hdzr3YlCHH3s8gUCH8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16109, "LastWriteTime": "2025-06-24T01:28:08.6468049+00:00"}, "Ai3cE+slKa+jjqMOlEQ/4/hojFhLr6o9y2+fVaIpr4k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\r2q3mj3u9h-xlpspxuy08.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny3r3g6nhq", "Integrity": "z48FFALZ2sAP4Fd5H7/RhhuPDZBP1f3ES8esZSke/qU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 18424, "LastWriteTime": "2025-06-24T01:28:08.6591858+00:00"}, "kOzqLsrUhHeffbJEoZ4KpWFu//gMUazPFQpy+CoFS4c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rvv7resapu-tz325eqvv5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "76x5eflkem", "Integrity": "L/EpLGuZe59Ju8jspXqvtC8hdyOL8Zrhe8lxopsvj6w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 23619, "LastWriteTime": "2025-06-24T01:28:08.6865918+00:00"}, "8r2MpPX0Y+j/2LDGYS9KkQ4Lkn5dntVXkqjlvKYSD3o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sx4xzqaqm5-jt8xzja2dj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2h4o821w0v", "Integrity": "WnOZRQAyyjOv8sTLVpC29t7cLD/gYEUsRWah0QSLSuk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 23295, "LastWriteTime": "2025-06-24T01:28:08.7121497+00:00"}, "/pJlWrsrs+5PfvWh4ZPzRoH+l9Il5ICexr+4PvJ9dVk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ta7m7j2gei-lsakbjp1fg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwlb4s9h23", "Integrity": "VXif+d8llcvt+N2pU6LUABQr1EUvnwTg27PGFGjJoWo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 14733, "LastWriteTime": "2025-06-24T01:28:08.7501749+00:00"}, "Ssf3hFl8vTZgJojPi1TrtFGKt8DG8knXk0wGqHZ29jA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ldqgtsc41p-ae1qwufxjk.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xq5kas6hv", "Integrity": "dqz4oJ04lK2Swcgh3kNZ40yIAlv1JsGNeTCEQZQM3sk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24036, "LastWriteTime": "2025-06-24T01:28:08.8096886+00:00"}, "fb2rzmLf0D5WGpFjSRoJxtuwmPIs0RGB+Xg7a6JkGv4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ah5uj9fkaq-tr42ods1qv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qibamu24ww", "Integrity": "Lnnec2bcbNOfFQK9VYlTVxUF8DXy6CbINjLyVzg9GZ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 6764, "LastWriteTime": "2025-06-24T01:28:08.8440702+00:00"}, "JtjRTZ7FMlHoweRnzTntADCncWz90GaPTAevf9bluWU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rrp3ygrbh4-t3di59eis6.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Buttons.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Buttons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7klbf6wegy", "Integrity": "5wg6iRs8y4BMsOZWgbQUqC+zIx1CIV9rGYwK3zfZClg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Buttons.wasm", "FileLength": 64659, "LastWriteTime": "2025-06-24T01:28:08.9056054+00:00"}, "FfeIC5Q6/ceiMCDf7GMQaS5Pkb5kWr7XiO6qhjTRTLg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pxxrbnr171-76z3t3ul0w.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "adowunaa6s", "Integrity": "ogKIQZS23c6NxlSnR/P8Bc4hSMjUxKJHMu8tF4WBsKU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Calendars.wasm", "FileLength": 181536, "LastWriteTime": "2025-06-24T01:28:09.0527915+00:00"}, "qup2qVhxFRMizygjGDDj0mGjRrvvQNo0nxtWQdLLc+M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jydetqvzvc-tb4icbsua9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "khsv1q0k1z", "Integrity": "CBVxhDdHMnzQmrd/LMpzQVWP9StZULDZPq+vGwArMQk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Core.wasm", "FileLength": 108873, "LastWriteTime": "2025-06-24T01:28:09.1607915+00:00"}, "EilvlSEUy8EGiJEIlm/sMdxxds2b1UGR+iL+T2dzYuA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lcyu9ghdos-ldhtchhorc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "spu2wylyl7", "Integrity": "AECOTvY4/0LvEj0lBielluQuEqa0IVsqz2Jg7K3nVKc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Data.wasm", "FileLength": 129621, "LastWriteTime": "2025-06-24T01:28:09.2622577+00:00"}, "r3ka3yGx0TBCfacWc3eWeLLgJifetqICGXr/cNeZ0vc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3bohcw7c8u-3b3recd4c3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.DropDowns.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.DropDowns.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4xkphrrnbs", "Integrity": "KKP+UEyqZLYfWhqKcyTbmwqtGzvAXiCiCmens/OmU/c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.DropDowns.wasm", "FileLength": 267926, "LastWriteTime": "2025-06-24T01:28:08.3437842+00:00"}, "iUTOXuz2IrwbfezoZEkEdo5kKO1RsQ5XIwSCpcTZxDI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xnicbo8o6w-9027wsoep3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Inputs.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Inputs.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2pck3dc7ep", "Integrity": "usv/fCLyJFipe2YV6J/sbcUbt6OuimeVL+xPwxTurmo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Inputs.wasm", "FileLength": 225184, "LastWriteTime": "2025-06-24T01:28:08.4908508+00:00"}, "yAGvmm4Ot0iYyzVWm1Wa5XSqhgUww2xXBgRRdtLCRzQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\h6h4qv19aa-vei9li4mx3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Lists.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Lists.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jwi117nege", "Integrity": "Dd2QyhqLa6bjHqT3FQivDO1sF9QJMUoQ/lzak//drM4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Lists.wasm", "FileLength": 40049, "LastWriteTime": "2025-06-24T01:28:08.5207201+00:00"}, "7R/VfM3TjCkFf6mOFhCUU3lScf3IxSN8QjiFwVZ7lXE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cp5mb4volr-5v0k8dfq0z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Navigations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Navigations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yx60uf77an", "Integrity": "Js1zNfWitbEiO8677U9fWmb6EO810Gc7wzPRfZHZWfA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Navigations.wasm", "FileLength": 285456, "LastWriteTime": "2025-06-24T01:28:08.7066416+00:00"}, "OU81wfn/9GNF5XmjjuqJfhdJrgH82qACXOgfZ+kM18U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wgslxn3apm-yarh891x5f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Notifications.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Notifications.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lwtv2c719", "Integrity": "PZEimOu5xOABOKsE9gbNrXTketn5gWTgs4AvOt2aXw4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Notifications.wasm", "FileLength": 31431, "LastWriteTime": "2025-06-24T01:28:08.7443985+00:00"}, "IMilU8SR+dUgpY5gRCjA3xTdItNfi8h4Sip+krD83BE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dwdcdv3wg8-7ksm1zkb1a.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Popups.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Popups.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5sxqodejev", "Integrity": "e8bvI/FFfp22BtOBJhZZ4ClFMW7/W7446ODHMbF3PEs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Popups.wasm", "FileLength": 50629, "LastWriteTime": "2025-06-24T01:28:08.7760375+00:00"}, "6veoVFNf08Mv6xT3kzERx071R7DTI3FRyofU65NDrko=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xsb0ndz1hr-7uae7ijupc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Schedule.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Schedule.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wszogpubm", "Integrity": "COXx/y6+q19shI6UEA79oHeqXcXQ4+IA8/TstpCggdc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Schedule.wasm", "FileLength": 322664, "LastWriteTime": "2025-06-24T01:28:08.9715839+00:00"}, "vUNogsuoDpgLpj6/H+vpnkc9ur0MIbMk4tCpWfsrjGc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jkmpkjk4xk-pcebwu17mj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Spinner.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Spinner.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9n1gemzepc", "Integrity": "VQKMLBx/3l8cEiooPVjX4wl2MjZK6j3TRcPd8aslfEY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Spinner.wasm", "FileLength": 20991, "LastWriteTime": "2025-06-24T01:28:08.7106445+00:00"}, "NqeNr3jJFq0WldUq5nhwMkNDdOczwHnLLdWwfcBQzYY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\w25zlm6clu-4z51p4oqs7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.SplitButtons.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.SplitButtons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gsp8gk5ckq", "Integrity": "kGQnVaUnPb9r3mtX5ssbfAjRg6XR54g6+xmXJp0tGIM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.SplitButtons.wasm", "FileLength": 35675, "LastWriteTime": "2025-06-24T01:28:08.7557258+00:00"}, "8jiqf8nql+u1BUOfhH1Kt66+X20Ez4UiuBSjaI6T710=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zojyde6k3s-11z9idvov7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Themes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Themes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9iukb0h1kh", "Integrity": "cRLPL2Q57th+yyw2/9PBK19vorcgIcyZj0TDF1jRp1I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Themes.wasm", "FileLength": 1755, "LastWriteTime": "2025-06-24T01:28:08.7648332+00:00"}, "/6rENURRBiIDVtyWGMUr7wmeVUuFNaVgY0Frch6mARQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\98ec87va6q-g0hxkhx4x3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.ExcelExport.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.ExcelExport.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4f6apytcw1", "Integrity": "AJCFC2Pqvdw/GD1Gkt/os2ZJWAcIued9L6HIjHeRkp4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.ExcelExport.Net.wasm", "FileLength": 28923, "LastWriteTime": "2025-06-24T01:28:08.7885695+00:00"}, "yKYEzj6XLJOBnZiAZudo4sntjZHuuz+LrM/Ef2q0A8Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\md53uouqm4-4usqb2x1su.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Licensing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Licensing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nymawklkr5", "Integrity": "v9LYceMEWLp1kzr+f0T0USsmoNniv7p8AGLFfGCQGFE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Licensing.wasm", "FileLength": 24051, "LastWriteTime": "2025-06-24T01:28:08.8157317+00:00"}, "u2Y9WpGTiPNlY05L5H7YFTTpdzbqenTwRZce54Armds=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\e97ynr2icq-iudrcw56e1.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cbvypw07go", "Integrity": "CMzRd155p5hsb9RsAB5w0e7pOoS0xg2okJH6wC5TZJk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 29719, "LastWriteTime": "2025-06-24T01:28:08.8563206+00:00"}, "mO4JM98bJQRLBWEbb1P60W6ZWy0eztSuQyAKQCHAVc8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzxdtik6zo-1hj4jqau9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9bkwerc34n", "Integrity": "KzdDoMv7JoD6QG9Efhg7xgLDqgA1Mubg8pakNIWTd0c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 131200, "LastWriteTime": "2025-06-24T01:28:08.9106043+00:00"}, "oto0bGU/YFb/LYWoM6ZnWViuUW/eZ6VKuI0QAEq3Gak=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\egcrwen6o7-rr2wy4asdd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "19rhglwuhx", "Integrity": "y1fXFjCYAnmS0Kc12Nb6r8YfXyZldWf2/OpKkKw6EBM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 166939, "LastWriteTime": "2025-06-24T01:28:08.9899083+00:00"}, "/B9ud57bBzYrzmolP5jxx5RhU3vW1QrW9wDtslKGVms=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ojmli06k3u-uosabjs4t4.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhhu59cna9", "Integrity": "Dh0g3MCEcTxNMfxwZGn2FqF1s1GpGId+0biDE1Sw8vY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2852, "LastWriteTime": "2025-06-24T01:28:08.988907+00:00"}, "PjZNaGYbjTURjgAgxO+2gwjFBBCA9OyBinY6fV63LTM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0khw4724ee-af1hnqw24k.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "okx3d7tgl0", "Integrity": "SLpXSkFQCEnE9t6BsF+VKhnyxi083qPULeM/9Ji+Q6A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2200, "LastWriteTime": "2025-06-24T01:28:09.0144515+00:00"}, "+LghxvygG8zodmppIBNpQw0AmvIQ0luk6IX45YNYVp0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\yi1kr6g9b3-n74qy90ozc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "30bot3nya8", "Integrity": "s2ahtY6Js1ojFc/LQs0mcx0ogFexM9hdz/y161ZkdOs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 8541, "LastWriteTime": "2025-06-24T01:28:09.0256919+00:00"}, "N/DKfRJlVDkwdD/aHTAp9D2Bjkg2aqOSlxzsoyGv3u8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzjo06rbey-r3dkwup91o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wglxhp15vu", "Integrity": "8O9il9SXVU9Y3JRErxXP2HO7RfvF2udbHFCpbzvyHbg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2097, "LastWriteTime": "2025-06-24T01:28:09.0387296+00:00"}, "mMwbkbJkXcaWfe/eWOKxsn1sxl0cC8SWRJ8x/m6OHZ4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rmczs60qrj-j17trnwz0f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t3s60ixqlz", "Integrity": "yevotpKa4rlbryHPLCgGPBkUFEYvprY6z8pD/xDq5Ck=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2098, "LastWriteTime": "2025-06-24T01:28:09.0517339+00:00"}, "s6li6JvMCZ3as8hQ+NegGkRug9KgEm3as6y6/bkEX+s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8pinc6rjf7-lkbadpelqi.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9qpme5af0r", "Integrity": "P3I7FG1BUJbHvh2BX7b8/0YTpTIkbrJLdNwHTDhZY8s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 32297, "LastWriteTime": "2025-06-24T01:28:09.0778317+00:00"}, "XPEJKP8JGq60f4CCN+yOXZo4Oxe4IBr0WLQINzAYn4g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vzisnnpkmx-4aax14grby.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z0niuoovrs", "Integrity": "E8P7Pf0zmSlS4j8kydrgnYSVRbpqn+L/gEcKgQPjMAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 95472, "LastWriteTime": "2025-06-24T01:28:09.1577228+00:00"}, "eJ7bNGegk8pRXitijfES2J9XsVWgeYxvmfn0xML0aTY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xde021tovc-f2o09bw51d.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eru1ovbsaf", "Integrity": "BgpD+YeFXdnEP++X5Xvqjprt2EM8VCbTn8lkOvvbA7Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14141, "LastWriteTime": "2025-06-24T01:28:09.1792058+00:00"}, "utQ298dMiNM80PV49zw6fRE7bICyzCyyF1P1s19XjS0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7a5kyzpvkv-exlzuq35jp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6hy5rtbzb5", "Integrity": "yjw08JXnHUBEc52XbpXu+/lyNXBOE4Tgrgdq38DoBCQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16046, "LastWriteTime": "2025-06-24T01:28:09.0176885+00:00"}, "eP72YRkhtEyMlUdveWuESUctRO41uzpg4DTizXBpiRg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jimaxq9mi6-y7qnt2sca0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "li1iq7ygyg", "Integrity": "0zEo5lrQWh6+txzgwvVdTPiPXiZqR79gmRerXXVMchM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 38924, "LastWriteTime": "2025-06-24T01:28:09.0407307+00:00"}, "G9BW6ncVduOXPySIHh/L66Fr8uFp2JnM69+ol26Ih8s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n257kqytbc-v5hyanf0mc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "niyp9u8mcl", "Integrity": "/1sa3aIsEe1TEKgmj+tIgYOKwY1bq3R7NPLEh9XefPc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 35253, "LastWriteTime": "2025-06-24T01:28:09.0631557+00:00"}, "yP8a6ueIEKuJ2s8tXlugo572RXGs7y1oq15V0+5kZtg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3paocsqo5e-2a2uywtzte.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fwnhdpwzz1", "Integrity": "wXsA0FaKmHTFMLHbczraksN2of0eY54lAfqJVIx9P2o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2572, "LastWriteTime": "2025-06-24T01:28:09.0758329+00:00"}, "qPjp74uuJmjJWBRzzgroO1e22mfs+8MtUbrti7fXfGs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cap10iz261-hsnsliye9b.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0ie2psq76", "Integrity": "QCcx0t5WO62E20gJZxrNEppbnOIEnPtqR1hZ5N68wiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6783, "LastWriteTime": "2025-06-24T01:28:08.2367018+00:00"}, "ihguYHSypqd6iUqrZkasB/pPqf2s24kDd6PfU9DoGqE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9ssd939fn0-m498x7yd6j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "837<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "ZH6tDts3+2tnHmoD4S0vQ2l7jiJOy7RbAs2vWS3eiD8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13073, "LastWriteTime": "2025-06-24T01:28:08.2744448+00:00"}, "XIiQjDLdhHK0nK7P+p/l+P11125449HE30iXLTRCegA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\byt3nyxy2q-69bwpm0gd5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tq490w1kqh", "Integrity": "RRCVYG6YWtl4WNHmCVJKob1znUxxZbfnqqKlaviV5Rs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 118082, "LastWriteTime": "2025-06-24T01:28:08.3385386+00:00"}, "eFLkyBswhFtTKvVun5QnSdfk02Pf4ikBqWHMlWdIG70=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\c39t6asywb-yq2dti153n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pc57z9h8i5", "Integrity": "JxKSollRWK6jo5rUQi7mmn7LCQ96uSQQXWiPSxaSZyw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2554, "LastWriteTime": "2025-06-24T01:28:08.4182562+00:00"}, "E9Y/R0hsGsDVqkwHPG16GY+5Tb2PA2LRbqtbNvY+J9s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hiyuk85oo1-pteo5cxcfm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9b1mghd7aq", "Integrity": "JTS4wqD8bFYTW1Fo9vgCykNZWTY3CRpmz/FQOH8k56I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3101, "LastWriteTime": "2025-06-24T01:28:08.4336367+00:00"}, "9NqtnOY73F7jz8FrZlmaGIxV4gZrGCp2fj/WKtQi+q8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9316qi1ji-zsobxitq9m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d6ofa9jyov", "Integrity": "Jeax3i9g1YYJWZVGLjEmM8xaFqAkqCqsGfBR6Jm45go=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19796, "LastWriteTime": "2025-06-24T01:28:08.4970862+00:00"}, "biGcWp2Bo+B3YdCaN9Vdc3o+FGRGMgOriVPJqgrOt9Q=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1umd06yrfb-ayoozo91sk.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1g2486v15h", "Integrity": "nwo2dAyBjQryhcbmVd3111QTbC9rpGkycx7WQ4W43ZY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4537, "LastWriteTime": "2025-06-24T01:28:08.512747+00:00"}, "KZvKX/zlRT8N3geSbKFVLD01KUa4p8sPVGS4Dd3ppTM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bz3avbczq2-ptdxkmw326.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kwtgxwtrsw", "Integrity": "nDMAP5IcmxPRQpsAAzy/gzBXjAXYGeg7nPilM8qpsc8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 376217, "LastWriteTime": "2025-06-24T01:28:08.7247867+00:00"}, "x9yTSgWPl7IbyNKgkGGpfvUcb/EoYWYqF5PMTxiNv74=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4wpiy2o4d3-ezdljfc37x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1kbrxf305t", "Integrity": "QQA3GmNzeH8/4+MCBZsdYvQPNEx6TpX7uWm32xE/0Qs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2060, "LastWriteTime": "2025-06-24T01:28:08.7402657+00:00"}, "H8VMU3kFTyxMLNw7TjzinZNAqtsIYvyzVx77g9mvYYw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9wtv5r574i-s4jbqeso3o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3bvzjuovbv", "Integrity": "P/R5dma6QtkpD2X1HHRTI7GIQ2YqYGADg5QWyAlI0Po=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 4990, "LastWriteTime": "2025-06-24T01:28:08.7750379+00:00"}, "mmoiXSTbhEs1LsKbha3YIMGGOOBuKqUagkMn3Htep3k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\m3qtwn3ns2-rl77dmc4g8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uyur41mm43", "Integrity": "ht3ZdJI0WNtVtJcrhwwLiyOMTQ+OpzS69T1k0fmrc/E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2386, "LastWriteTime": "2025-06-24T01:28:08.7835623+00:00"}, "nmK0sA0X5xEeGkZ85Cb/ffVsRhnFJNHnPw6pSefM8f4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xnalvtn1mb-d6r1qmhtiw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvzr24ljx7", "Integrity": "Z9CpJlO4WNouGPgRrPDcjuDoHzfGHW/bserGn1p8g1k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2268, "LastWriteTime": "2025-06-24T01:28:08.8142851+00:00"}, "fU8mTnPCbcvjL2VgwflsQYPMW2483t6Z9kZ40SlDG7c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fnpgxujk9y-imdaogz3ij.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcwtmo1qly", "Integrity": "BfHo7tj/hczINWO90hVez350NzD6tdEZi6SlqcGFjrI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 65136, "LastWriteTime": "2025-06-24T01:28:08.8455883+00:00"}, "x0pTmLLqXt3pJAjmasW+s0ThFrYs49gULXR8PU2REM8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sxppidzjgj-gqyf43a3pe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xg2f6930e", "Integrity": "JVlsgLgvw81DErZ//g8X8LzfJ00vxra/ga7xkeXaoYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 4541, "LastWriteTime": "2025-06-24T01:28:08.8735994+00:00"}, "vFQosOmT37HU6NukhgM6+7/RRCbOqZpd39jve7cW3Lo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1lzqee7hvd-2qdjhg82pw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "23acjr4ix4", "Integrity": "Dr0M5EqmQmZUYgBlWroUISYdU2GJ8StHTbVINDBa+wI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 15502, "LastWriteTime": "2025-06-24T01:28:08.9116044+00:00"}, "FqalpLPmt4xL3QHCQbrrkboKel11JY9AFfAh7YJFXCM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gdmgran5y7-lx3knuy0pm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f5s2sv31se", "Integrity": "A+OYlrweyDYZMJtkPvPPwP8ovZi4Vnu7KRsklvBzE/Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7337, "LastWriteTime": "2025-06-24T01:28:08.9361061+00:00"}, "Kp+NA5RI2Rg5vkgVXtTCUJ/budJkqbT7DhgLhFL8n4Q=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wdlm54q4fw-p6vx4lif6u.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "68rv787b56", "Integrity": "aq2dt+iaq7WiLm/s8AJSq5QXr2/yJGGxRlNGEE4/DTw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9389, "LastWriteTime": "2025-06-24T01:28:08.9690743+00:00"}, "XKnpA9gEx8h18YngoRnhcLdV1g2Mvj4RFHH7I88ACmU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\67lx4f78cj-4s4qj16jn0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ppekvym6e6", "Integrity": "g1Gm59Wb7OPwIQWb1m47EmGE4BTjE9iAGpVQHhl4oAs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2175, "LastWriteTime": "2025-06-24T01:28:09.0175126+00:00"}, "CT/9eizEIclNhRaAu/A5UCEihykjSijUKRtsAosBbts=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\77au2vjfld-5howj2x4lt.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wvxse30j4g", "Integrity": "dOsRzhaWV96BCY1Ycm/u3ezIaiKAEip0IFSzwd7BEhg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 19528, "LastWriteTime": "2025-06-24T01:28:09.0357237+00:00"}, "QO4tb7S+DbOxfrlRtiTc+0fvB9OHG57MIlGb6SExDm0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\binqqlz1sy-jg8c5ekqx6.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s6x2cf719o", "Integrity": "fBPaTZHEVK3+migohaE/Yy7OInHRLWHB65yYdDGyU8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2501, "LastWriteTime": "2025-06-24T01:28:09.0517339+00:00"}, "mQVI+Xle1X50mn02Gx2pEaGLQ/f7TgdLTcF/Q+isr/Q=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\eh357harcp-tvnls6hcxf.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rx3m03fkr3", "Integrity": "b5nlulk0mFhxWADtDot28bUdoRYNnpcyO5azIJrwZU4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 23209, "LastWriteTime": "2025-06-24T01:28:09.0685624+00:00"}, "kipByAukgoueksAeEIFsYfSHf245evi+okNNaTLVIX4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9yxv7vuua-2ad51ju9aa.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "509vpylrg1", "Integrity": "VdBBtw0OhR0il0Y7j/j74swMAJJeki+tg0z1+PPkO5I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3836, "LastWriteTime": "2025-06-24T01:28:09.0818313+00:00"}, "fS8wXtl6sD0M97WzdZHkGikyP1uNRul6YknVrCc95Qo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\leomrziixf-n0jfd8l0iz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e8pz0poje4", "Integrity": "glFWtRMdwoHgsHoIBoafQ9XMwA2fuglIBmfgBwp/YiU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2439, "LastWriteTime": "2025-06-24T01:28:09.0926037+00:00"}, "syRwCaRECnznw7S+i2y48xJPgOCDsefKEWbz87J03go=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\c4ro1hju42-l2i13om05z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "llifbqlkm7", "Integrity": "FxH7+EkE20SRBfrCFEYpuNyzYsGghnW5dtgaWF6zV1I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35455, "LastWriteTime": "2025-06-24T01:28:09.1278954+00:00"}, "w2bas8zbycvGWHRKb1SIznUxuXDq6m/dycpWETFoXQE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\57agg9smmy-k6z943nrpy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wwmvnmo5v8", "Integrity": "wNS9nQ6taI4WIOuWvLhHiNUbHnQHmIghxoU8nE3oEJY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 9800, "LastWriteTime": "2025-06-24T01:28:09.1638236+00:00"}, "TOSQrzKF+Ibgx7l4jkXms5g1Sif9B/F+Q5NktWhpTTE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zr3rxoo55n-g2zbr73a2g.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n0siig7ezj", "Integrity": "qR+bdN+x8r/qd5YeDAZ+iMiMEFXx4Tv3NyBVNkAQYLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2286, "LastWriteTime": "2025-06-24T01:28:09.2090776+00:00"}, "nM37qD9v5kcPPh6x7y/5mfgt6fQUPbVQ5is7Xwn72+M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z98cfng326-m90ww2zviv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rzfub7enaa", "Integrity": "d7OmwmD+uY4Y5zF+IHR/ufDjDASRFWpMQdWoQNnL9XY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2172, "LastWriteTime": "2025-06-24T01:28:09.2567509+00:00"}, "02DKObDqDSloY4yIffcMLffo1JZNam4kAVLuSxOzy8M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\giup3hxlyd-8bmqvi5to8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cokylgo4j", "Integrity": "NI6MBIZGKXJj72bc+IDjKDG1nTaRS62UMU7HhUe9B8k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2253, "LastWriteTime": "2025-06-24T01:28:08.2336931+00:00"}, "D8U38MjwET0nG0a41g9RG8qKV1gyYIFldvYYWFhZTEU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\smvg5f5rpt-xpehz1u5xg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lrfpf212tn", "Integrity": "75zD1Q2ROvS4abvFcfUGIvIRXrYph4uh95ApImW/haU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 6272, "LastWriteTime": "2025-06-24T01:28:08.2528197+00:00"}, "g2qspsUWL8muqOBxFBnbrJidMHtW7aRLQAJImYyTGA4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3zji9tv399-ofyky8esh0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ar5txxss3p", "Integrity": "Zmd9bHK3cFt6k9sNgJuREnTS4aEPzxzeMV3m8wfc3tc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1990, "LastWriteTime": "2025-06-24T01:28:08.2666855+00:00"}, "ZaagxjA+9k5Ni2/XsPnwus1lpcHCx/iBiTBV+x4nuJg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rgl88w1it9-kqgep265ab.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xeynh5pdza", "Integrity": "Bz0JVrr6Hx3RVKbXH1DvlPSwZ8U6urLUZ7YsDLMN1gk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12136, "LastWriteTime": "2025-06-24T01:28:08.4051157+00:00"}, "ycTAlPLqK0TlBJbr5n10KJ0UgMebaqORLwUzLM6viAQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lvyrlugiaz-whf02me0s4.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mj82s2fn4h", "Integrity": "witmYMa9fX1LWLEnnSkpxc6qMBPvH9UJ56UtpV6UDiw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 42208, "LastWriteTime": "2025-06-24T01:28:08.4266778+00:00"}, "EQLzpCOp5bGuLk4p2k0K9smbnyCnScVsG38UpZ49pwo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\y3zikztta2-1trkjj9toj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7zerdc7jwm", "Integrity": "bW4/Q896W7rggpSIkcnzzXK8sF2zS+Nh3Ye+FkOe+Hg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 7897, "LastWriteTime": "2025-06-24T01:28:08.4499062+00:00"}, "q598o1xfh8EdTSPMi/YKqS96c95NKecIdrxkp2BrnG8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\l7ml98ds2n-pak789pfhc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qkpig6b8z0", "Integrity": "+ukRzCWyHHI2UMNy8eSTc0+qoj4k6ks7GLIi1AxlSxE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 5594, "LastWriteTime": "2025-06-24T01:28:08.4554113+00:00"}, "MX88w0Q5LhGkBahbbneBNOAymWtqQm2+yvdsovrdB3c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\26zimhthez-tx889edwyf.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iyfkzprjym", "Integrity": "NvtWlUdNbuqFIqDe7wn6eig1pzv3ubGVrST6af8kdLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2178, "LastWriteTime": "2025-06-24T01:28:08.4919215+00:00"}, "JB2Ox3BlT6imAcGQNNR58a+wl2PTXqmdvihaOEhEpME=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cumen4e1px-2y53qsfelr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gyhsstey2d", "Integrity": "GCb8bFKPLaznzN82U9esvWP6yOzad8UP7FjWlXgi/Bk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8195, "LastWriteTime": "2025-06-24T01:28:08.5249112+00:00"}, "oaShbFHVXCad0ikIel61MdELpaynJ/QeYOVW6uKLK3s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ohnw08v0s7-5i7u2kz8gq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fw5w18ce27", "Integrity": "IRnwVEiXfK+xZ68TNQVJOP2qXguSZXVzrwQxtqPX2aI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2292, "LastWriteTime": "2025-06-24T01:28:08.5533521+00:00"}, "1RexsMBtyjz4xb6nz8yjElV3gj3BKbkryrP+plBv538=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gmtjrsjpjr-bg69h2q1tx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16zmobxf9k", "Integrity": "iU/cpgWhMyQffbAdpduvPLrhY6Kj07nzKAhgBY+dM8I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 8776, "LastWriteTime": "2025-06-24T01:28:08.5654794+00:00"}, "0EJGOAyDfIqSbiFQnlBRkwwznpX2uhDwGZnsIxjzLXo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\yqou8ak97v-s21p6d0e1y.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3mjomyzpge", "Integrity": "tXgFpGSAfgq1a9xA2NFRuMqz30+bxV4dmwS1Hrdr1Sw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16342, "LastWriteTime": "2025-06-24T01:28:08.6198645+00:00"}, "ZB9n2MMbcgrzMJiFFCzwlYnU79ihwqcI9bIlaUm2GTg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\y9x9709oxs-6jayxq6dso.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i58mqzxche", "Integrity": "xqjDP+M7TxdNllx85RenwK2rx7+V/2slBz8qQGDm1oI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5304, "LastWriteTime": "2025-06-24T01:28:08.6415864+00:00"}, "SJVwxu6Ct/PTWcuiawZiDbVkgkzg7nL8tPBpJ1SUDac=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\2yj9ptca6s-78mrke9rwp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nn9ju4tano", "Integrity": "XYq8n+UkgUOCvfxP8h2dW4858UaTyqsM10uBPtr8PxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 10481, "LastWriteTime": "2025-06-24T01:28:08.6921929+00:00"}, "qjw6OGG32xu7FFIOIBGqTUEOtWQAqI9DvipAsGwOmf4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5oprngc3na-rqikjp4z06.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ag89dak36", "Integrity": "l17a+x9KegTblfc489R0R9q9yjDIBn5fG8op2SGjJmA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2199, "LastWriteTime": "2025-06-24T01:28:08.7096425+00:00"}, "LP+HDXdfVf7W4646LJHoTtyGlT0Yt3S//kQGW4jGx8E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9rp1nsr78a-lyr9te5dpd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ge1qryld7y", "Integrity": "mNy0m8/GIFsF687byrnByu5FydFGY1KQdU9W8nj7CFg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2254, "LastWriteTime": "2025-06-24T01:28:08.7257941+00:00"}, "y1C9jxgyd5iAqTSMGvbz6aP57AOgs6Cxkx8e5xwaYLY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wqbo3t8krt-v19ocl650f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2l1l0tmhvx", "Integrity": "Ri8EMIJeDG0rCy+8iYC88ausg6ADxlqjh7cIHeEYXfw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 213340, "LastWriteTime": "2025-06-24T01:28:08.8747187+00:00"}, "jFdsw4kMMg0zlDgiNMFbfAqorQ+Y9HDi3AJ0m4O323E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\32t05zddb7-7jakql04zz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "484huxgmuc", "Integrity": "pLEkmUQ+d4hzc1xvlvmUVKxzzLq/ZGEqzHWRbEzoqv0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 86883, "LastWriteTime": "2025-06-24T01:28:08.9416153+00:00"}, "IQJhsc96+ni5u+c9XNZqlAIXFPgGu4JRe4Yke9XyXvk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\o4a5y23fdq-yskp2l2j28.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "38gm5sg4jg", "Integrity": "oxAXb9LEPCVRpK6lfJMaCLud8cT9BwAyeIR11RgTH+I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 19930, "LastWriteTime": "2025-06-24T01:28:08.9790672+00:00"}, "RfKeBYxL/wURIEzUg0iK510fTqZtLserfqhHLyUne7g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\6a5ovfauzx-dr9ustd9mn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q6e2yjy63c", "Integrity": "rh0k7q0Rj7nJIy9awxK3sNC8ZxQmsbkwNyFdZJvwRqM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 50155, "LastWriteTime": "2025-06-24T01:28:09.0199727+00:00"}, "zNnlLvo3JxzhPManpF0/ZENTI1HWJYC7+wQ7nn1c8po=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\raqtb0g8tk-r5wuytek4x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yw8digvpjd", "Integrity": "YjA/Rkd9FOdVKIeRjWV+/jFIwK8Jn+80stxpPPbLNKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 20344, "LastWriteTime": "2025-06-24T01:28:09.0936176+00:00"}, "u+ua5iNzDZghrYDdKZnGaXQ7uj99dTrGFHulswKSK8o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\k90ia90a0l-r4fmndj4lr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pzv3gw1kc9", "Integrity": "S5uyDL49nDbBrz5McBV7euPXQ+lMDVjZXoZqpIw7q5g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19101, "LastWriteTime": "2025-06-24T01:28:09.1477101+00:00"}, "sge8ectkmLq3GcYYXQs1fnZ61pI/7RG9enxhkR5MPts=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1cs6asnscm-dfc7iaw959.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77ioaax9bw", "Integrity": "3Yu3Wa4h3iHEArbBOf7yb+QtRKmJEgAHS2vrZ/8uUK0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 110803, "LastWriteTime": "2025-06-24T01:28:09.2831916+00:00"}, "WTFt6OEzNgmDbpbiHuuEVHS2LmWhG3Sf7PYiOKy8pkg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\om386z7hvl-ypu8t1a3th.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g53eoah1ck", "Integrity": "jCiqprAiO3CT8Vhgk//euU3zKduq8BvF1YsC1FAdgJ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 15450, "LastWriteTime": "2025-06-24T01:28:09.3157047+00:00"}, "J9oQEBbRZYWzsrbXzHqyOvjkXvLroolbmVA6bnqIvHg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\01kj3kzu05-avt2ugss8m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qon64aop3f", "Integrity": "amlyJTqDmydz+OtgLjiXqZby3pdbHW+DGGA1Fi2te5E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 41421, "LastWriteTime": "2025-06-24T01:28:09.3700928+00:00"}, "xcvWUDWkXfJ1jZhjCTzPU8PUUp9G4e9enrzBuesMS7Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4ziudi5er1-kqt05iowt5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "86wjlbs9zy", "Integrity": "oGp+2AS6Y5IPJZiieXlusOVrWezQMf+BeNYJtcIo5+A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5340, "LastWriteTime": "2025-06-24T01:28:09.4071139+00:00"}, "azuMeSCQ0Y0bGnyfbldVxD7Qu+7v5hQsiOFav1SrcfU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gqrdkmv4gr-ac0n5txzyd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uvuyz0cttl", "Integrity": "JiyT/6xYYmK0R8a5TbJh0Mws8KUfEYg/jc1xqE0dlxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 12139, "LastWriteTime": "2025-06-24T01:28:09.4253616+00:00"}, "uh0uW0B8FXY8jW2+hnohlaYw1DscWx9VTWBsgWJCKy8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\778te4j2e9-rrwevpsa36.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7149sngwk3", "Integrity": "q9ThFzz9q2gD0EwjqEHL5Fmlgen7IfOeVJAau8oBer8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 6939, "LastWriteTime": "2025-06-24T01:28:08.2336931+00:00"}, "U8RQeDVhEqKNHfa45zqWv3DL8+VqvSkCSBXDiWMOlkQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\axunml5apk-9jxobawljc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xn6u2l8nng", "Integrity": "w4xwZbTj0hb1Rhh27gZ4B8fZ1+8yjJI/X9ZBgLiFkZM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 44984, "LastWriteTime": "2025-06-24T01:28:08.2835389+00:00"}, "g5mSaO+rheJdWs/VKKRCfRgUnsjW3u07vobd2T8rasA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\swgmwtsne4-nn6t1rxfu5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mem9qevjnx", "Integrity": "UiSda/h0xpyaOY9R/aCl8liHhr0KKwdVbQwt/YwKPaI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 10555, "LastWriteTime": "2025-06-24T01:28:08.3053024+00:00"}, "XQOMYCa1kHjlt6SGloXq5pGwIgTiM8YpdEGmXCEfQBU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8bprzflbij-psjawytmz8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7sy3fabvn", "Integrity": "pl0sR89QMESvxhxOIVL7R4UjWry/kgPgDunOUEqH7zQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 18143, "LastWriteTime": "2025-06-24T01:28:08.3300072+00:00"}, "se76SegtkM1unJtMq1bFX9chQ5VUcTSUAkyOr76TLbU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5peanjzhpf-49lyg32can.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7v60bzgxd9", "Integrity": "PUaJImE5ckbLfJYsXFj5cDkMcvJQ9DNVO7MybEkZGvc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 31586, "LastWriteTime": "2025-06-24T01:28:08.3606334+00:00"}, "UoBs4uIPOsCGybRj75WTkg0YvdxvGj7iCHBiVWdd/Oo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z537psxkb5-rtqo41ax01.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nr6sdeecsv", "Integrity": "veljWMXJcCH7rt7VSDdL+XQqBjvnCbqeh/AyayRKz3g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 7242, "LastWriteTime": "2025-06-24T01:28:08.3839698+00:00"}, "gEVYKgA4UntZtZd40BNkjOdilSZAupt+Hid6uEFrNS0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0prwepgyfd-300sh8z8ui.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ee6hj5lchi", "Integrity": "prE4z0kV0rwaqLHEDAOIDQ21xglM0hFsisHTidgW9xU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 22456, "LastWriteTime": "2025-06-24T01:28:08.3978655+00:00"}, "amI0vkwDkgTmpMxLd5EYzJjZWOYDOaNpEguBSibQMik=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\12jxpcjsau-o9b03xt26m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2tg12glemn", "Integrity": "qqIbhoUpTxWiHD2FYyneT+mabxlksEDlNNRVttluOs8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 13342, "LastWriteTime": "2025-06-24T01:28:08.4236717+00:00"}, "7IB2WgQUrPWddadn33WFaVBAx3Bn10+26xEvBlcuTQw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\876btrwotz-i1l90qe767.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "db52nw0so2", "Integrity": "aX6Czvy2bWobhB9gZsBcEZkxqQw8sB8CO52znepjKi8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10638, "LastWriteTime": "2025-06-24T01:28:08.4594078+00:00"}, "0a32q22wSKVzy4XYPZ3LAOWlcDHco6KFx2ZqkhmdUzM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rb0r3wv541-ckahycs9oy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zojeka79o6", "Integrity": "zfgwasG5Wqe80sBTey2LPKjKwtA/P67J7bG4RyDW1fY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5611, "LastWriteTime": "2025-06-24T01:28:08.4803941+00:00"}, "HtNkf5Jn5Ajjc6aIUx3P/vs0MFdIflRqbUF1VN0ehvk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n9ufvg6zor-12eg0vhcpm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y830a1zy97", "Integrity": "cq6zyP5y2mpG2a+gaK4TPe2NohRa+61dX1XphyXo74w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 16048, "LastWriteTime": "2025-06-24T01:28:08.4935573+00:00"}, "65Ig1Bju/O+8NNIFbTDLRTZ3oskiQPLj0LKorjUzpi4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\s007umfqyr-tl58hhwwm9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2ebllpfgpt", "Integrity": "Fqo5F5okh6+1LaodfcG32bl5Kc/Q/0lMwgjk8Hj5cxo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 31154, "LastWriteTime": "2025-06-24T01:28:08.5381199+00:00"}, "kQrSsMG2X3koGFL/woReGtrFZASrxGVMeWkBdM0TAM8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\momr4swqrq-s42jx0duup.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "peimhjwjlv", "Integrity": "saOft7q0HakPdXdg8F0yKzrLc5L47V5VaAyINLdnWhI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2736, "LastWriteTime": "2025-06-24T01:28:08.5544632+00:00"}, "yZLauQF/qJ5aJqwjUNYaUQClSvp0Mykm4sAvitS4wS8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zlfrj5nqwi-a1ph0cw4zn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ad2x663mk", "Integrity": "QpcVARPeINasJbo3DgR/iduaglKuEALRJ8r8Bo+q+n8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2259, "LastWriteTime": "2025-06-24T01:28:08.5676834+00:00"}, "IA7hEk94SG2ObCBH6Ht4qEhWdnaN+65ncD+qpcYU9zk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0lktitq3ad-s0hlxxu97u.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4ka65cgi8", "Integrity": "ZD4xPO4qztMhOo8cuRrA35wsEViKaxMJf/6/FyU2Lbk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2023, "LastWriteTime": "2025-06-24T01:28:08.5764068+00:00"}, "FQTiqSYFK7+hXps0t4c440qouI6KSkjEQKRqKo0o76U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hmt6yadul1-cfb8wdf8bw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhbrd5g2p5", "Integrity": "9lPQEYhKUE0lwHNsgmMclDwfvwso1aKf6cS3bMGw38A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 12607, "LastWriteTime": "2025-06-24T01:28:08.5878969+00:00"}, "mNz6tgm2sVFHGJ6e4U7UX14zZne2aXqFCRtB7fQ2nlg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tqgnr7mkgy-pf4dzgp6et.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jfrip8650v", "Integrity": "oStP6XLcdz/5EfQPc9LYBRGrgqjm5i6VYyEDljdc9Lg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 301144, "LastWriteTime": "2025-06-24T01:28:08.7106445+00:00"}, "+HibjeeDoUX/1xfW6CUkL+e4tTPBKD2lxgQUSSgRlM0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qm6lsqstgu-3mrenqpwlz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8i35yc22yc", "Integrity": "AK2YY7vBkmfd61XozZugjgFjiiqQ9+lFTQOfju1uJpU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 41146, "LastWriteTime": "2025-06-24T01:28:08.733338+00:00"}, "7um8VfD3ha2GUIMdF1WniuF1Im30YWXgCqDCGD2RGLQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\kt41y9oyfq-nudbvebtzm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ubgu6qjwu", "Integrity": "EUqF8NOJ+jTFMJ9zNxnXyY4Z0RushGQ4xMWXukydvow=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 58586, "LastWriteTime": "2025-06-24T01:28:08.8293088+00:00"}, "XKWF+5orqPC2mVRBr+zjYQLFKlURsEPVyOqS1/npKg8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\07zc013blp-tbmcprtln3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ipjnodiyxp", "Integrity": "ejAVdjqjPJSREmeWoKoFWCthv0S1pfawXAGQ0PCtL3U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1062979, "LastWriteTime": "2025-06-24T01:28:09.4927491+00:00"}, "eZ4F5Mz9OBRIWUVv32zOvkgiah3dC8WTQwmnlYsZHIM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tesx50ibi3-824m7y8iv9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1izng5m8z", "Integrity": "6DJGUFZR8n2Mves782YEbmp6DAE78QTi5yTiiDmEfIE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 12103, "LastWriteTime": "2025-06-24T01:28:09.5167123+00:00"}, "IEMUf6ri4AdSUeu1DkagQCSPPyanYrDla/qV94UZh+k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0t5acjtnsc-5e6t7jey7n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oglg669tnk", "Integrity": "WME+orWFAK6pzqfLprop1rImI5xc8lTN10592bCWA1Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2270, "LastWriteTime": "2025-06-24T01:28:09.5410808+00:00"}, "7wW++n9TLJ/ROcG8Kjkh4MVbmeQaiVNQrsg/lMGXUvQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4b9lgapm59-zyak9ezx99.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdk7a3e3e4", "Integrity": "5zihI5BBHgZ6C9wCUHWqtcYbht5v6PGpTIdMtM5GGe4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2229, "LastWriteTime": "2025-06-24T01:28:09.5571542+00:00"}, "Z3wXuQx6pip8KoYb+MoN+9IZH3/VTzjVyF+bHM1zORM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lawfelcw3p-vqk9iwwdi8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jwgopog89g", "Integrity": "bXtSY/cj+SHug4/IkWHIAmGKk4/0fWqz+o1KvV5Gndg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 26864, "LastWriteTime": "2025-06-24T01:28:09.5862182+00:00"}, "wmG+M5/gpLPqbNm5/B4JFujjyU8gRuVhiI90bNmXYn4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lc1a89fsfa-7whwneqdab.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bqh2hkphpo", "Integrity": "vegRt2qgkUyawGZjWk9QvdAM8Ql6QUV8uDOzTAjXYb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2140, "LastWriteTime": "2025-06-24T01:28:09.6075769+00:00"}, "Fumg70EaCQ511mrIqz9Tb8lAm265lnCijUyVWRn6fNc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ksef9slfq8-1fm33xfb4x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77vv86mi5w", "Integrity": "s2+E2apeU/8UtRBgyWXPjk6JSsoklkkKiblSMsIIqx0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 180470, "LastWriteTime": "2025-06-24T01:28:09.7001623+00:00"}, "WyPSaWrLrDaElYdNMNM13Uh+foo2YYiDXCV7ylPGisY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dlqy23dccu-kcvkdr4alb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "55m1xc85z7", "Integrity": "WVd92+USYOsvgF1RgSbHvExhgdzRFshDmc1F6gdFxBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2354, "LastWriteTime": "2025-06-24T01:28:09.7173093+00:00"}, "hKZtAulb1VgfvJ/tk/ydSSXmz/dM3xUebSwQq6OT6YY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\58erqdvtin-gpga8vsymd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmxrtxfzjp", "Integrity": "AvR1RokpATMyFYF92UFBck8yadV27MEZe1mQw5y+IPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5661, "LastWriteTime": "2025-06-24T01:28:08.2346999+00:00"}, "nRRyAvbCYx9nKTFe+1uqlqj1WTjbAC2K/73e5JdyY2k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\odya9nxts3-abr508h4gv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bluxglujkc", "Integrity": "4cJH9oWzScvfq5sPDwLqijLKv4Kbn+eFNlihP39BDIE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2450, "LastWriteTime": "2025-06-24T01:28:08.2493824+00:00"}, "jFH2Ih+UQfuf6qRpHdWqL/Zbm+8J+wqdF5wtf+oWMsY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lhpdkrosjy-fjhf6hjcqm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e45jqid7h2", "Integrity": "Uu3HYh+pHjWKEq6sJhvrE4tH03XtN4W8kxSrUBQapyQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2110, "LastWriteTime": "2025-06-24T01:28:08.2993446+00:00"}, "Kdiz3PIs50g5kYeCNCEZm8dcXS/uh8BbeTjjAapQ5vo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bqol8tplix-1zqopzubo2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uwb3h5iqqo", "Integrity": "8htJbKBtKuj6YHTaWR0isn5Y9fRY17VTyW5F5u+k0Q8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2227, "LastWriteTime": "2025-06-24T01:28:08.3103032+00:00"}, "02GPp/fKxCpZHslXNK1kSO2whr3DVsUDfChfeHWb7cs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iwjvmfxqu3-nilb9xe1yl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvqbudk76z", "Integrity": "x44Wd45OmcsG3qNwVpj6F+7mk93lzssVclI1rAt7B+0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7620, "LastWriteTime": "2025-06-24T01:28:08.335539+00:00"}, "UxIO6OrHJV0ZvY3Zjx7+AbzN7r9Gq6ACaDKjYjPqUAo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9m1cnur73y-xpg3jz0dn2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "58jyewk2fk", "Integrity": "K1wD4MTMOw+faqanguKuQgdh2La9vlHySq9iZs2pqsA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2118, "LastWriteTime": "2025-06-24T01:28:08.3520371+00:00"}, "ddw63ljMYfBe4ByoCI6hFAomUe9AsOqXtBY5gI1NTZo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fw6jev8eke-83ia4yffoo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nk5xnik6d8", "Integrity": "d1mEv0Rt6WBdfpQL4rwhIoaDS6qWWmej9hJxIqnLTh0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3034, "LastWriteTime": "2025-06-24T01:28:08.5217212+00:00"}, "C4lX8+kU3J56z3qHY26gEfW7DqDGotLwnGZlNOHGgIE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xklfolbr8t-nbdqcvny6q.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "psyrbwnqwz", "Integrity": "F7z0D/oOHYmW0zj+p/WLcJjjicVz6wdx2buDaInw5W8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2964, "LastWriteTime": "2025-06-24T01:28:08.5504072+00:00"}, "wrEPBJKvPYCZrGHJNUsZ4xfHidg8NIFaPUuxscBlIik=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\nmba0r7o2l-apxeirum8l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0lwizuzr7t", "Integrity": "dKDYYhuZlnAhq5RZjmRjP/EsRLJWttjadZlXsM+HIEk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2192, "LastWriteTime": "2025-06-24T01:28:08.5609524+00:00"}, "bjUYvjlOXejWSYWTuqU4KJtkf2xd8GT1sPdU8g60wi0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vb7m02hyiv-bhp1yeu8fb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "exl3s6xjb9", "Integrity": "wtX+hXuUbcxGbmCDJRNlpESnKEId7ErP/IOBoEvCJ7A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 34087, "LastWriteTime": "2025-06-24T01:28:08.5834418+00:00"}, "VXM7gWdYLWa4PoE0ALaTjKYETWLL6GNTcF59VnD1JD0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5ctps1xp05-mvr25z4kt0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "agwxpzbopm", "Integrity": "dghGeAbBTXa1lrqBOZ7liVUBAR7r8HnYYw+2JkCUW/8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2147, "LastWriteTime": "2025-06-24T01:28:08.6352048+00:00"}, "EMSUvdRDzc6M8KMiwN93YL0pdgygRLA4Zp9P1+c0IB8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\royp2e729g-qk5bzw2lz9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw02lop9x2", "Integrity": "7CljJtoJ76Wy2TbElyvQjYTmJhCAfK9b6HGkQwgew8o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 20369, "LastWriteTime": "2025-06-24T01:28:08.6780624+00:00"}, "FjGwgR3bRUbx/Sq1KlHRqSEz8YzA/VSQutZyRoj8lGQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pexc7mp10a-luafopex6b.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pu1yxqgj95", "Integrity": "H5mIEJSMzvO1gBcLb9KNWSHdMWfZ7p6gbLV+wuXVVyA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2638, "LastWriteTime": "2025-06-24T01:28:08.7106445+00:00"}, "Fw2xA2vjmvfHlREy0Xr8/cNlDR9WXlTzBBeUfAyhQ6U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7gj2dpgwqs-j22agkcn9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2rvq94iv4", "Integrity": "0XgMu/eU9YU73vu82zjWMvSSvqS5VfvoG50jYZHW31I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2310, "LastWriteTime": "2025-06-24T01:28:08.7303074+00:00"}, "s3NqOwnvJxBH/vNWlOYbFueOfniEHzf2vqZ7gpFd1GE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\kbgplu5rwn-2k5z9g2rmq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tdrmp9329y", "Integrity": "354Er12tThW7oA0QVo5k3B3EcPpXERhtb8dlAPCtH0g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 45362, "LastWriteTime": "2025-06-24T01:28:08.7927629+00:00"}, "lI6YdwN/QpmwOk90oCvHibSF+VTGZcOSKJdswu87WRA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\j28a3ppivi-7e6tdrrvk0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k0ghmblhyu", "Integrity": "rCKyYStmdYCL3Rb5QMndexQo+r4CtD7OkZKREMn1LCc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 23893, "LastWriteTime": "2025-06-24T01:28:08.8502699+00:00"}, "VzwZmSV0MWrN6cK/49wmlPQILXyFyOyr+c8WCOtnGIo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jyffsad4v5-ophjv8dnro.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgipc54347", "Integrity": "RBa+y94ve8y1iS8PDXaSh/V96m4T0/iqzcyeL7E12w4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2252, "LastWriteTime": "2025-06-24T01:28:08.8957163+00:00"}, "zFrHAbu8yawCdmtBRr2pBMyyJEjGgLnomHONu6MPt9M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7xl7opbj6m-f6qrsh5ggz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1qcl35ck5", "Integrity": "5n/RRLbNsyNW3ibvrcqQcQtwj/VpOyqKM6Bb/MIqQLc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5410, "LastWriteTime": "2025-06-24T01:28:08.9228625+00:00"}, "dfjG3texyFVLJXsIGOABZ39h/K6ZhWEVxjQGToTMpGc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pv91aidw81-768xxo33e0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sbm9t2lxqb", "Integrity": "SO7rAf74h99cMMuws/ncxK+EwAJXVdgQJ5sAp1KSbXU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2554, "LastWriteTime": "2025-06-24T01:28:08.9534081+00:00"}, "olsK82KeZYNEyOXzjtLj5buPmn8VJ5U56EBxWsNVtxI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\nm3k1vr2b0-nolure4ku9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zo7gpxk5f", "Integrity": "0WF3VkObva6Wa3u4Vat19CvEfUrVJRidnq2b5GDlf6s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2489, "LastWriteTime": "2025-06-24T01:28:08.9855553+00:00"}, "229Od9yB+F+ph4h2vjfU0hicwN4/qup38PVCFEYJDNE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mv2ne30sfa-l12i2hq2kq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h8ipoprl0m", "Integrity": "kTRdqnkmDvNToK+j8y8JoBEkgz9+TJWxiZpZFGnsZ9I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10440, "LastWriteTime": "2025-06-24T01:28:09.0387296+00:00"}, "ArLwDjXc+itsEcx/wU9algm/swYOGmxy7G8zY+7HKrA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xm0a60puc7-93x5jg3mkp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uaiucbm1pv", "Integrity": "JvCs1LG2508Ne/b8JNQOKhGfyds6lO3lbhO4gfDAoFY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 16254, "LastWriteTime": "2025-06-24T01:28:09.0651639+00:00"}, "rsMc7VI4PyoWMnbdenKPeAy1/YEz9TZ7cyIHTEVvGT8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fyv9wlngmq-2cfqx8u8uj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5716h6abnu", "Integrity": "kTQaOlLKJ55KtOlrilATCAL1Itqma1FiPT6QGGiyGtA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 15709, "LastWriteTime": "2025-06-24T01:28:09.0823378+00:00"}, "VbsUVx+z5BVTtnq39+wEaKJXCsajhDYp7rcGWxMwcBg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qrxu2zwgr5-wyelmb42ye.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ozm5870g9d", "Integrity": "pIfsvI61HYodf9Y9VWYojaWDL65wjkRFjcxpTvEazOA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2695, "LastWriteTime": "2025-06-24T01:28:09.1087187+00:00"}, "lFDkulVvillks+KmyFLEG6G+xIBfGpcJMYQnW9tUMrA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ja52lhp99e-dst12xa6e5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "afv0r8aju3", "Integrity": "Fho5O/9o0iNDe3nq9dL374I8HzMvgHYf0XKv1mVHHes=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2459, "LastWriteTime": "2025-06-24T01:28:09.1308955+00:00"}, "iuLWeUDtEGuER3I31L4DRgFfb0t7rAe7rCcaNRm0ZAk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\g8d1ll62re-6ei8na5yhg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3vzd0bntcf", "Integrity": "MU3xjGMY8vD4xnfZH0irqgRDlIBPJurjfIRHMdFgLrE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2322, "LastWriteTime": "2025-06-24T01:28:09.15251+00:00"}, "Ljew092sXuByqE0JhMWTkyOKtnMri6+Ql/z61WFJBJI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\plvxa4s6ga-3eb8c3pfwe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t50l0nov1q", "Integrity": "EfBffVPWpe58xruJwJsNlZ9rzwMqGZx5/IAZ2lmbNLo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2267, "LastWriteTime": "2025-06-24T01:28:09.2498989+00:00"}, "HDElwgqMB5GVCco8Xv7WLVho5OsR8TfsqR5BLjxnA10=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\arw8hu9x14-171oaphs01.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cscfdewx0f", "Integrity": "hKxOOBQ/fSCsL2LrAMqY/2sULobybRjt4W14A9awCE0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2197, "LastWriteTime": "2025-06-24T01:28:08.2346999+00:00"}, "cyo+moE3B7rLzRs/nEBxFOpztmMpUHe2IzrVP5kXMjQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\il592650ow-1hrv129abp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tt4ixubui0", "Integrity": "8RH9fKYh+mRXmKintECdQg+YOjEP4UyJL72KP9CV1AA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2324, "LastWriteTime": "2025-06-24T01:28:08.2508032+00:00"}, "rxeHet2XlN9kQdw/w6qT7/yDV6Yim92rhnwChc2JXi4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ced8wcd8cu-atf14r4yhw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7l50o3qupp", "Integrity": "S0DHA/lyKN91O8N20+vhKfUiDaVYpC1L1K5kilpR13c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2652, "LastWriteTime": "2025-06-24T01:28:08.2826035+00:00"}, "ouI+kASjOEVyhwNWixPRdLBRe5ahrOn0LIP/UGtEKb0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\eh7hhep0d3-ac8qf4w6lx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n18lxojb0f", "Integrity": "+yyjpud2HwQ+OGPCdI/UqCTj+pb8244ITQWUvwfpbRw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 177338, "LastWriteTime": "2025-06-24T01:28:08.3345392+00:00"}, "21IRzMbtldKB5OSZiP9OXBYIMADRKYGTuXVgc0dFwLY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jyu1zp9qgf-nqh8s5tb94.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ax3au530m6", "Integrity": "XnRIHwY2UCoiFriXyUFvCdkZICFGHZEtMa9F8lvzbWw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 10532, "LastWriteTime": "2025-06-24T01:28:08.3562529+00:00"}, "nq7k+syR1727Pzot1MIUjiXkEt8V28ZghZ5T/rdgFD4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\p1o86wnkwn-61u88luwrz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bolqu0i7zp", "Integrity": "xVHyKZDBFzSye02g1tUAgVfqXS4Q71WdcXJ7u5BfzII=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2151, "LastWriteTime": "2025-06-24T01:28:08.3829586+00:00"}, "5qcdvRHoJcx8ATj2c45QUMbGLXdxKRvxeSy92WXfFuU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ylntj2h544-kkdt47cvjv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xz07b795u7", "Integrity": "M8ityBtfy534MWphTysAcVhdDUpR9qBJU8TgCh6Pqmw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2184, "LastWriteTime": "2025-06-24T01:28:08.7183729+00:00"}, "NMjU0avXnWsDTM7ezx3q04M+Mxdi90LOHKmM/xfIzpc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xgis4ptx7x-dhjbngzvrh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c4pgkkmsgv", "Integrity": "LKdHfLw90xpicOQyO0W2P3QJlBkiuIVnXgB7U6/Nh8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2952, "LastWriteTime": "2025-06-24T01:28:08.7567228+00:00"}, "nvYNf2mC97cmakf1t0/6PDifx1p17QrQVY2ONF2OYeY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8801tn65l9-9c7ym8z6z0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxdot1nk1o", "Integrity": "f63be/hihAtZ17hZpOPi6kiNztHMkZyzgQOztyfKpvQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2529, "LastWriteTime": "2025-06-24T01:28:08.7732239+00:00"}, "93jLV5FXnZcL5DCRquboBcsYu236YN4JA03uqj6uO6g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\v8iorw1ao4-74uvd51634.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dgxvrqhl7c", "Integrity": "41GOXv3/61q6PAei8t3AYDrEns2K97f8ymoD3ZV1qyI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2295, "LastWriteTime": "2025-06-24T01:28:08.8132736+00:00"}, "G2n8YWf/HH/gPAfp2hlt1nYMNFhWq5k5/ednWfSHazE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f6wx6gd1gt-su7r23l034.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0rv36z1ex9", "Integrity": "hHojCn0xiRCwWyLrslxq6PClbL7a/Q2omlr8wJOxKcc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 514592, "LastWriteTime": "2025-06-24T01:28:08.9391077+00:00"}, "rH22G4/d5tKtONZSeOZQkN8Jic7RiLiwNV66y2xBe0g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7x27t74nhm-1yccpcq7id.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zjhdfezxp8", "Integrity": "H3FtXAVgj7swGReRVg1moh35L+hmMgm+2VXR9Lkelvs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2255, "LastWriteTime": "2025-06-24T01:28:08.9574177+00:00"}, "Zxr/K+nN+zgzNveIVSN71gxbDxUZKL4/gba6Ayt67qQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ppfdnigb7q-55ue31v3pl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00652p439q", "Integrity": "oPNYwXcRoWl7uya/HXXYKERu94fKm0CZJ8hFL0CQFZk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2228, "LastWriteTime": "2025-06-24T01:28:08.984557+00:00"}, "bbjM1OnsfBg4nbmBvAp5NRftYPexfbgr6B/kLMz43CM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qdvfj0qa9d-akbtg6mu9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j4oh12472a", "Integrity": "nYIailQEVdO/2dsRaY1NdIhXFXX7uJcdj6Ucd719EiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23478, "LastWriteTime": "2025-06-24T01:28:08.9998852+00:00"}, "Vp903d/8QK6XwIeyzPckfUp2iQ6gw0b8eDKLomYlhNs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cn4p7z7pyl-c5gx1pj8rz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b1rbk16ps0", "Integrity": "YcohLbWIfGPVOKk2RB5lRX3xuPV9jkO8yA9rXZfUDBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 200887, "LastWriteTime": "2025-06-24T01:28:09.1802755+00:00"}, "QiYVHvMZREprK0xX+rQm3ylNBuJlisZkz55Q3wjdcxk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ipm780nllg-l1kk2a4a0t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qd7nlb140s", "Integrity": "4il47+VZ7DWYRBb7ei/QcLUSSnRYRpW6GrgUevs64i0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 149516, "LastWriteTime": "2025-06-24T01:28:09.1628116+00:00"}, "gR11tQz4SHQIkhuKZY5It12bxTqb53L8RkKKlE1uA4A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jef7gsrqq7-1qhdretbuh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hphvgmocym", "Integrity": "m+yGXO8YqCr6OeBaPgpxUw5KkhVZfiu/UFDLmIz62Lk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 19241, "LastWriteTime": "2025-06-24T01:28:09.2323581+00:00"}, "VZluyEEoPGga5jnX25Ix4q9gJMQE22KPoax871c+hpg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\laazswwohk-609xepspin.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8j4xfz7b8", "Integrity": "b4ces5UKtHhTT4focWQsQQ4peWgUs91fjW2pXqwd5qw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2300, "LastWriteTime": "2025-06-24T01:28:09.2481733+00:00"}, "GrXyvVAlsfqaK402yA0DR22AtV3wAUK1eiVldN3Ht38=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n4x5zi1hy5-emm1p2vibe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1o27p37ksp", "Integrity": "v+EzzPbHp6+i/owi3b7X51qyihZHGtRtET6End+DclI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 73778, "LastWriteTime": "2025-06-24T01:28:09.3186486+00:00"}, "Yjybe1JNuLrlMcPFqvpYQTLbL5ufAE6XgN8xeQHdI0o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\awerrgejpl-68n5xxnez7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o0e1d0n0id", "Integrity": "OJpuywD4IWo13VL/M7DVr0mfCvWbnrbURkle2C5SC9E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2285, "LastWriteTime": "2025-06-24T01:28:09.345657+00:00"}, "EMzLmawnvfSPIvt94DYl6VWpo/nv26Al3vNlNSj0bmU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8nxszxol5d-zchzosoc8t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u4udsecrro", "Integrity": "zu3s3rgiI/1CU8rvuvxPstyBDrDdSOOmmY5JscBhKIY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21575, "LastWriteTime": "2025-06-24T01:28:09.4055053+00:00"}, "K7QyLSbESyl7yd7rsz4F5OD+ULiehT4E7+MX5hgnVN4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ezx0izcezf-mzcae7sc1w.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zt10t3qa5g", "Integrity": "R1Ji6MJf1BoMC8mughzPcjuSJd2ofWbxqP3nMQIfnhE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2547, "LastWriteTime": "2025-06-24T01:28:09.4253616+00:00"}, "eHzlYstXaOa/dfyarfDvgTZZ3+Qg8tj+BU+PIU7PO+0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\x959zs3735-av82mpp65m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1wbw7xae8", "Integrity": "+KKEcm6UbopA25E3V1r0Hz9bpPELXokumhb07RvsNAg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2324, "LastWriteTime": "2025-06-24T01:28:09.4417958+00:00"}, "6mv9UhOnt2mb2bBSXaRf4+jJuF2DJv8H1bkMCDJuRcU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4pab78fge1-03z9epkgeu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8685rjzp8e", "Integrity": "YLD6OG8G5k/hY1CmycBl2TwwBXlGeAf4pkos3ZLQ/xI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2249, "LastWriteTime": "2025-06-24T01:28:09.4568731+00:00"}, "N7PfvY4CavZXjKnad58W6v4ZQ5sSZejzGUl0OzbjDag=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hlv81p94us-tcb84raxfr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ir72gb1chy", "Integrity": "zr8zlBl43Vc05ZggODe9cCOPjq/dkWM58HYX7ofISKQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2122, "LastWriteTime": "2025-06-24T01:28:09.4668177+00:00"}, "Pl9VcqPouai04xVymfzPL2L/SByvXgSLKIgXIc84hik=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lx4q04c6vo-j0x2amkbr8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azdx7qdg1d", "Integrity": "+lvTsQQjzQqjIL0b1dDp2L70w9zLut+pm9Xgk05SGto=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14271, "LastWriteTime": "2025-06-24T01:28:09.4904051+00:00"}, "LRrLCi15J4UAGVRSd8uiWkMskOB76tCF8VAq3hnrd6c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ym2sc23egi-3mr0k4ql8l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "07vh6x72bn", "Integrity": "LJISaxlzzsg2/QrGJ9sXmDlBOSkXiCgKIZYTyakiYxU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 51105, "LastWriteTime": "2025-06-24T01:28:09.544718+00:00"}, "3MbAyyxktcHgLduldEUuuW+MzrLjie8/5+l0HJayHu0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\e0p52hsb7u-tebc67toix.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7o4w1c655", "Integrity": "d6jaxiV4B/7ivYnqIJEugtxkYC0Ke2ySkqb530zduCc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2360, "LastWriteTime": "2025-06-24T01:28:08.2336931+00:00"}, "89zfMZ2fCbG4TS1wNVVe9xutOl660qpn5aZTlaWZiYM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\axs689n286-mylfx8o0as.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gb3ss7nrty", "Integrity": "zEfMTLs8gexhvPxrVpWgS4sJWpHVlQvdO3NhEX6Apdw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2168, "LastWriteTime": "2025-06-24T01:28:08.2508032+00:00"}, "myv54jkGspqf00+eQBUa9cIpnSwdeL6VF65c2tJn9OQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9l7ueuc70-wj73nmpklo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3pvi7gcbsb", "Integrity": "CWjbsZdyuHfxzuNNN3TW5gU+J85Ei81pEUkRqOOaZjw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 8370, "LastWriteTime": "2025-06-24T01:28:08.2835389+00:00"}, "jq/Fka80S+tA7fDoIkwZFXNnRCllqQ7I0qWg6AMPpKg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cajl2o1e7z-fxengc3wyj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m0kte91qtt", "Integrity": "lix+/U0hL690/ReuuOA+NURVSh0IVSPJh0gK2WN+aj0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2111, "LastWriteTime": "2025-06-24T01:28:08.3147972+00:00"}, "aXZ1pQAJ256uY22qmAmETA30iTEDJrRN6LeM4KH/5vY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sadg8p8gec-nphd9f66p0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zzh8nxx6oi", "Integrity": "HzgJnoptUX805KkbZZpCJGQ1DPanHrSj/0of3xNCaYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2266, "LastWriteTime": "2025-06-24T01:28:08.3447937+00:00"}, "uRFuzF4L1iLnwA91S+09CPXALisgFO6GRoBc1Q16fq0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzgeww6nnf-8cl47m4jkh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yoebce8vme", "Integrity": "IuOAYSA7zT4MekVsU+fb6GR/9hV5tJSlCUI8cf4HB74=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2197, "LastWriteTime": "2025-06-24T01:28:08.350529+00:00"}, "SWjw0HZnyLTWzP8BL8gkkcx2/nPznWpNwrTDbhmhr00=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bdjfymnqrw-3yhl940msb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sfnr940nm4", "Integrity": "aKii++1t3eIFbfJ8kxXNztrAmbDf5lCnEWFPG2OiWPE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4009, "LastWriteTime": "2025-06-24T01:28:08.4000796+00:00"}, "c4WoKs1YJR170j+OBg5Rg1bC0ubhN6Zd1LsOqOXpWAA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gwugds5sw2-75yss9zhoy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u69fk6s3z8", "Integrity": "ujcxkCNExrbgBUktEcT8sIcBnXCAqDQpFYtDsYr/DkQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2228, "LastWriteTime": "2025-06-24T01:28:08.4061912+00:00"}, "B5k6SbYVXxIWvrDagS0mZA8nyQqTc81TH3VVB/v8Rpw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3kjfjnpc9x-i6esjxgs2i.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8k<PERSON><PERSON><PERSON><PERSON>", "Integrity": "OP+3WHGSLQ714XTMeRxcZYvBQFKxupqkANRGFfci+hI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2385, "LastWriteTime": "2025-06-24T01:28:08.4359593+00:00"}, "WAgM7D2oZQTD0WES+2s26i96lZx4dyXgII4RZ7Kydhk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ddncr1chdl-5xzb5f7wvr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3q3icw7s5t", "Integrity": "W1+mdvDOSqtF5vDo7RbvpKKHcGBwhHhtZyebziHx+/U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2482, "LastWriteTime": "2025-06-24T01:28:08.4528743+00:00"}, "jKTdzn6fGGR5psLmvOYqCFRqwk4GXFtcb2OjChHxEe8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tb1rghhwn6-uk4ktf4545.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkoq6vbpco", "Integrity": "7ZB8I+L31+JpElgTdmIsmbYiHU8Hq1NNWq3E2hpxfnQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2308, "LastWriteTime": "2025-06-24T01:28:08.478383+00:00"}, "0zA5j0qRIZuepAqfUDiNEqXSILUy53ho3LhS6JqD36s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\uh9y7ps724-ngnkh62a7t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qipu9hrpsw", "Integrity": "sln492p7YGgUlXl75mNzNha0rQHV+hKkNEaF22Mv3Ro=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2348, "LastWriteTime": "2025-06-24T01:28:08.5081586+00:00"}, "zz4iXir6kNoiitg3TizA04RIb293i4UJablH5e+TORk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dc9ab4adpy-tyrk4ywyu5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqur2gucz9", "Integrity": "azbHFvj0G2FOt+zpbeKcqhQJDzyG/31RNStqOIkWSHk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2843, "LastWriteTime": "2025-06-24T01:28:08.5259146+00:00"}, "bvwztOaeKi9x8FL95odM5tNrM73sbZlhjPlLtgyi4sk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7qt3udra8g-ekfbtxon3o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mazk4w0jj", "Integrity": "f0TlS7a0kYBeQmpINNfz0Ay5ZCIVQoqSbfhAaFIHInY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4201, "LastWriteTime": "2025-06-24T01:28:08.5644821+00:00"}, "uAD3rhh0oapof+pOc3cGjrRn6upH4FkxtEwlV6iR108=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7ycekvj0yv-4isf5pcuol.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wecjxff8x6", "Integrity": "aO2H3P36KeeCoaSb5fPLtgdR3F6Si+/dCry8yVAOyAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11741, "LastWriteTime": "2025-06-24T01:28:08.6198645+00:00"}, "WF/Cbs23ioF2Jxqnhpvs/bEOnipuL6zoczb7+LXOHLM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xw3xqrs1gn-u7plvjpova.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0t6qsm<PERSON><PERSON>", "Integrity": "9BjRdvYM2FEgBAfyV2JzkFm8EfyJfyI6Rpdt2w1ZHmg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2513, "LastWriteTime": "2025-06-24T01:28:09.0507191+00:00"}, "qI4D7Iq1zJYMJwj+ij3XlnWSGerrtrAr2hSQj9LkDhc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xv769p21qu-epnagkv91l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4kcqoa988", "Integrity": "5IpB6j9TP4oWcspGDVI/FYwRJvK44Ca0Sglh/sypQz4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14744, "LastWriteTime": "2025-06-24T01:28:09.0663362+00:00"}, "nsLNS3IWU5+3BPtXaJxhMYnZrVXrEqWEAXcIsV9Vc8k=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iqwuxofz82-u6wau0ktb7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b12xqtnz2p", "Integrity": "cVkmRyxFNqeMxwYJp1BTXbZkqgQXQz6TfXtHtPjyJkA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 25996, "LastWriteTime": "2025-06-24T01:28:09.086866+00:00"}, "AjGHsm2VrRteSnTEi4z5kcH6kSBnq9BZ7iRK5m6gJnU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0tq3ubgpl3-710onjtcga.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wu8<PERSON><PERSON><PERSON><PERSON>", "Integrity": "WCtnSrphKRoxTEOreGgMgEpNFHYkj/yE2QhdPZPtHmU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1394567, "LastWriteTime": "2025-06-24T01:28:09.6745526+00:00"}, "Srg6SeIz3ss6L167LQcLbxcS9YiRq4uJ8z1PreYBJ4s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\12gy8c80sd-4rejzitfsn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wcqmrrwexh", "Integrity": "EApuGNk8AvmCwl07TLFJG222JaFOdNOcFoqhIUQeBzM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 11445, "LastWriteTime": "2025-06-24T01:28:09.6894507+00:00"}, "DSIaVizb5/7UgeKxEILL2RQUQ203CdZbx5vjjX1d4+s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mrmnme9fy7-3gypxjnyzv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hllvpd1438", "Integrity": "62MDqadOj+/GIGmhftV1peTz0tId0vCBCXCHa38OKb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 18726, "LastWriteTime": "2025-06-24T01:28:09.7090723+00:00"}, "WxRj6ojouuyJZ18kFRVEIJmFsH7Xm8uARB1lzj+IDE4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f2nmvc6iyo-vpgbraawsm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2hpdksa4ob", "Integrity": "NuanXv5ObHQUgmzahq06eVrXcG+WoMunixO8gmQKGMQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 36160, "LastWriteTime": "2025-06-24T01:28:09.7345801+00:00"}, "OxJ/KUyrigjk+tMl5JDW3fC/0mzwJ3qRB6h0gYX0DQ8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\92k36dsjrc-61e7sdubtr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ahan8gc0vy", "Integrity": "zpxDddN4c/cNCws/EZSTYxW//AVvfrNfBVrupVC31kc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1154460, "LastWriteTime": "2025-06-24T01:28:10.1993525+00:00"}, "2k2FAziScgpLYJ3Cnnw0Ou+oBgvVgkcnC7H7qrRTlGE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mgqvc88zci-ywccyuk0ea.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvhsn92mqz", "Integrity": "e9swblY/HDrM4H+jYQUqr8xKrdMNLra5T0+d7BOz+2c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 64973, "LastWriteTime": "2025-06-24T01:28:10.2300548+00:00"}, "+5J4Yedsjg4/0m/Wgz5mwdKads9PVQjYILst+gbZLFI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\v9ucdunf1s-nmlfvbueei.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilzgw5gy6t", "Integrity": "yhOL83W6Mgf0jJLwHzMZGA4NkEfwQoUJmCpJRya8LBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 100294, "LastWriteTime": "2025-06-24T01:28:10.2837494+00:00"}, "h0RKg9kJoogP/ZPPBmarw5hBu2BVmbGAsjHCYGpoxQ0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\90hp2p71hu-tjcz0u77k5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "su9h2nea1m", "Integrity": "JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 333110, "LastWriteTime": "2025-06-24T01:28:10.3526377+00:00"}, "OO/VXzKuuPGeqRxEG6HK7A3oKhZtUlvoIsPyAxZe4J0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iolod2it8d-tptq2av103.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnxfkgr4e8", "Integrity": "G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 196037, "LastWriteTime": "2025-06-24T01:28:10.4069595+00:00"}, "WcKcMFPKNw5jrV8/KYlnDGBxZjHvxIjaz94M2JPqCXk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\j19hqecu3k-lfu7j35m59.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v385ycndre", "Integrity": "S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 317618, "LastWriteTime": "2025-06-24T01:28:08.336539+00:00"}, "XrVt9wmYnXFkNI3qATGIEHADBBIfS5VgBvfgFFugz/Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\i4ozfjtw4i-oyz0vx2fzb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/emcc-props.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\emcc-props.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6rw5k6cf8e", "Integrity": "7t6AVk6lvrWEqY7hRavzlgS107PQ4doQEFxFK3dDtRQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\emcc-props.json", "FileLength": 592, "LastWriteTime": "2025-06-24T01:28:08.3525587+00:00"}, "YgqynSLV7lf9KvKyBg/9ljLcastpJaIzfm79W4KTP8o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xvf58668z8-xlg82bhyzu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicApp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "czysu3goln", "Integrity": "6JXSEqIgn32TCrx7cAbwsRyNhc3XueBGnueAPCvDv/0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.wasm", "FileLength": 30262, "LastWriteTime": "2025-06-24T03:13:31.5849853+00:00"}, "V3+/m3VOWp/bW9kQdwq/GUZnwZzfNlQKziqBXyzXI5I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bqkz6kxeo8-4wiyj2j1kl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicApp.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y2i06p4p2q", "Integrity": "dlpBldrqa7yjf6QippD8FSXh9UYxNdlhUYPMP0p8DS8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.pdb", "FileLength": 34108, "LastWriteTime": "2025-06-24T03:13:31.6028688+00:00"}, "mAl99p0q+x6K5vXwMf8mt8KB1NqqprY3rsU05oJJtVU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0qonx4ygs9-oq1umnodzw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicCommon.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hx0p9webfx", "Integrity": "C2ZAT1hGIxmbC+d54qyPd7Mbj0OG4a7ftjI1X/ZpXHk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.wasm", "FileLength": 5224, "LastWriteTime": "2025-06-24T03:12:58.7727113+00:00"}, "wvlj/m5CGrMZAfO2vfbFt7+VFjRIGI6P5XepObPtnfQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\esi1du385w-hq2xpbbq05.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicCommon.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ev6l48r3jg", "Integrity": "9tUAquHyfDqw44kIZodmHifFMLnxsuqMMmLXe84m95k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.pdb", "FileLength": 7530, "LastWriteTime": "2025-06-24T03:12:58.7852531+00:00"}, "GPpxyzilD84XqFxgqQq0kSgUkkzUhJABx8Osc/GNwic=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tga4xb2zqw-rqkute32mq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lngehzhi6h", "Integrity": "0fgMAw86pUBg6ANpZf2giALSXklLFO5e8Xeclab/bwg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 9455, "LastWriteTime": "2025-06-24T03:13:47.7861333+00:00"}}, "CachedCopyCandidates": {}}