{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["suGVKyj6fA9OlTxlrpay0uBIbtz2Oq55Fi494wmK0/g=", "d/+XztcOjoyF0V5qqBChR+Og4tKfSlsn8Ra788FZTy8=", "R8J5cUuloW59JxwJgNFm17WxTrpZERObcjH7Kr7NBhs=", "NVaBayAYk0367JYMIaGP1Acel71P5MjMRx3mamHv9W8=", "NE5rAppZMNatPOe3NXf6GxRnVuoKopKaLhn2Xf3rmx8=", "9TDaKnQD8eLefGVqpppUlJVHbS5pm3g1UN5L5HsdFXE=", "A1I86xjbniFh4x/zl1Gwh+0/68mohvcDSrMjkKfEZMI=", "Q23ixKfzshctnBo/7wKIvtrDEXKoy1Wj1tr0AnmLJ50=", "MFPml/qxbss8Uq5R9nA0SFKT8PebjG+7uYvh6sZSv7U=", "jC73wCojyvtcOd2w2seIHWPr5G2Bqry92cOyFanJEtA=", "IL/Ql+gEGVSQd3uP3wmBVMuFNZ/FD9EdLtDwC5Jj8zA=", "b8Hcm00ACY69x0QtywSLlAGviOoeWN72KHAieEi40JE=", "MswGz7AmMhABObezS+EHnLnoocqy6nFw5BFTasnY1vU=", "/Znw85f3/5JuNiZVoNK2cWIElMZry4qpt2ms8lIObGk=", "gTV+R/KfiXdfMtM5pgQh1miS6lTN4VYK1Y/SoNgXBRc=", "QDzzFQcNMwKP/Acveug5YfiFqwrzrt7FZ0HkHY7Nb6A=", "hg4QwqxRGGNOSgUxf+WjsPugOkj08LdShSphhw0n8zM=", "mXnZDdjceaig1HgoFmA5Lfc2OrRTydLclR9aghbraL0=", "4GZmYdKXhGTY3Y0EKcfcVNvOckseP60uUCcxlv/0bQs=", "7ei6Z6sbQO2Tn92rRuvCUjS57yQ/HlYDrGqCvlFlW1s=", "3SQsWnMj+huHQRz3Ys88453YWFLgoVRZr7rkfeDv+2Y=", "JCPJweUsw/pwinIbL6QRtuSFNabkg6kmy4UCv5NCesA=", "l2xHKQ6wo3z+0SCnKQfs7CtjbHWqt7jE+OWpvqjV6gM=", "0gRCxTcL5jJX7Mu/za2fwvHRY44rWhgRvAFFGpuM6rs=", "jS5h+vQnflTN4/iNta+/kmNUOsIdyofV2odEVx/CXy4=", "NpX3HSmRpc48c2noQn4lCuFY5GiLoKuOVG1r1AvvrgM=", "p/NqPSHnh774GS+JG7ZlII1zJvK9olPCLDIbmUfvP/s=", "omZaG5eX2uIfpJbgRNBmspuvnyfjfy1Ny1WkJDuPBzQ=", "tdiG3O4sMXZ1mvUgfrCHkTkKsFUX0OliGRDgYL2eeh8=", "DqSkUC640/DYT99k7uiU0AcCfzRYqbORo9IcVxrV/Dc=", "KnrvpVoPlggspnAVDRYM4KsEJOQHqmf/RxQElWapi+o=", "T89wUbwlsMmk3SMw1QY/NzzZgMkb7IsCjCb/bYKo9KA=", "/pEKx4KGK1DITSFDVvp5obnTHD79xJXUO+3jE8RTcFk=", "kRGqZw70tmG5lr389hwYCoWIX5rUlBsmilZNtUPu7/4=", "YdPFBVd4z0a6AXBFtIaSSURNHH189XX7JKiUterD8O8=", "Hy86tNizE2eLUg7ZD7rsWewChvo7z3Hvz5VcJHHVorg=", "mFATyr4mXfptJmWcCLYQU4KFDY1aKQKqInCEEj0uWdc=", "aG6DKYEMKvlS0Fnx1tpBgjCjPWBchQyFmUr5J5AQAE0=", "A1FstUbnZGVS7Q+mqvCyBZcd5QMEQTpG8VVYNM6H/Eg=", "RE7EuoCYnlEg5OJ7PBgSGGYFDjVcr7UqyYMzUgIe8fo=", "n7zZV50dsllZAIHiZQliznvZ5TvhOnodj/az+Ulnk48=", "OnDIfY9MxGYFgKoZzmGLUPp3XVthILVzWukUe+LEpwY=", "HI2/2q5yWD9wRo1FM8YZaZU2EQf7ikKvhWLKAxmXwMo=", "tbUEJhedAnTVTZTzGjAWjW6eLKI1urfJsAhtWF1+8IU=", "ebA0UlYZaXGeCgc/SjQZiy+sTPUTlRoJkn1yZApVMQM=", "m/jf+k5cyBA/oT9mUDdwrj7rtDyDOiy5DAgVnhD7//w=", "rmZBQZ5kIvTJSgM3c2BXSQkt+FsRu9dUalB+RT207p0=", "K/B9cTjRx6zEEMd4Aizv3oSWCBDeopIY8HqjEM3Jyhs=", "X9CXXzElY61Ay/Nh7nW921rBBzQGwpthN5mrGcYqDmM=", "W7KMIfioodHcPNw799CgaWJLOyLDFb5+ncvi1r9niLc=", "8njtl3B8aXgPt9gSsLQy29A/X7DOA+wQQnfxKUDdnkU=", "mcri73tVilaAb8pdT23Lo2H4K3AvSAe88hxoNL2GVlI=", "XU11UlxE3K2NuCb/+mSqLNfSsndz0xB37f4Yvpx4YsA=", "ywfSEdPTWZ/Z09L6tPJUIPumMhaj3jEOwPJGOpZmXGs=", "rrlRDZsDWvDsqbzpkLwPpatKJ6WKGRIkiVxtgSeOXH0=", "QOTr+CJwSFlgDCfJYWMFv78IGBLnXiQsvyfiKTNX/5A=", "DpYcYm/g08gV1/kynChgEfNNwiev9pyUcs7rfw+a0F8=", "VDT2iEJ7b4fb/PCkuoVbs6l7Vn09zsmHvPiK3vyj5OM=", "hguYVH5Glgal/44acpYKi9fwy3xfZw7ZvWU22JDnfcU=", "UmD2uUjYnjsgyl9os21mykwvRpD5RkWmyYIAmbCZmdE=", "pkcAKXpZW5jN+TOqZhBqYtpq8JBzEzU5I0Q9b0QprIk=", "KkfBLAM1DqCT5/xNyBV8OlidmkUxZSyf5JHfufJsWd4=", "gTJEtMwX6qULN+e+9zv6UfP3LVUi3uYJ9KYSeOolTHg=", "ssqCQre3NCLiXoFQ4PDhx3HrttGnHbt9AU2SRKnF/XM=", "z8WECxUjbcuBRGav3k8WTXox7IU6gRmCXljrhwFpwuA=", "ypmYM1y1B3evoWiVoFxz/ZodY/NUeoy1A20mg1TMMp0=", "WLn93TsOFzmq/BBf/Ot7N+sQpA0nIr0OaCI9WYPWTUc=", "MhHUUqmyaAq/eSkuwMS0bE1Y1LQ9IfuQ8oRijeoOiyk=", "ORMZEZJT0nKTf9Npxec163zfxueJavLdCBJbBtoRd9U=", "2GFu3w/iqpjQxR0Q8C1O966Zi+nutxMZTQ1eLRH7ZNE=", "GRvAGIDcPiUwLPrVES+61rEIMe+4lEyncLz6dLp51dE=", "WWjgWlV2Plmsmvw/yq6OqUWjooUZefRj2QGzACaUwWg=", "bxQtdc4BTduZ7nos5NsmJhPt4zawDi8XWLTgLZZTUGE=", "VWOZ7kC/q50Yeb2rGu2gPmSFyI9ejv9Qn3LI5mlbi9A=", "PhCWSGI+2lY6hhV10eY5vaZoM7OYquKTUy4wcMfbMJA=", "0N43u1ipnejVQ/ciOzP1XyZZNvcVcdfGFfo6Ygu+mQY=", "OmKx/DsadrGaYi0n4Z1xsHF5u7qPBkLUxxmR5uApfXk=", "Ojb4ybqPCAfUX5VpgH4anXq8PMSczaNRAZknPVTPHTU=", "bKogHSNeMwXxNjg/qy9WAFwLiSyjzdQtFW+jadbKw98=", "RYMJ9MxHC8ZDVdXBX94DhqnP2OtHNILe1FXrysX/Ebs=", "DEldewYsmUEJxwgVj2fmv6LxsE6YNr+bnWLzxhS/cEM=", "EcULPR5+PCyEZdlWkyqKENU5NHutXs4mWQqYbfX//Rs=", "Xkl+8jcIDgagYcFjVJ2cq3mhCri20DpNjeREHPm1pYY=", "BS8h80bqhhN6h5okst2ZtwfOGeKZLPlsNL6jj7KrCs0=", "odm4qk1d8Gvh8wgF+1C3gadQYTvC0EdqdUh3/u8i7nQ=", "OSzpwEcmBdSV50JTGNhNpFIuGhZyppj+mptHrBNDxnc=", "h4Jae7A4nrJFWjW2Y/EVwjdMMdjRJbMJ68IK6rDttJo=", "8VLpvOcZz329y/M813y1PF5xL76Z/AjdPNPe5nI+IOM=", "PveHNsfVCuA5CW3nKrckEYyfLkppUNHkeee9tHL6nRU=", "KqQ2JSAPLvFHbv7+oWDUms64/9kVqjzB9XlTKNNyI3A=", "I3k7NZ3C02pjRWrWhIrLTtu6hjrMEpxwgDZEsFI28go=", "ER7ccLTGA6ShE8wsOGHAk+qkESkc8ygqSIgTuskmCkg=", "3Pe7ETD5eqsia+C8DxN9WTJHgTP+b/FF6TjiFMI2mdI=", "8XHacn5O5VIRDaOV0WN9LsQMT6VbwbKWmAgeiBUeMRo=", "r1qdxw/Moh3+ZTLQn2YbZMT1oKjqZYtZoC3n15BQl4w=", "0uN1O1dMf9SBcnxvYsJ6KBs7rZreLHzuMenJenDU/DI=", "kInm19DfBWd5vSh7hojPctrX0oSDM33BgfzL9tJ1Cz4=", "JRft0omf0vjTr6hefc5FRcFIa9SG/n6pS1Aek8GE5kU=", "RGKCPHlTznDOjRwiRoFAdl619P2BcCqjMSlP7LdyUqs=", "zH9xFoheu+cR7cLW2YVDKlyk7FwrZ9v1TeJf/+fcMCs=", "WX59p6mhnvrYLednG8RG9HR5riStsXaegZqd2rhvkjM=", "yRr7VHjjnkfxhrw3q769Z8siyA68qJku8WEY0taw81A=", "4eQsy+zLddDWSErVpGBnwXmZAPXhsY+UeK51++lyx10=", "6Ej0iKLzhGWZk0pbgy3Fz5BskeQ0abPpl+7Fjbzt6sw=", "2EpWqDjVICU8BuhvhgGtHFP4rRvSViteJ4hYAbPi/O4=", "i1H+f8mPArFOpg56O3wYq2w/qnOvb6z8kUMG9zFzdwM=", "DLckMyJW7GQ5g9RhJXRaJ+JXRu6vZxE0h39hI6NtLL4=", "g8U7buopYy2/IE9+7YRi9DHTgexo4H7q6ZMDfT6JNP4=", "S+ZO7H3VVwjsikBlMUDRvkLUlGzi0X47gIYmUOo3VuM=", "iXNlBYyxcu+m+foDYb+JrHl9b3llKBItoCLPYqrrwFc=", "qnWFHzXzypGb6uqfIL1V5zKxd066ZZbpWhwKl4nmC+c=", "v7N4eRMasxunARofFbEcGiY6RjLSkgJF3LUsNgcocCU=", "2lLrVZljbdLq2+d4fGroBuGoAv4/exDt0Y40pz3Klns=", "Jk0acYXGCqHSHHSautXQ2fsJYyK3HJGOmimIb8haKLw=", "HAgRv7zbU7BUxYJnGBiPi30nzeq/NT/O6Obsique3Sw=", "7QlRx7XgD4q1lwKFsiiuH/LfBjtyBYn0v7j+w9bm8g8=", "rgrdfkRQ7MVTdTu1tWcpVpijPNL7G4Nb+xvINBQ5s7c=", "iodmooGmhjpTjBnJqowXEn3AqzShQHJGCej45YJqdBw=", "IRsXdJaFUIIb9acFA1ePkNZRvbqczfWkh8lLIQTLeWE=", "vadKruhwbY0IYAeWVm46IVbOgedhY9atmKtnQgmnDIo=", "erXtgJPGJn5L8BwUqXn45dn+w2AjxwDluJEcg/R6up8=", "3zoyrvmPDyacC5QTHfuqlHaFIOHaxDun0NAZ2oFZIOs=", "YnAfzD+Fi74+H8jQE2BleLKOJ+qwgL1MLURmmv3IQW0=", "30VuK3lzS2/2PlzpGs05G8w131EAn81TM3ar1E0dQPM=", "KBq5evUzAj/KV2rUiZDDHtaNj98R4x5Qu3LfSoZB46E=", "Q8kF5E27NAL2xylugQ/f/MN36f0mtXisrFvBB/EYCnA=", "CK5UxQ8R34SgXBsajOnSvNy3/5auQwmczzy7VX4OXvo=", "V9jxKdGYG9nz3lNRHqrhJjMklz76M63vOM+MQXuDkxM=", "5DC3PZxkaYfzw18xh8EJXx0CR27O4HylpNrPMA9SUAs=", "0uwH7Si3kzfz8EGnuuLuetPgjmWqxAMsUU9XYdY7/9w=", "ofS0WdWyWecCu70b635LWtHzsA7IBxwF8ZOhL6YFCLY=", "pvDLqMxpuqHST5OKQ+JzSgGP/QKpA2IQTvZgZgL+75M=", "ZOueVk480s7SsSsD2QcGpXSIOJwqEMlHTOAsoTvSOas=", "B2Tdk/LihYsO+HLHvIg/JaXZj2l95JMu7EuHhs9+8uY=", "VjWau2rOritQzYgYm5/VmedhN2bbo2hdxEznrnXGf5I=", "5HDcz/NQ7D2QiH4or7IDBdvj4nUF/TYMtRggdpZFhD4=", "Z44FvjrzNwyqVKZGZKTmCMlUugu9GsUf0PaF+fg1GEs=", "VQvyWvPQTf8tDg2efb2krjrACrgp2hKSr9X4p61x1m8=", "vR4OMKIbjPaQW4Cjqwg0yU2DlAVHUco7uMGymL79gKo=", "1zbZ4p8WTEiKEygaDzSS2sD1y9QShdVbDeP1teYO1b4=", "SDmFoDnKDFMZNLAC4fmXTfV30V9XyDey7qPYuA6gnz4=", "GiQObgNhS4wt7qoJoLTr7SmnE2wIn6E9X84Nlw4LowA=", "ipz8M7nCswT7FL1WA4DAIqy5wNDWMax4wdNInfr1gD8=", "H0Jg/v7cveR/JqB+Imcz1p3AGIfC2+h7AvRbO+t16qQ=", "kZP/NibBY459sPh7jqSsr3mlIy5Vy/vMW5nC4bgvwWg=", "0WaIecFmXzXUI19TmQK5nUX2iC5lKTgwFWy4978F370=", "kD93VXZ4vYAmqDBk0KQ1F4DRZshARpy5TyrP+NOv+y4=", "kHRb/wrVYrRo6kvVtkcxx/uV7Os75AKhrY+f/FDVQyg=", "EK/6dskL7kSiqNifuGfh08DFihJdvp/jD+iTUVWpkHo=", "lIOVxpagencMANzuDgg/5TIzHO3Tops2EEjGV4r2bL8=", "2nDtXiv7iVSrT4nq1vHyhKBswqULGkMilSeICRjxsgE=", "KqHepDKNMHYJQch5NuCEBo5bvv4XR7eYRGvbif4/2uw=", "fvz74h0faQsXm7p3dXwUbqIk8r7KJsYS3jVRIGcv6/c=", "Gw4C/7sutwER6DNcIF5JI8x4J1Qk6fkzLzx1CWTtxLY=", "J3brsJaIEoOG9elMB8RFSD5kS7EkWYH3jX1OtwKP1/g=", "O2FPhkMiVAmYuahPO1jD28dGReRu5zBTxfxl80K7RhM=", "0uP7GvQykKqPxyksguEfSIHxwMeq5wR35sNqSkbF8qs=", "dbaPosbwvwQUhFA1B+ghuV4z8D4foJ0BmsK5MLOvrA8=", "kjwCC7vQ4/PLsCoIT34JarCiCpq9mBzBVYZShYxXi24=", "sIgSHkN5NkJh5R/aTQc3DpWtl/K1+I1rwMVnIj/7HFs=", "X9palUdnpJdANEBTturbb9Pj/AI/UAwe4gukbA5OCRo=", "Nudy1ghy0+tS2CogmjttxNMG85cLYcaX8dR5joJDDeg=", "qd1jVix8rcNNPi0O3KDlQmxl0NHZGk9x5NV4b5mb43I=", "ElGosWhQT7mqeA+hUIWbiDb1N4DF1uUPs7CrZutq3cM=", "C7XycsEtISpYqp24hjXfVPUDHEF/h2wL8cQorSnsGxE=", "3LC4Zzr38as0sa5vtFVqhgvffz7/+6cF2Bg+smc0qPU=", "/9tcpkxTe1rSv3N+84xXU4KQYxrjgYY22+jdDOUXcnQ=", "DrATue1a0FGIBak830CFfpR32TrylCtQ8PZwjrcFm0s=", "H4YEn5Ub+mZD6OGayR5t+2gGRbnlw21puLYV3QsqJQs=", "m8Sa6wRqckEWpp4hHTduJ8//yiXHUbc28v1GiGtVK14=", "SMSnV/B+xDSOvt2vJ4Ju1VktUNibrWZyxPpbHiYFYZ0=", "YwaDgLsUbJ9UasZ9Bzx5gfuHR+jfOf60VM2prrSd1C8=", "PWwqh0rlHNSIfTGiMdmHa9AUFpIfm3OTAPWIjsxDv98=", "3AK8R+x8WdZtyHgDa0ukNghbI1XMy2D9zePzRH/jFuU=", "FgdirFWxPnpQodns6UlZefVjrFNjc+efYNIebm03pmM=", "XYch4ziQC5mojSk7rl6f7irum057S/OHAaYb3++xZLE=", "0KPhhy3efW770EuTbZygACyc9Fwo5yKBcrxFBg5N5BY=", "gHcbE81AEQ04gkp4JBFU6S7xsl5TN5eSMn8VZ5vgpTw=", "I+M+Z0NCJ7KnBTo8wOVOYO36UZrlsh8sTPNPnOA1ovM=", "Q3xfcY2GWViFQYqPgklVzdTIp8ySuhrMo3psMtRyDFQ=", "9Gf0BgJNKO4J2UN9+JYJxiEnMQEqU7ihw9tzGMFRX50=", "bDMdfKeY3WL8OO5cpWqLvOV6VEMpR5/4FqmzB76qUOw=", "31MqCwaUjAjpzrDdiznKzT0u5XuLhoUaWr6vh3EsNjg=", "9q3bMGEJ47SyNBnjP4k+mFmOHytNlDt1/cWkTMkm7es=", "BP4c9f6W9UB5wJ8NnREpWnrhfofAADd4Q3iXLdPyb2o=", "KR1CjrOidRr3nX8dJNtkduNHnFuuywJLUfySput5Oaw=", "FEEzngVHGNRM1DGcPgXRCcy2LyfgNUzroZcUPLEbGs0=", "t057lzgAYxaVRPD7oAL+4mg0dZrsszXh9MAaMlKh2dg=", "KB+HNd8LJNeYMHbl6DDgaYlE7w0E1AS0Ue5JI+PFosA=", "UkljKT0qutOxGm7KTh2QD/P6wF+OO1AZcoWrhsVGCHA=", "T6jaYascYmoFvzTPaczv+fxSHz7NFbPXj4vQ8cElSas=", "Rmc9FJQFBz1JNR+Oao6tHxTa8EcVRj64ZCO/GjX7G2w=", "q1wZe8N1bQVgKgWYMXpQ97PYiYiXpxQk7wjECLVDOqY=", "N55gVNNcEN4zwYYtarw61mThAfzZQqTKRVfNPykLIb0=", "Xxc9i0Q/yRJgQ2pWWDGBopOaGGx3lvrhpHmOTgt1AMg=", "BiIF2EUqHDwyOin6+ObQPYTVjONVWY8toRzD9Sh7jWo=", "x3qRt9XilO0hqkImWvOGahw1pveiWAIWbb7nt33GSoM=", "XL0mzy355ysHKLeRd67hfKJzuaf9FHC0YSU4NHu3Oeo=", "0I+h9wCVBLTfC12syq+UkDF7jLQF32oG6rBfGLOa/VE=", "XMCbhombPJ/g9ve/zdmCI/UvBewblpuze3npAdS938A=", "mxNFGoA6fOTgONgE+NazQ2Vh1QLFG4ncXGcriNWpjIU=", "unMnHLPKJbkSDapNmUlAAtSqyjeXF43GTtqyZjqiLak=", "P6+k62q+OzPj6n3cu83fQlVmfmYJxai+DdIENy+BPoQ=", "6vddWOaQcoAChm6wlA6GaQUI55vspznU8KDS1iwhOzg=", "y+YiysIKWubX67dNZ6vriM4PJYMGoJSZLv1gmo/a/p4=", "tusun9F36tduSqW33KG1tqQFam6vn6ZEZDmQ8OZHRdQ=", "NZrZ9cqx3S0IR5SqpV6omER0zEP9VhqdUK6AdaDSq64=", "9rirKIzHB30SO1udX/zPQL+UvOYN2oZrviAcmcooyl8=", "VzriRgE9Q3E+G+CV/R5BII2ySrp0yhwrrBofTjvu/x0=", "UahY0lNSDucOdsNrZPw/zhPWJKK2GK1ZxJK6GB2iY5g=", "wWtaf0KRZhy/c0jGrLJqqStRRD+td7RPmF8r0pXdTaU=", "s1g1ycBT3OaGAQTXuuhvRANNoFixkmuAS6QEDgF334Q=", "nuL7tdpYSlytqQf0w6YCy6+C8PlzU2z2WIOQbRitptk=", "fWd1DTWVaC/OKTlnFMU7G8RlaB+CO9R6FqyaSaZOfqM=", "ONb7N/fJ84AonrokZAeih5vYjGbEEJB5Q8i9CJI/6jY=", "DsXYSll46VUxVSmvNAztr1TSS9a+GdLgHh7YQRX5vQg=", "HUExLFLCX0kLL1f/Z70I7iOyRquxDbwJczrm7xD3xD8=", "XdHRty8FjAxG8O8hRsAeLhiblJJDuvOKxMiRZyH7oaw=", "1bT2qZyfUNe5wcMubErOGst9STuyXUMVsJ62AHircYQ=", "9fOP4NM+Xm7yAFFbAVlJWBifgeq7Z9PEv6DwX8za+6U=", "ZLGNONgfmTJ28+MpBnmOnErXlD6srSnqhdJjjL0BOxc=", "iUjCzA8zuCeQnNLlsQ/mGY64sH+w3cPXzrPkFDDJVXk=", "APC+o8CyQU6YI8NUa8OLRBuiYWYU7u+b0u/hALM1YPQ="], "CachedAssets": {"suGVKyj6fA9OlTxlrpay0uBIbtz2Oq55Fi494wmK0/g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5j8dxmne5b-mv535bwyet.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ijom068", "Integrity": "+V2Lg1Jy/5iLhIkZHMvZU1uFPbr2UWCvWIR1xl5s8EE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 19025, "LastWriteTime": "2025-06-24T00:41:14.9565207+00:00"}, "d/+XztcOjoyF0V5qqBChR+Og4tKfSlsn8Ra788FZTy8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dy5lkhjkn1-xtjqgewnsy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ypltwg0or", "Integrity": "mTNQIg/OZDt0Q0dAy7dBwiQu2PV2LviEQDjvUWc6sJA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 17685, "LastWriteTime": "2025-06-24T00:41:14.9785924+00:00"}, "R8J5cUuloW59JxwJgNFm17WxTrpZERObcjH7Kr7NBhs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f17o09ymz1-vbl3iftxmx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2bq4ped28k", "Integrity": "Ll/BngIPnHtO6rbjdkXKhF2EYc27bIegGy+JdEoDUjs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 129423, "LastWriteTime": "2025-06-24T00:41:15.0287496+00:00"}, "NVaBayAYk0367JYMIaGP1Acel71P5MjMRx3mamHv9W8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f813afr1vj-i47vxqdqw2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iifampbfuf", "Integrity": "9kglTrQpUWHbUZooUGNxTRs8OZ+RQDXsyCrgBlLse3c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16301, "LastWriteTime": "2025-06-24T00:41:15.0360499+00:00"}, "NE5rAppZMNatPOe3NXf6GxRnVuoKopKaLhn2Xf3rmx8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1gdl188rtq-y9j53ldofb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "01u0wu79e2", "Integrity": "BKCzLjnDV40Tikw9Al6lUk5mePr4/nHVzrNv6at/I4A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 65400, "LastWriteTime": "2025-06-24T00:41:15.0560215+00:00"}, "9TDaKnQD8eLefGVqpppUlJVHbS5pm3g1UN5L5HsdFXE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\99xx5yiczq-3uudqrjyld.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aw1545j549", "Integrity": "2sDM+GiLqv0h1K5ltpSprM5ypTpQKddIZ/qeY7ES1Pg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 46105, "LastWriteTime": "2025-06-24T00:41:15.0682462+00:00"}, "A1I86xjbniFh4x/zl1Gwh+0/68mohvcDSrMjkKfEZMI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1jvnjphhuc-txus4zzmh1.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f996fn2i64", "Integrity": "ncBx47S75+HEGz5/+1KAk8NqZREAOIw1j0LT7UeLir0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2409, "LastWriteTime": "2025-06-24T00:41:15.0727503+00:00"}, "Q23ixKfzshctnBo/7wKIvtrDEXKoy1Wj1tr0AnmLJ50=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sd4w29iblj-4njtqvtvgx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ehpzq00vvn", "Integrity": "woWY7cPpxRwo/ZlBGIpiuVyrCcNVURoJEClmhSxYIT0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15095, "LastWriteTime": "2025-06-24T00:41:15.0973234+00:00"}, "MFPml/qxbss8Uq5R9nA0SFKT8PebjG+7uYvh6sZSv7U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qmrh3cyln1-8kr5d0tjmo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tvlzg9p4s", "Integrity": "SKcKAQ6unQQmWOLud3+yjljdvRq3k5HjYUL0Z0Ex8QM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 7793, "LastWriteTime": "2025-06-24T00:41:15.1086027+00:00"}, "jC73wCojyvtcOd2w2seIHWPr5G2Bqry92cOyFanJEtA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qq3w8mqh2l-0r3amze666.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dz6cxvyzbz", "Integrity": "WSuabncDxkAB8fqRIdPNHPgeAGnmfkzqrcBXgplQMGQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 13809, "LastWriteTime": "2025-06-24T00:41:15.1195871+00:00"}, "IL/Ql+gEGVSQd3uP3wmBVMuFNZ/FD9EdLtDwC5Jj8zA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f0p7mhideg-en8mb8dgz5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8oobv0w90v", "Integrity": "WO+uRYcj3Zb9HIK7aDnF+ZYPe+fyAeKo2LMHDHQRlOI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 7680, "LastWriteTime": "2025-06-24T00:41:15.1516901+00:00"}, "b8Hcm00ACY69x0QtywSLlAGviOoeWN72KHAieEi40JE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\luw69m5zpj-yy6f57640l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vliov49hve", "Integrity": "M1N3wrmu41ddGz5INp3pKS70tYR/Y+Xqu+oZ9rZqjZ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 7509, "LastWriteTime": "2025-06-24T00:41:15.1693731+00:00"}, "MswGz7AmMhABObezS+EHnLnoocqy6nFw5BFTasnY1vU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vhswmu8kpf-xqsu2wsvba.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6msqh3xb8j", "Integrity": "XoDoAbTIxo5MKAxsmkcf9azi6O5OLViGGKrBi2qMlgY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 35118, "LastWriteTime": "2025-06-24T00:41:15.212675+00:00"}, "/Znw85f3/5JuNiZVoNK2cWIElMZry4qpt2ms8lIObGk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fg6rcqyzob-kgyjb8k43h.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "56nyq42peo", "Integrity": "MwyC9p6nt0mGMqIypm+SnvG+21YdrXDmlVaZDNsWJeA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 20683, "LastWriteTime": "2025-06-24T00:41:15.236982+00:00"}, "gTV+R/KfiXdfMtM5pgQh1miS6lTN4VYK1Y/SoNgXBRc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rzc0mkqxf8-1c7ksbormu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvw1zdn8s9", "Integrity": "pyOZoIFEM9t5FDCjL1vt7pFHGrJ/aCpe5ncDLhyScEs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5094, "LastWriteTime": "2025-06-24T00:41:15.2515463+00:00"}, "QDzzFQcNMwKP/Acveug5YfiFqwrzrt7FZ0HkHY7Nb6A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pyud5iqoj0-rpvltkbyzt.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gmno2wz14c", "Integrity": "mL9aDIgzoCBBugdOwscAnV2L14lXopq1fPoBppkHjc0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 16310, "LastWriteTime": "2025-06-24T00:41:15.186238+00:00"}, "hg4QwqxRGGNOSgUxf+WjsPugOkj08LdShSphhw0n8zM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3wl2w0d9cv-i464dwxnbb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bbtd3i9alo", "Integrity": "p1Ah/YODlnwQ4s7t24etOtyb4hdzr3YlCHH3s8gUCH8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16109, "LastWriteTime": "2025-06-24T00:41:15.2096719+00:00"}, "mXnZDdjceaig1HgoFmA5Lfc2OrRTydLclR9aghbraL0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\r2q3mj3u9h-xlpspxuy08.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny3r3g6nhq", "Integrity": "z48FFALZ2sAP4Fd5H7/RhhuPDZBP1f3ES8esZSke/qU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 18424, "LastWriteTime": "2025-06-24T00:41:15.2344649+00:00"}, "4GZmYdKXhGTY3Y0EKcfcVNvOckseP60uUCcxlv/0bQs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rvv7resapu-tz325eqvv5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "76x5eflkem", "Integrity": "L/EpLGuZe59Ju8jspXqvtC8hdyOL8Zrhe8lxopsvj6w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 23619, "LastWriteTime": "2025-06-24T00:41:15.2503672+00:00"}, "7ei6Z6sbQO2Tn92rRuvCUjS57yQ/HlYDrGqCvlFlW1s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sx4xzqaqm5-jt8xzja2dj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2h4o821w0v", "Integrity": "WnOZRQAyyjOv8sTLVpC29t7cLD/gYEUsRWah0QSLSuk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 23295, "LastWriteTime": "2025-06-24T00:41:15.2585313+00:00"}, "3SQsWnMj+huHQRz3Ys88453YWFLgoVRZr7rkfeDv+2Y=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ta7m7j2gei-lsakbjp1fg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwlb4s9h23", "Integrity": "VXif+d8llcvt+N2pU6LUABQr1EUvnwTg27PGFGjJoWo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 14733, "LastWriteTime": "2025-06-24T00:41:15.2677859+00:00"}, "JCPJweUsw/pwinIbL6QRtuSFNabkg6kmy4UCv5NCesA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ldqgtsc41p-ae1qwufxjk.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xq5kas6hv", "Integrity": "dqz4oJ04lK2Swcgh3kNZ40yIAlv1JsGNeTCEQZQM3sk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24036, "LastWriteTime": "2025-06-24T00:41:15.2822018+00:00"}, "l2xHKQ6wo3z+0SCnKQfs7CtjbHWqt7jE+OWpvqjV6gM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ah5uj9fkaq-tr42ods1qv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qibamu24ww", "Integrity": "Lnnec2bcbNOfFQK9VYlTVxUF8DXy6CbINjLyVzg9GZ0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 6764, "LastWriteTime": "2025-06-24T00:41:15.2944245+00:00"}, "0gRCxTcL5jJX7Mu/za2fwvHRY44rWhgRvAFFGpuM6rs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rrp3ygrbh4-t3di59eis6.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Buttons.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Buttons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7klbf6wegy", "Integrity": "5wg6iRs8y4BMsOZWgbQUqC+zIx1CIV9rGYwK3zfZClg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Buttons.wasm", "FileLength": 64659, "LastWriteTime": "2025-06-24T00:41:15.2822018+00:00"}, "jS5h+vQnflTN4/iNta+/kmNUOsIdyofV2odEVx/CXy4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pxxrbnr171-76z3t3ul0w.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "adowunaa6s", "Integrity": "ogKIQZS23c6NxlSnR/P8Bc4hSMjUxKJHMu8tF4WBsKU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Calendars.wasm", "FileLength": 181536, "LastWriteTime": "2025-06-24T00:41:15.3472477+00:00"}, "NpX3HSmRpc48c2noQn4lCuFY5GiLoKuOVG1r1AvvrgM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jydetqvzvc-tb4icbsua9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "khsv1q0k1z", "Integrity": "CBVxhDdHMnzQmrd/LMpzQVWP9StZULDZPq+vGwArMQk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Core.wasm", "FileLength": 108873, "LastWriteTime": "2025-06-24T00:41:15.3919777+00:00"}, "p/NqPSHnh774GS+JG7ZlII1zJvK9olPCLDIbmUfvP/s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lcyu9ghdos-ldhtchhorc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "spu2wylyl7", "Integrity": "AECOTvY4/0LvEj0lBielluQuEqa0IVsqz2Jg7K3nVKc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Data.wasm", "FileLength": 129621, "LastWriteTime": "2025-06-24T00:41:15.4441269+00:00"}, "omZaG5eX2uIfpJbgRNBmspuvnyfjfy1Ny1WkJDuPBzQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3bohcw7c8u-3b3recd4c3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.DropDowns.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.DropDowns.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4xkphrrnbs", "Integrity": "KKP+UEyqZLYfWhqKcyTbmwqtGzvAXiCiCmens/OmU/c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.DropDowns.wasm", "FileLength": 267926, "LastWriteTime": "2025-06-24T00:41:15.0334215+00:00"}, "tdiG3O4sMXZ1mvUgfrCHkTkKsFUX0OliGRDgYL2eeh8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xnicbo8o6w-9027wsoep3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Inputs.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Inputs.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2pck3dc7ep", "Integrity": "usv/fCLyJFipe2YV6J/sbcUbt6OuimeVL+xPwxTurmo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Inputs.wasm", "FileLength": 225184, "LastWriteTime": "2025-06-24T00:41:15.1793682+00:00"}, "DqSkUC640/DYT99k7uiU0AcCfzRYqbORo9IcVxrV/Dc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\h6h4qv19aa-vei9li4mx3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Lists.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Lists.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jwi117nege", "Integrity": "Dd2QyhqLa6bjHqT3FQivDO1sF9QJMUoQ/lzak//drM4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Lists.wasm", "FileLength": 40049, "LastWriteTime": "2025-06-24T00:41:15.2053676+00:00"}, "KnrvpVoPlggspnAVDRYM4KsEJOQHqmf/RxQElWapi+o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cp5mb4volr-5v0k8dfq0z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Navigations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Navigations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yx60uf77an", "Integrity": "Js1zNfWitbEiO8677U9fWmb6EO810Gc7wzPRfZHZWfA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Navigations.wasm", "FileLength": 285456, "LastWriteTime": "2025-06-24T00:41:15.3602852+00:00"}, "T89wUbwlsMmk3SMw1QY/NzzZgMkb7IsCjCb/bYKo9KA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wgslxn3apm-yarh891x5f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Notifications.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Notifications.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lwtv2c719", "Integrity": "PZEimOu5xOABOKsE9gbNrXTketn5gWTgs4AvOt2aXw4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Notifications.wasm", "FileLength": 31431, "LastWriteTime": "2025-06-24T00:41:15.3899769+00:00"}, "/pEKx4KGK1DITSFDVvp5obnTHD79xJXUO+3jE8RTcFk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dwdcdv3wg8-7ksm1zkb1a.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Popups.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Popups.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5sxqodejev", "Integrity": "e8bvI/FFfp22BtOBJhZZ4ClFMW7/W7446ODHMbF3PEs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Popups.wasm", "FileLength": 50629, "LastWriteTime": "2025-06-24T00:41:15.4841592+00:00"}, "kRGqZw70tmG5lr389hwYCoWIX5rUlBsmilZNtUPu7/4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xsb0ndz1hr-7uae7ijupc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Schedule.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Schedule.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wszogpubm", "Integrity": "COXx/y6+q19shI6UEA79oHeqXcXQ4+IA8/TstpCggdc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Schedule.wasm", "FileLength": 322664, "LastWriteTime": "2025-06-24T00:41:15.6786107+00:00"}, "YdPFBVd4z0a6AXBFtIaSSURNHH189XX7JKiUterD8O8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jkmpkjk4xk-pcebwu17mj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Spinner.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Spinner.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9n1gemzepc", "Integrity": "VQKMLBx/3l8cEiooPVjX4wl2MjZK6j3TRcPd8aslfEY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Spinner.wasm", "FileLength": 20991, "LastWriteTime": "2025-06-24T00:41:15.3199601+00:00"}, "Hy86tNizE2eLUg7ZD7rsWewChvo7z3Hvz5VcJHHVorg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\w25zlm6clu-4z51p4oqs7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.SplitButtons.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.SplitButtons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gsp8gk5ckq", "Integrity": "kGQnVaUnPb9r3mtX5ssbfAjRg6XR54g6+xmXJp0tGIM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.SplitButtons.wasm", "FileLength": 35675, "LastWriteTime": "2025-06-24T00:41:15.3391873+00:00"}, "mFATyr4mXfptJmWcCLYQU4KFDY1aKQKqInCEEj0uWdc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zojyde6k3s-11z9idvov7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Blazor.Themes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Themes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9iukb0h1kh", "Integrity": "cRLPL2Q57th+yyw2/9PBK19vorcgIcyZj0TDF1jRp1I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Blazor.Themes.wasm", "FileLength": 1755, "LastWriteTime": "2025-06-24T00:41:15.3679736+00:00"}, "aG6DKYEMKvlS0Fnx1tpBgjCjPWBchQyFmUr5J5AQAE0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\98ec87va6q-g0hxkhx4x3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.ExcelExport.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.ExcelExport.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4f6apytcw1", "Integrity": "AJCFC2Pqvdw/GD1Gkt/os2ZJWAcIued9L6HIjHeRkp4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.ExcelExport.Net.wasm", "FileLength": 28923, "LastWriteTime": "2025-06-24T00:41:15.3919777+00:00"}, "A1FstUbnZGVS7Q+mqvCyBZcd5QMEQTpG8VVYNM6H/Eg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\md53uouqm4-4usqb2x1su.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Syncfusion.Licensing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Licensing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nymawklkr5", "Integrity": "v9LYceMEWLp1kzr+f0T0USsmoNniv7p8AGLFfGCQGFE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Syncfusion.Licensing.wasm", "FileLength": 24051, "LastWriteTime": "2025-06-24T00:41:15.4063154+00:00"}, "RE7EuoCYnlEg5OJ7PBgSGGYFDjVcr7UqyYMzUgIe8fo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\e97ynr2icq-iudrcw56e1.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cbvypw07go", "Integrity": "CMzRd155p5hsb9RsAB5w0e7pOoS0xg2okJH6wC5TZJk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 29719, "LastWriteTime": "2025-06-24T00:41:15.4200058+00:00"}, "n7zZV50dsllZAIHiZQliznvZ5TvhOnodj/az+Ulnk48=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzxdtik6zo-1hj4jqau9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9bkwerc34n", "Integrity": "KzdDoMv7JoD6QG9Efhg7xgLDqgA1Mubg8pakNIWTd0c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 131200, "LastWriteTime": "2025-06-24T00:41:15.5215475+00:00"}, "OnDIfY9MxGYFgKoZzmGLUPp3XVthILVzWukUe+LEpwY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\egcrwen6o7-rr2wy4asdd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "19rhglwuhx", "Integrity": "y1fXFjCYAnmS0Kc12Nb6r8YfXyZldWf2/OpKkKw6EBM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 166939, "LastWriteTime": "2025-06-24T00:41:15.6074675+00:00"}, "HI2/2q5yWD9wRo1FM8YZaZU2EQf7ikKvhWLKAxmXwMo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ojmli06k3u-uosabjs4t4.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhhu59cna9", "Integrity": "Dh0g3MCEcTxNMfxwZGn2FqF1s1GpGId+0biDE1Sw8vY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2852, "LastWriteTime": "2025-06-24T00:41:15.6107333+00:00"}, "tbUEJhedAnTVTZTzGjAWjW6eLKI1urfJsAhtWF1+8IU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0khw4724ee-af1hnqw24k.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "okx3d7tgl0", "Integrity": "SLpXSkFQCEnE9t6BsF+VKhnyxi083qPULeM/9Ji+Q6A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2200, "LastWriteTime": "2025-06-24T00:41:15.614451+00:00"}, "ebA0UlYZaXGeCgc/SjQZiy+sTPUTlRoJkn1yZApVMQM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\yi1kr6g9b3-n74qy90ozc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "30bot3nya8", "Integrity": "s2ahtY6Js1ojFc/LQs0mcx0ogFexM9hdz/y161ZkdOs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 8541, "LastWriteTime": "2025-06-24T00:41:15.6184948+00:00"}, "m/jf+k5cyBA/oT9mUDdwrj7rtDyDOiy5DAgVnhD7//w=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzjo06rbey-r3dkwup91o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wglxhp15vu", "Integrity": "8O9il9SXVU9Y3JRErxXP2HO7RfvF2udbHFCpbzvyHbg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2097, "LastWriteTime": "2025-06-24T00:41:15.6265692+00:00"}, "rmZBQZ5kIvTJSgM3c2BXSQkt+FsRu9dUalB+RT207p0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rmczs60qrj-j17trnwz0f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t3s60ixqlz", "Integrity": "yevotpKa4rlbryHPLCgGPBkUFEYvprY6z8pD/xDq5Ck=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2098, "LastWriteTime": "2025-06-24T00:41:15.637518+00:00"}, "K/B9cTjRx6zEEMd4Aizv3oSWCBDeopIY8HqjEM3Jyhs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8pinc6rjf7-lkbadpelqi.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9qpme5af0r", "Integrity": "P3I7FG1BUJbHvh2BX7b8/0YTpTIkbrJLdNwHTDhZY8s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 32297, "LastWriteTime": "2025-06-24T00:41:15.656093+00:00"}, "X9CXXzElY61Ay/Nh7nW921rBBzQGwpthN5mrGcYqDmM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vzisnnpkmx-4aax14grby.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z0niuoovrs", "Integrity": "E8P7Pf0zmSlS4j8kydrgnYSVRbpqn+L/gEcKgQPjMAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 95472, "LastWriteTime": "2025-06-24T00:41:15.72737+00:00"}, "W7KMIfioodHcPNw799CgaWJLOyLDFb5+ncvi1r9niLc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xde021tovc-f2o09bw51d.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eru1ovbsaf", "Integrity": "BgpD+YeFXdnEP++X5Xvqjprt2EM8VCbTn8lkOvvbA7Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14141, "LastWriteTime": "2025-06-24T00:41:15.7360742+00:00"}, "8njtl3B8aXgPt9gSsLQy29A/X7DOA+wQQnfxKUDdnkU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7a5kyzpvkv-exlzuq35jp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6hy5rtbzb5", "Integrity": "yjw08JXnHUBEc52XbpXu+/lyNXBOE4Tgrgdq38DoBCQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16046, "LastWriteTime": "2025-06-24T00:41:15.410971+00:00"}, "mcri73tVilaAb8pdT23Lo2H4K3AvSAe88hxoNL2GVlI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jimaxq9mi6-y7qnt2sca0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "li1iq7ygyg", "Integrity": "0zEo5lrQWh6+txzgwvVdTPiPXiZqR79gmRerXXVMchM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 38924, "LastWriteTime": "2025-06-24T00:41:15.4262767+00:00"}, "XU11UlxE3K2NuCb/+mSqLNfSsndz0xB37f4Yvpx4YsA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n257kqytbc-v5hyanf0mc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "niyp9u8mcl", "Integrity": "/1sa3aIsEe1TEKgmj+tIgYOKwY1bq3R7NPLEh9XefPc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 35253, "LastWriteTime": "2025-06-24T00:41:15.4451385+00:00"}, "ywfSEdPTWZ/Z09L6tPJUIPumMhaj3jEOwPJGOpZmXGs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3paocsqo5e-2a2uywtzte.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fwnhdpwzz1", "Integrity": "wXsA0FaKmHTFMLHbczraksN2of0eY54lAfqJVIx9P2o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2572, "LastWriteTime": "2025-06-24T00:41:15.4676566+00:00"}, "rrlRDZsDWvDsqbzpkLwPpatKJ6WKGRIkiVxtgSeOXH0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cap10iz261-hsnsliye9b.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0ie2psq76", "Integrity": "QCcx0t5WO62E20gJZxrNEppbnOIEnPtqR1hZ5N68wiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6783, "LastWriteTime": "2025-06-24T00:41:14.9509577+00:00"}, "QOTr+CJwSFlgDCfJYWMFv78IGBLnXiQsvyfiKTNX/5A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9ssd939fn0-m498x7yd6j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "837<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "ZH6tDts3+2tnHmoD4S0vQ2l7jiJOy7RbAs2vWS3eiD8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13073, "LastWriteTime": "2025-06-24T00:41:14.977585+00:00"}, "DpYcYm/g08gV1/kynChgEfNNwiev9pyUcs7rfw+a0F8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\byt3nyxy2q-69bwpm0gd5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tq490w1kqh", "Integrity": "RRCVYG6YWtl4WNHmCVJKob1znUxxZbfnqqKlaviV5Rs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 118082, "LastWriteTime": "2025-06-24T00:41:15.0175012+00:00"}, "VDT2iEJ7b4fb/PCkuoVbs6l7Vn09zsmHvPiK3vyj5OM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\c39t6asywb-yq2dti153n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pc57z9h8i5", "Integrity": "JxKSollRWK6jo5rUQi7mmn7LCQ96uSQQXWiPSxaSZyw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2554, "LastWriteTime": "2025-06-24T00:41:15.0205022+00:00"}, "hguYVH5Glgal/44acpYKi9fwy3xfZw7ZvWU22JDnfcU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hiyuk85oo1-pteo5cxcfm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9b1mghd7aq", "Integrity": "JTS4wqD8bFYTW1Fo9vgCykNZWTY3CRpmz/FQOH8k56I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3101, "LastWriteTime": "2025-06-24T00:41:15.0370588+00:00"}, "UmD2uUjYnjsgyl9os21mykwvRpD5RkWmyYIAmbCZmdE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9316qi1ji-zsobxitq9m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d6ofa9jyov", "Integrity": "Jeax3i9g1YYJWZVGLjEmM8xaFqAkqCqsGfBR6Jm45go=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19796, "LastWriteTime": "2025-06-24T00:41:15.0662432+00:00"}, "pkcAKXpZW5jN+TOqZhBqYtpq8JBzEzU5I0Q9b0QprIk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1umd06yrfb-ayoozo91sk.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1g2486v15h", "Integrity": "nwo2dAyBjQryhcbmVd3111QTbC9rpGkycx7WQ4W43ZY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4537, "LastWriteTime": "2025-06-24T00:41:15.0754558+00:00"}, "KkfBLAM1DqCT5/xNyBV8OlidmkUxZSyf5JHfufJsWd4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bz3avbczq2-ptdxkmw326.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kwtgxwtrsw", "Integrity": "nDMAP5IcmxPRQpsAAzy/gzBXjAXYGeg7nPilM8qpsc8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 376217, "LastWriteTime": "2025-06-24T00:41:15.2253822+00:00"}, "gTJEtMwX6qULN+e+9zv6UfP3LVUi3uYJ9KYSeOolTHg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4wpiy2o4d3-ezdljfc37x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1kbrxf305t", "Integrity": "QQA3GmNzeH8/4+MCBZsdYvQPNEx6TpX7uWm32xE/0Qs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2060, "LastWriteTime": "2025-06-24T00:41:15.2409825+00:00"}, "ssqCQre3NCLiXoFQ4PDhx3HrttGnHbt9AU2SRKnF/XM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9wtv5r574i-s4jbqeso3o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3bvzjuovbv", "Integrity": "P/R5dma6QtkpD2X1HHRTI7GIQ2YqYGADg5QWyAlI0Po=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 4990, "LastWriteTime": "2025-06-24T00:41:15.2569481+00:00"}, "z8WECxUjbcuBRGav3k8WTXox7IU6gRmCXljrhwFpwuA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\m3qtwn3ns2-rl77dmc4g8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uyur41mm43", "Integrity": "ht3ZdJI0WNtVtJcrhwwLiyOMTQ+OpzS69T1k0fmrc/E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2386, "LastWriteTime": "2025-06-24T00:41:15.2822018+00:00"}, "ypmYM1y1B3evoWiVoFxz/ZodY/NUeoy1A20mg1TMMp0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xnalvtn1mb-d6r1qmhtiw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvzr24ljx7", "Integrity": "Z9CpJlO4WNouGPgRrPDcjuDoHzfGHW/bserGn1p8g1k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2268, "LastWriteTime": "2025-06-24T00:41:15.2907635+00:00"}, "WLn93TsOFzmq/BBf/Ot7N+sQpA0nIr0OaCI9WYPWTUc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fnpgxujk9y-imdaogz3ij.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcwtmo1qly", "Integrity": "BfHo7tj/hczINWO90hVez350NzD6tdEZi6SlqcGFjrI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 65136, "LastWriteTime": "2025-06-24T00:41:15.3199601+00:00"}, "MhHUUqmyaAq/eSkuwMS0bE1Y1LQ9IfuQ8oRijeoOiyk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sxppidzjgj-gqyf43a3pe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xg2f6930e", "Integrity": "JVlsgLgvw81DErZ//g8X8LzfJ00vxra/ga7xkeXaoYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 4541, "LastWriteTime": "2025-06-24T00:41:15.3248054+00:00"}, "ORMZEZJT0nKTf9Npxec163zfxueJavLdCBJbBtoRd9U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1lzqee7hvd-2qdjhg82pw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "23acjr4ix4", "Integrity": "Dr0M5EqmQmZUYgBlWroUISYdU2GJ8StHTbVINDBa+wI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 15502, "LastWriteTime": "2025-06-24T00:41:15.3442319+00:00"}, "2GFu3w/iqpjQxR0Q8C1O966Zi+nutxMZTQ1eLRH7ZNE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gdmgran5y7-lx3knuy0pm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f5s2sv31se", "Integrity": "A+OYlrweyDYZMJtkPvPPwP8ovZi4Vnu7KRsklvBzE/Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7337, "LastWriteTime": "2025-06-24T00:41:15.3582857+00:00"}, "GRvAGIDcPiUwLPrVES+61rEIMe+4lEyncLz6dLp51dE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wdlm54q4fw-p6vx4lif6u.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "68rv787b56", "Integrity": "aq2dt+iaq7WiLm/s8AJSq5QXr2/yJGGxRlNGEE4/DTw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9389, "LastWriteTime": "2025-06-24T00:41:15.37622+00:00"}, "WWjgWlV2Plmsmvw/yq6OqUWjooUZefRj2QGzACaUwWg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\67lx4f78cj-4s4qj16jn0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ppekvym6e6", "Integrity": "g1Gm59Wb7OPwIQWb1m47EmGE4BTjE9iAGpVQHhl4oAs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2175, "LastWriteTime": "2025-06-24T00:41:15.3960181+00:00"}, "bxQtdc4BTduZ7nos5NsmJhPt4zawDi8XWLTgLZZTUGE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\77au2vjfld-5howj2x4lt.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wvxse30j4g", "Integrity": "dOsRzhaWV96BCY1Ycm/u3ezIaiKAEip0IFSzwd7BEhg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 19528, "LastWriteTime": "2025-06-24T00:41:15.4373082+00:00"}, "VWOZ7kC/q50Yeb2rGu2gPmSFyI9ejv9Qn3LI5mlbi9A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\binqqlz1sy-jg8c5ekqx6.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s6x2cf719o", "Integrity": "fBPaTZHEVK3+migohaE/Yy7OInHRLWHB65yYdDGyU8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2501, "LastWriteTime": "2025-06-24T00:41:15.4621274+00:00"}, "PhCWSGI+2lY6hhV10eY5vaZoM7OYquKTUy4wcMfbMJA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\eh357harcp-tvnls6hcxf.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rx3m03fkr3", "Integrity": "b5nlulk0mFhxWADtDot28bUdoRYNnpcyO5azIJrwZU4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 23209, "LastWriteTime": "2025-06-24T00:41:15.4859245+00:00"}, "0N43u1ipnejVQ/ciOzP1XyZZNvcVcdfGFfo6Ygu+mQY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9yxv7vuua-2ad51ju9aa.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "509vpylrg1", "Integrity": "VdBBtw0OhR0il0Y7j/j74swMAJJeki+tg0z1+PPkO5I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3836, "LastWriteTime": "2025-06-24T00:41:15.5076677+00:00"}, "OmKx/DsadrGaYi0n4Z1xsHF5u7qPBkLUxxmR5uApfXk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\leomrziixf-n0jfd8l0iz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e8pz0poje4", "Integrity": "glFWtRMdwoHgsHoIBoafQ9XMwA2fuglIBmfgBwp/YiU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2439, "LastWriteTime": "2025-06-24T00:41:15.5240625+00:00"}, "Ojb4ybqPCAfUX5VpgH4anXq8PMSczaNRAZknPVTPHTU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\c4ro1hju42-l2i13om05z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "llifbqlkm7", "Integrity": "FxH7+EkE20SRBfrCFEYpuNyzYsGghnW5dtgaWF6zV1I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35455, "LastWriteTime": "2025-06-24T00:41:15.5591524+00:00"}, "bKogHSNeMwXxNjg/qy9WAFwLiSyjzdQtFW+jadbKw98=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\57agg9smmy-k6z943nrpy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wwmvnmo5v8", "Integrity": "wNS9nQ6taI4WIOuWvLhHiNUbHnQHmIghxoU8nE3oEJY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 9800, "LastWriteTime": "2025-06-24T00:41:15.5892957+00:00"}, "RYMJ9MxHC8ZDVdXBX94DhqnP2OtHNILe1FXrysX/Ebs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zr3rxoo55n-g2zbr73a2g.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n0siig7ezj", "Integrity": "qR+bdN+x8r/qd5YeDAZ+iMiMEFXx4Tv3NyBVNkAQYLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2286, "LastWriteTime": "2025-06-24T00:41:15.5940978+00:00"}, "DEldewYsmUEJxwgVj2fmv6LxsE6YNr+bnWLzxhS/cEM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z98cfng326-m90ww2zviv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rzfub7enaa", "Integrity": "d7OmwmD+uY4Y5zF+IHR/ufDjDASRFWpMQdWoQNnL9XY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2172, "LastWriteTime": "2025-06-24T00:41:15.5985401+00:00"}, "EcULPR5+PCyEZdlWkyqKENU5NHutXs4mWQqYbfX//Rs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\giup3hxlyd-8bmqvi5to8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cokylgo4j", "Integrity": "NI6MBIZGKXJj72bc+IDjKDG1nTaRS62UMU7HhUe9B8k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2253, "LastWriteTime": "2025-06-24T00:41:14.9494864+00:00"}, "Xkl+8jcIDgagYcFjVJ2cq3mhCri20DpNjeREHPm1pYY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\smvg5f5rpt-xpehz1u5xg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lrfpf212tn", "Integrity": "75zD1Q2ROvS4abvFcfUGIvIRXrYph4uh95ApImW/haU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 6272, "LastWriteTime": "2025-06-24T00:41:14.9596333+00:00"}, "BS8h80bqhhN6h5okst2ZtwfOGeKZLPlsNL6jj7KrCs0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3zji9tv399-ofyky8esh0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ar5txxss3p", "Integrity": "Zmd9bHK3cFt6k9sNgJuREnTS4aEPzxzeMV3m8wfc3tc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1990, "LastWriteTime": "2025-06-24T00:41:14.9815917+00:00"}, "odm4qk1d8Gvh8wgF+1C3gadQYTvC0EdqdUh3/u8i7nQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rgl88w1it9-kqgep265ab.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xeynh5pdza", "Integrity": "Bz0JVrr6Hx3RVKbXH1DvlPSwZ8U6urLUZ7YsDLMN1gk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12136, "LastWriteTime": "2025-06-24T00:41:14.9941779+00:00"}, "OSzpwEcmBdSV50JTGNhNpFIuGhZyppj+mptHrBNDxnc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lvyrlugiaz-whf02me0s4.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mj82s2fn4h", "Integrity": "witmYMa9fX1LWLEnnSkpxc6qMBPvH9UJ56UtpV6UDiw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 42208, "LastWriteTime": "2025-06-24T00:41:15.015987+00:00"}, "h4Jae7A4nrJFWjW2Y/EVwjdMMdjRJbMJ68IK6rDttJo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\y3zikztta2-1trkjj9toj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7zerdc7jwm", "Integrity": "bW4/Q896W7rggpSIkcnzzXK8sF2zS+Nh3Ye+FkOe+Hg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 7897, "LastWriteTime": "2025-06-24T00:41:15.0215021+00:00"}, "8VLpvOcZz329y/M813y1PF5xL76Z/AjdPNPe5nI+IOM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\l7ml98ds2n-pak789pfhc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qkpig6b8z0", "Integrity": "+ukRzCWyHHI2UMNy8eSTc0+qoj4k6ks7GLIi1AxlSxE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 5594, "LastWriteTime": "2025-06-24T00:41:15.0267504+00:00"}, "PveHNsfVCuA5CW3nKrckEYyfLkppUNHkeee9tHL6nRU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\26zimhthez-tx889edwyf.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iyfkzprjym", "Integrity": "NvtWlUdNbuqFIqDe7wn6eig1pzv3ubGVrST6af8kdLA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2178, "LastWriteTime": "2025-06-24T00:41:15.0319126+00:00"}, "KqQ2JSAPLvFHbv7+oWDUms64/9kVqjzB9XlTKNNyI3A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cumen4e1px-2y53qsfelr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gyhsstey2d", "Integrity": "GCb8bFKPLaznzN82U9esvWP6yOzad8UP7FjWlXgi/Bk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8195, "LastWriteTime": "2025-06-24T00:41:15.0470293+00:00"}, "I3k7NZ3C02pjRWrWhIrLTtu6hjrMEpxwgDZEsFI28go=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ohnw08v0s7-5i7u2kz8gq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fw5w18ce27", "Integrity": "IRnwVEiXfK+xZ68TNQVJOP2qXguSZXVzrwQxtqPX2aI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2292, "LastWriteTime": "2025-06-24T00:41:15.0534821+00:00"}, "ER7ccLTGA6ShE8wsOGHAk+qkESkc8ygqSIgTuskmCkg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gmtjrsjpjr-bg69h2q1tx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16zmobxf9k", "Integrity": "iU/cpgWhMyQffbAdpduvPLrhY6Kj07nzKAhgBY+dM8I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 8776, "LastWriteTime": "2025-06-24T00:41:15.0597163+00:00"}, "3Pe7ETD5eqsia+C8DxN9WTJHgTP+b/FF6TjiFMI2mdI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\yqou8ak97v-s21p6d0e1y.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3mjomyzpge", "Integrity": "tXgFpGSAfgq1a9xA2NFRuMqz30+bxV4dmwS1Hrdr1Sw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16342, "LastWriteTime": "2025-06-24T00:41:15.0799711+00:00"}, "8XHacn5O5VIRDaOV0WN9LsQMT6VbwbKWmAgeiBUeMRo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\y9x9709oxs-6jayxq6dso.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i58mqzxche", "Integrity": "xqjDP+M7TxdNllx85RenwK2rx7+V/2slBz8qQGDm1oI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5304, "LastWriteTime": "2025-06-24T00:41:15.0885838+00:00"}, "r1qdxw/Moh3+ZTLQn2YbZMT1oKjqZYtZoC3n15BQl4w=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\2yj9ptca6s-78mrke9rwp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nn9ju4tano", "Integrity": "XYq8n+UkgUOCvfxP8h2dW4858UaTyqsM10uBPtr8PxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 10481, "LastWriteTime": "2025-06-24T00:41:15.0973234+00:00"}, "0uN1O1dMf9SBcnxvYsJ6KBs7rZreLHzuMenJenDU/DI=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5oprngc3na-rqikjp4z06.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ag89dak36", "Integrity": "l17a+x9KegTblfc489R0R9q9yjDIBn5fG8op2SGjJmA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2199, "LastWriteTime": "2025-06-24T00:41:15.1175774+00:00"}, "kInm19DfBWd5vSh7hojPctrX0oSDM33BgfzL9tJ1Cz4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9rp1nsr78a-lyr9te5dpd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ge1qryld7y", "Integrity": "mNy0m8/GIFsF687byrnByu5FydFGY1KQdU9W8nj7CFg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2254, "LastWriteTime": "2025-06-24T00:41:15.1622001+00:00"}, "JRft0omf0vjTr6hefc5FRcFIa9SG/n6pS1Aek8GE5kU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\wqbo3t8krt-v19ocl650f.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2l1l0tmhvx", "Integrity": "Ri8EMIJeDG0rCy+8iYC88ausg6ADxlqjh7cIHeEYXfw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 213340, "LastWriteTime": "2025-06-24T00:41:15.2440387+00:00"}, "RGKCPHlTznDOjRwiRoFAdl619P2BcCqjMSlP7LdyUqs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\32t05zddb7-7jakql04zz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "484huxgmuc", "Integrity": "pLEkmUQ+d4hzc1xvlvmUVKxzzLq/ZGEqzHWRbEzoqv0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 86883, "LastWriteTime": "2025-06-24T00:41:15.2877639+00:00"}, "zH9xFoheu+cR7cLW2YVDKlyk7FwrZ9v1TeJf/+fcMCs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\o4a5y23fdq-yskp2l2j28.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "38gm5sg4jg", "Integrity": "oxAXb9LEPCVRpK6lfJMaCLud8cT9BwAyeIR11RgTH+I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 19930, "LastWriteTime": "2025-06-24T00:41:15.3134403+00:00"}, "WX59p6mhnvrYLednG8RG9HR5riStsXaegZqd2rhvkjM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\6a5ovfauzx-dr9ustd9mn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q6e2yjy63c", "Integrity": "rh0k7q0Rj7nJIy9awxK3sNC8ZxQmsbkwNyFdZJvwRqM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 50155, "LastWriteTime": "2025-06-24T00:41:15.3381871+00:00"}, "yRr7VHjjnkfxhrw3q769Z8siyA68qJku8WEY0taw81A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\raqtb0g8tk-r5wuytek4x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yw8digvpjd", "Integrity": "YjA/Rkd9FOdVKIeRjWV+/jFIwK8Jn+80stxpPPbLNKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 20344, "LastWriteTime": "2025-06-24T00:41:15.3644571+00:00"}, "4eQsy+zLddDWSErVpGBnwXmZAPXhsY+UeK51++lyx10=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\k90ia90a0l-r4fmndj4lr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pzv3gw1kc9", "Integrity": "S5uyDL49nDbBrz5McBV7euPXQ+lMDVjZXoZqpIw7q5g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19101, "LastWriteTime": "2025-06-24T00:41:15.3749953+00:00"}, "6Ej0iKLzhGWZk0pbgy3Fz5BskeQ0abPpl+7Fjbzt6sw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\1cs6asnscm-dfc7iaw959.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77ioaax9bw", "Integrity": "3Yu3Wa4h3iHEArbBOf7yb+QtRKmJEgAHS2vrZ/8uUK0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 110803, "LastWriteTime": "2025-06-24T00:41:15.4378166+00:00"}, "2EpWqDjVICU8BuhvhgGtHFP4rRvSViteJ4hYAbPi/O4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\om386z7hvl-ypu8t1a3th.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g53eoah1ck", "Integrity": "jCiqprAiO3CT8Vhgk//euU3zKduq8BvF1YsC1FAdgJ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 15450, "LastWriteTime": "2025-06-24T00:41:15.4621274+00:00"}, "i1H+f8mPArFOpg56O3wYq2w/qnOvb6z8kUMG9zFzdwM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\01kj3kzu05-avt2ugss8m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qon64aop3f", "Integrity": "amlyJTqDmydz+OtgLjiXqZby3pdbHW+DGGA1Fi2te5E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 41421, "LastWriteTime": "2025-06-24T00:41:15.492763+00:00"}, "DLckMyJW7GQ5g9RhJXRaJ+JXRu6vZxE0h39hI6NtLL4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4ziudi5er1-kqt05iowt5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "86wjlbs9zy", "Integrity": "oGp+2AS6Y5IPJZiieXlusOVrWezQMf+BeNYJtcIo5+A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5340, "LastWriteTime": "2025-06-24T00:41:15.542345+00:00"}, "g8U7buopYy2/IE9+7YRi9DHTgexo4H7q6ZMDfT6JNP4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gqrdkmv4gr-ac0n5txzyd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uvuyz0cttl", "Integrity": "JiyT/6xYYmK0R8a5TbJh0Mws8KUfEYg/jc1xqE0dlxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 12139, "LastWriteTime": "2025-06-24T00:41:15.5662098+00:00"}, "S+ZO7H3VVwjsikBlMUDRvkLUlGzi0X47gIYmUOo3VuM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\778te4j2e9-rrwevpsa36.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7149sngwk3", "Integrity": "q9ThFzz9q2gD0EwjqEHL5Fmlgen7IfOeVJAau8oBer8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 6939, "LastWriteTime": "2025-06-24T00:41:14.9519588+00:00"}, "iXNlBYyxcu+m+foDYb+JrHl9b3llKBItoCLPYqrrwFc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\axunml5apk-9jxobawljc.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xn6u2l8nng", "Integrity": "w4xwZbTj0hb1Rhh27gZ4B8fZ1+8yjJI/X9ZBgLiFkZM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 44984, "LastWriteTime": "2025-06-24T00:41:14.963399+00:00"}, "qnWFHzXzypGb6uqfIL1V5zKxd066ZZbpWhwKl4nmC+c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\swgmwtsne4-nn6t1rxfu5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mem9qevjnx", "Integrity": "UiSda/h0xpyaOY9R/aCl8liHhr0KKwdVbQwt/YwKPaI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 10555, "LastWriteTime": "2025-06-24T00:41:14.9835925+00:00"}, "v7N4eRMasxunARofFbEcGiY6RjLSkgJF3LUsNgcocCU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8bprzflbij-psjawytmz8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7sy3fabvn", "Integrity": "pl0sR89QMESvxhxOIVL7R4UjWry/kgPgDunOUEqH7zQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 18143, "LastWriteTime": "2025-06-24T00:41:14.995184+00:00"}, "2lLrVZljbdLq2+d4fGroBuGoAv4/exDt0Y40pz3Klns=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5peanjzhpf-49lyg32can.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7v60bzgxd9", "Integrity": "PUaJImE5ckbLfJYsXFj5cDkMcvJQ9DNVO7MybEkZGvc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 31586, "LastWriteTime": "2025-06-24T00:41:15.011955+00:00"}, "Jk0acYXGCqHSHHSautXQ2fsJYyK3HJGOmimIb8haKLw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z537psxkb5-rtqo41ax01.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nr6sdeecsv", "Integrity": "veljWMXJcCH7rt7VSDdL+XQqBjvnCbqeh/AyayRKz3g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 7242, "LastWriteTime": "2025-06-24T00:41:15.015987+00:00"}, "HAgRv7zbU7BUxYJnGBiPi30nzeq/NT/O6Obsique3Sw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0prwepgyfd-300sh8z8ui.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ee6hj5lchi", "Integrity": "prE4z0kV0rwaqLHEDAOIDQ21xglM0hFsisHTidgW9xU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 22456, "LastWriteTime": "2025-06-24T00:41:15.0307518+00:00"}, "7QlRx7XgD4q1lwKFsiiuH/LfBjtyBYn0v7j+w9bm8g8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\12jxpcjsau-o9b03xt26m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2tg12glemn", "Integrity": "qqIbhoUpTxWiHD2FYyneT+mabxlksEDlNNRVttluOs8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 13342, "LastWriteTime": "2025-06-24T00:41:15.0434958+00:00"}, "rgrdfkRQ7MVTdTu1tWcpVpijPNL7G4Nb+xvINBQ5s7c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\876btrwotz-i1l90qe767.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "db52nw0so2", "Integrity": "aX6Czvy2bWobhB9gZsBcEZkxqQw8sB8CO52znepjKi8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10638, "LastWriteTime": "2025-06-24T00:41:15.0607252+00:00"}, "iodmooGmhjpTjBnJqowXEn3AqzShQHJGCej45YJqdBw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\rb0r3wv541-ckahycs9oy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zojeka79o6", "Integrity": "zfgwasG5Wqe80sBTey2LPKjKwtA/P67J7bG4RyDW1fY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5611, "LastWriteTime": "2025-06-24T00:41:15.0769632+00:00"}, "IRsXdJaFUIIb9acFA1ePkNZRvbqczfWkh8lLIQTLeWE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n9ufvg6zor-12eg0vhcpm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y830a1zy97", "Integrity": "cq6zyP5y2mpG2a+gaK4TPe2NohRa+61dX1XphyXo74w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 16048, "LastWriteTime": "2025-06-24T00:41:15.0849668+00:00"}, "vadKruhwbY0IYAeWVm46IVbOgedhY9atmKtnQgmnDIo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\s007umfqyr-tl58hhwwm9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2ebllpfgpt", "Integrity": "Fqo5F5okh6+1LaodfcG32bl5Kc/Q/0lMwgjk8Hj5cxo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 31154, "LastWriteTime": "2025-06-24T00:41:15.0993318+00:00"}, "erXtgJPGJn5L8BwUqXn45dn+w2AjxwDluJEcg/R6up8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\momr4swqrq-s42jx0duup.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "peimhjwjlv", "Integrity": "saOft7q0HakPdXdg8F0yKzrLc5L47V5VaAyINLdnWhI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2736, "LastWriteTime": "2025-06-24T00:41:15.1076039+00:00"}, "3zoyrvmPDyacC5QTHfuqlHaFIOHaxDun0NAZ2oFZIOs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\zlfrj5nqwi-a1ph0cw4zn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ad2x663mk", "Integrity": "QpcVARPeINasJbo3DgR/iduaglKuEALRJ8r8Bo+q+n8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2259, "LastWriteTime": "2025-06-24T00:41:15.1224947+00:00"}, "YnAfzD+Fi74+H8jQE2BleLKOJ+qwgL1MLURmmv3IQW0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0lktitq3ad-s0hlxxu97u.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4ka65cgi8", "Integrity": "ZD4xPO4qztMhOo8cuRrA35wsEViKaxMJf/6/FyU2Lbk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2023, "LastWriteTime": "2025-06-24T00:41:15.1581907+00:00"}, "30VuK3lzS2/2PlzpGs05G8w131EAn81TM3ar1E0dQPM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hmt6yadul1-cfb8wdf8bw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhbrd5g2p5", "Integrity": "9lPQEYhKUE0lwHNsgmMclDwfvwso1aKf6cS3bMGw38A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 12607, "LastWriteTime": "2025-06-24T00:41:15.1708132+00:00"}, "KBq5evUzAj/KV2rUiZDDHtaNj98R4x5Qu3LfSoZB46E=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tqgnr7mkgy-pf4dzgp6et.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jfrip8650v", "Integrity": "oStP6XLcdz/5EfQPc9LYBRGrgqjm5i6VYyEDljdc9Lg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 301144, "LastWriteTime": "2025-06-24T00:41:15.2989721+00:00"}, "Q8kF5E27NAL2xylugQ/f/MN36f0mtXisrFvBB/EYCnA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qm6lsqstgu-3mrenqpwlz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8i35yc22yc", "Integrity": "AK2YY7vBkmfd61XozZugjgFjiiqQ9+lFTQOfju1uJpU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 41146, "LastWriteTime": "2025-06-24T00:41:15.3219613+00:00"}, "CK5UxQ8R34SgXBsajOnSvNy3/5auQwmczzy7VX4OXvo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\kt41y9oyfq-nudbvebtzm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ubgu6qjwu", "Integrity": "EUqF8NOJ+jTFMJ9zNxnXyY4Z0RushGQ4xMWXukydvow=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 58586, "LastWriteTime": "2025-06-24T00:41:15.3505681+00:00"}, "V9jxKdGYG9nz3lNRHqrhJjMklz76M63vOM+MQXuDkxM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\07zc013blp-tbmcprtln3.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ipjnodiyxp", "Integrity": "ejAVdjqjPJSREmeWoKoFWCthv0S1pfawXAGQ0PCtL3U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1062979, "LastWriteTime": "2025-06-24T00:41:15.8711105+00:00"}, "5DC3PZxkaYfzw18xh8EJXx0CR27O4HylpNrPMA9SUAs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tesx50ibi3-824m7y8iv9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1izng5m8z", "Integrity": "6DJGUFZR8n2Mves782YEbmp6DAE78QTi5yTiiDmEfIE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 12103, "LastWriteTime": "2025-06-24T00:41:15.8764755+00:00"}, "0uwH7Si3kzfz8EGnuuLuetPgjmWqxAMsUU9XYdY7/9w=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0t5acjtnsc-5e6t7jey7n.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oglg669tnk", "Integrity": "WME+orWFAK6pzqfLprop1rImI5xc8lTN10592bCWA1Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2270, "LastWriteTime": "2025-06-24T00:41:15.8820873+00:00"}, "ofS0WdWyWecCu70b635LWtHzsA7IBxwF8ZOhL6YFCLY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4b9lgapm59-zyak9ezx99.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdk7a3e3e4", "Integrity": "5zihI5BBHgZ6C9wCUHWqtcYbht5v6PGpTIdMtM5GGe4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2229, "LastWriteTime": "2025-06-24T00:41:15.8854266+00:00"}, "pvDLqMxpuqHST5OKQ+JzSgGP/QKpA2IQTvZgZgL+75M=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lawfelcw3p-vqk9iwwdi8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jwgopog89g", "Integrity": "bXtSY/cj+SHug4/IkWHIAmGKk4/0fWqz+o1KvV5Gndg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 26864, "LastWriteTime": "2025-06-24T00:41:15.8954604+00:00"}, "ZOueVk480s7SsSsD2QcGpXSIOJwqEMlHTOAsoTvSOas=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lc1a89fsfa-7whwneqdab.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bqh2hkphpo", "Integrity": "vegRt2qgkUyawGZjWk9QvdAM8Ql6QUV8uDOzTAjXYb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2140, "LastWriteTime": "2025-06-24T00:41:15.8984704+00:00"}, "B2Tdk/LihYsO+HLHvIg/JaXZj2l95JMu7EuHhs9+8uY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ksef9slfq8-1fm33xfb4x.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77vv86mi5w", "Integrity": "s2+E2apeU/8UtRBgyWXPjk6JSsoklkkKiblSMsIIqx0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 180470, "LastWriteTime": "2025-06-24T00:41:15.9517561+00:00"}, "VjWau2rOritQzYgYm5/VmedhN2bbo2hdxEznrnXGf5I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dlqy23dccu-kcvkdr4alb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "55m1xc85z7", "Integrity": "WVd92+USYOsvgF1RgSbHvExhgdzRFshDmc1F6gdFxBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2354, "LastWriteTime": "2025-06-24T00:41:15.9558344+00:00"}, "5HDcz/NQ7D2QiH4or7IDBdvj4nUF/TYMtRggdpZFhD4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\58erqdvtin-gpga8vsymd.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmxrtxfzjp", "Integrity": "AvR1RokpATMyFYF92UFBck8yadV27MEZe1mQw5y+IPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5661, "LastWriteTime": "2025-06-24T00:41:14.9527267+00:00"}, "Z44FvjrzNwyqVKZGZKTmCMlUugu9GsUf0PaF+fg1GEs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\odya9nxts3-abr508h4gv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bluxglujkc", "Integrity": "4cJH9oWzScvfq5sPDwLqijLKv4Kbn+eFNlihP39BDIE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2450, "LastWriteTime": "2025-06-24T00:41:14.977585+00:00"}, "VQvyWvPQTf8tDg2efb2krjrACrgp2hKSr9X4p61x1m8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lhpdkrosjy-fjhf6hjcqm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e45jqid7h2", "Integrity": "Uu3HYh+pHjWKEq6sJhvrE4tH03XtN4W8kxSrUBQapyQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2110, "LastWriteTime": "2025-06-24T00:41:14.9805927+00:00"}, "vR4OMKIbjPaQW4Cjqwg0yU2DlAVHUco7uMGymL79gKo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bqol8tplix-1zqopzubo2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uwb3h5iqqo", "Integrity": "8htJbKBtKuj6YHTaWR0isn5Y9fRY17VTyW5F5u+k0Q8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2227, "LastWriteTime": "2025-06-24T00:41:14.9861066+00:00"}, "1zbZ4p8WTEiKEygaDzSS2sD1y9QShdVbDeP1teYO1b4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iwjvmfxqu3-nilb9xe1yl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvqbudk76z", "Integrity": "x44Wd45OmcsG3qNwVpj6F+7mk93lzssVclI1rAt7B+0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7620, "LastWriteTime": "2025-06-24T00:41:14.9901061+00:00"}, "SDmFoDnKDFMZNLAC4fmXTfV30V9XyDey7qPYuA6gnz4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\9m1cnur73y-xpg3jz0dn2.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "58jyewk2fk", "Integrity": "K1wD4MTMOw+faqanguKuQgdh2La9vlHySq9iZs2pqsA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2118, "LastWriteTime": "2025-06-24T00:41:15.008956+00:00"}, "GiQObgNhS4wt7qoJoLTr7SmnE2wIn6E9X84Nlw4LowA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fw6jev8eke-83ia4yffoo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nk5xnik6d8", "Integrity": "d1mEv0Rt6WBdfpQL4rwhIoaDS6qWWmej9hJxIqnLTh0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3034, "LastWriteTime": "2025-06-24T00:41:15.0215021+00:00"}, "ipz8M7nCswT7FL1WA4DAIqy5wNDWMax4wdNInfr1gD8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xklfolbr8t-nbdqcvny6q.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "psyrbwnqwz", "Integrity": "F7z0D/oOHYmW0zj+p/WLcJjjicVz6wdx2buDaInw5W8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2964, "LastWriteTime": "2025-06-24T00:41:15.0360499+00:00"}, "H0Jg/v7cveR/JqB+Imcz1p3AGIfC2+h7AvRbO+t16qQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\nmba0r7o2l-apxeirum8l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0lwizuzr7t", "Integrity": "dKDYYhuZlnAhq5RZjmRjP/EsRLJWttjadZlXsM+HIEk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2192, "LastWriteTime": "2025-06-24T00:41:15.0419864+00:00"}, "kZP/NibBY459sPh7jqSsr3mlIy5Vy/vMW5nC4bgvwWg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\vb7m02hyiv-bhp1yeu8fb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "exl3s6xjb9", "Integrity": "wtX+hXuUbcxGbmCDJRNlpESnKEId7ErP/IOBoEvCJ7A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 34087, "LastWriteTime": "2025-06-24T00:41:15.0672447+00:00"}, "0WaIecFmXzXUI19TmQK5nUX2iC5lKTgwFWy4978F370=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\5ctps1xp05-mvr25z4kt0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "agwxpzbopm", "Integrity": "dghGeAbBTXa1lrqBOZ7liVUBAR7r8HnYYw+2JkCUW/8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2147, "LastWriteTime": "2025-06-24T00:41:15.0789709+00:00"}, "kD93VXZ4vYAmqDBk0KQ1F4DRZshARpy5TyrP+NOv+y4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\royp2e729g-qk5bzw2lz9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw02lop9x2", "Integrity": "7CljJtoJ76Wy2TbElyvQjYTmJhCAfK9b6HGkQwgew8o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 20369, "LastWriteTime": "2025-06-24T00:41:15.0959258+00:00"}, "kHRb/wrVYrRo6kvVtkcxx/uV7Os75AKhrY+f/FDVQyg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pexc7mp10a-luafopex6b.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pu1yxqgj95", "Integrity": "H5mIEJSMzvO1gBcLb9KNWSHdMWfZ7p6gbLV+wuXVVyA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2638, "LastWriteTime": "2025-06-24T00:41:15.1076039+00:00"}, "EK/6dskL7kSiqNifuGfh08DFihJdvp/jD+iTUVWpkHo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7gj2dpgwqs-j22agkcn9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2rvq94iv4", "Integrity": "0XgMu/eU9YU73vu82zjWMvSSvqS5VfvoG50jYZHW31I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2310, "LastWriteTime": "2025-06-24T00:41:15.1235013+00:00"}, "lIOVxpagencMANzuDgg/5TIzHO3Tops2EEjGV4r2bL8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\kbgplu5rwn-2k5z9g2rmq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tdrmp9329y", "Integrity": "354Er12tThW7oA0QVo5k3B3EcPpXERhtb8dlAPCtH0g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 45362, "LastWriteTime": "2025-06-24T00:41:15.1622001+00:00"}, "2nDtXiv7iVSrT4nq1vHyhKBswqULGkMilSeICRjxsgE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\j28a3ppivi-7e6tdrrvk0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k0ghmblhyu", "Integrity": "rCKyYStmdYCL3Rb5QMndexQo+r4CtD7OkZKREMn1LCc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 23893, "LastWriteTime": "2025-06-24T00:41:15.1793682+00:00"}, "KqHepDKNMHYJQch5NuCEBo5bvv4XR7eYRGvbif4/2uw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jyffsad4v5-ophjv8dnro.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgipc54347", "Integrity": "RBa+y94ve8y1iS8PDXaSh/V96m4T0/iqzcyeL7E12w4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2252, "LastWriteTime": "2025-06-24T00:41:15.2060046+00:00"}, "fvz74h0faQsXm7p3dXwUbqIk8r7KJsYS3jVRIGcv6/c=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7xl7opbj6m-f6qrsh5ggz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1qcl35ck5", "Integrity": "5n/RRLbNsyNW3ibvrcqQcQtwj/VpOyqKM6Bb/MIqQLc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5410, "LastWriteTime": "2025-06-24T00:41:15.2116738+00:00"}, "Gw4C/7sutwER6DNcIF5JI8x4J1Qk6fkzLzx1CWTtxLY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pv91aidw81-768xxo33e0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sbm9t2lxqb", "Integrity": "SO7rAf74h99cMMuws/ncxK+EwAJXVdgQJ5sAp1KSbXU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2554, "LastWriteTime": "2025-06-24T00:41:15.2379822+00:00"}, "J3brsJaIEoOG9elMB8RFSD5kS7EkWYH3jX1OtwKP1/g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\nm3k1vr2b0-nolure4ku9.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zo7gpxk5f", "Integrity": "0WF3VkObva6Wa3u4Vat19CvEfUrVJRidnq2b5GDlf6s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2489, "LastWriteTime": "2025-06-24T00:41:15.2585313+00:00"}, "O2FPhkMiVAmYuahPO1jD28dGReRu5zBTxfxl80K7RhM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mv2ne30sfa-l12i2hq2kq.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h8ipoprl0m", "Integrity": "kTRdqnkmDvNToK+j8y8JoBEkgz9+TJWxiZpZFGnsZ9I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10440, "LastWriteTime": "2025-06-24T00:41:15.2752293+00:00"}, "0uP7GvQykKqPxyksguEfSIHxwMeq5wR35sNqSkbF8qs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xm0a60puc7-93x5jg3mkp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uaiucbm1pv", "Integrity": "JvCs1LG2508Ne/b8JNQOKhGfyds6lO3lbhO4gfDAoFY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 16254, "LastWriteTime": "2025-06-24T00:41:15.2924076+00:00"}, "dbaPosbwvwQUhFA1B+ghuV4z8D4foJ0BmsK5MLOvrA8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\fyv9wlngmq-2cfqx8u8uj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5716h6abnu", "Integrity": "kTQaOlLKJ55KtOlrilATCAL1Itqma1FiPT6QGGiyGtA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 15709, "LastWriteTime": "2025-06-24T00:41:15.3351818+00:00"}, "kjwCC7vQ4/PLsCoIT34JarCiCpq9mBzBVYZShYxXi24=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qrxu2zwgr5-wyelmb42ye.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ozm5870g9d", "Integrity": "pIfsvI61HYodf9Y9VWYojaWDL65wjkRFjcxpTvEazOA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2695, "LastWriteTime": "2025-06-24T00:41:15.3515695+00:00"}, "sIgSHkN5NkJh5R/aTQc3DpWtl/K1+I1rwMVnIj/7HFs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ja52lhp99e-dst12xa6e5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "afv0r8aju3", "Integrity": "Fho5O/9o0iNDe3nq9dL374I8HzMvgHYf0XKv1mVHHes=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2459, "LastWriteTime": "2025-06-24T00:41:15.3560959+00:00"}, "X9palUdnpJdANEBTturbb9Pj/AI/UAwe4gukbA5OCRo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\g8d1ll62re-6ei8na5yhg.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3vzd0bntcf", "Integrity": "MU3xjGMY8vD4xnfZH0irqgRDlIBPJurjfIRHMdFgLrE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2322, "LastWriteTime": "2025-06-24T00:41:15.3659736+00:00"}, "Nudy1ghy0+tS2CogmjttxNMG85cLYcaX8dR5joJDDeg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\plvxa4s6ga-3eb8c3pfwe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t50l0nov1q", "Integrity": "EfBffVPWpe58xruJwJsNlZ9rzwMqGZx5/IAZ2lmbNLo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2267, "LastWriteTime": "2025-06-24T00:41:15.380738+00:00"}, "qd1jVix8rcNNPi0O3KDlQmxl0NHZGk9x5NV4b5mb43I=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\arw8hu9x14-171oaphs01.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cscfdewx0f", "Integrity": "hKxOOBQ/fSCsL2LrAMqY/2sULobybRjt4W14A9awCE0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2197, "LastWriteTime": "2025-06-24T00:41:14.9509577+00:00"}, "ElGosWhQT7mqeA+hUIWbiDb1N4DF1uUPs7CrZutq3cM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\il592650ow-1hrv129abp.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tt4ixubui0", "Integrity": "8RH9fKYh+mRXmKintECdQg+YOjEP4UyJL72KP9CV1AA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2324, "LastWriteTime": "2025-06-24T00:41:14.977585+00:00"}, "C7XycsEtISpYqp24hjXfVPUDHEF/h2wL8cQorSnsGxE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ced8wcd8cu-atf14r4yhw.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7l50o3qupp", "Integrity": "S0DHA/lyKN91O8N20+vhKfUiDaVYpC1L1K5kilpR13c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2652, "LastWriteTime": "2025-06-24T00:41:14.9815917+00:00"}, "3LC4Zzr38as0sa5vtFVqhgvffz7/+6cF2Bg+smc0qPU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\eh7hhep0d3-ac8qf4w6lx.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n18lxojb0f", "Integrity": "+yyjpud2HwQ+OGPCdI/UqCTj+pb8244ITQWUvwfpbRw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 177338, "LastWriteTime": "2025-06-24T00:41:15.0583546+00:00"}, "/9tcpkxTe1rSv3N+84xXU4KQYxrjgYY22+jdDOUXcnQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jyu1zp9qgf-nqh8s5tb94.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ax3au530m6", "Integrity": "XnRIHwY2UCoiFriXyUFvCdkZICFGHZEtMa9F8lvzbWw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 10532, "LastWriteTime": "2025-06-24T00:41:15.0769632+00:00"}, "DrATue1a0FGIBak830CFfpR32TrylCtQ8PZwjrcFm0s=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\p1o86wnkwn-61u88luwrz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bolqu0i7zp", "Integrity": "xVHyKZDBFzSye02g1tUAgVfqXS4Q71WdcXJ7u5BfzII=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2151, "LastWriteTime": "2025-06-24T00:41:15.0934065+00:00"}, "H4YEn5Ub+mZD6OGayR5t+2gGRbnlw21puLYV3QsqJQs=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ylntj2h544-kkdt47cvjv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xz07b795u7", "Integrity": "M8ityBtfy534MWphTysAcVhdDUpR9qBJU8TgCh6Pqmw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2184, "LastWriteTime": "2025-06-24T00:41:15.0973234+00:00"}, "m8Sa6wRqckEWpp4hHTduJ8//yiXHUbc28v1GiGtVK14=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xgis4ptx7x-dhjbngzvrh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c4pgkkmsgv", "Integrity": "LKdHfLw90xpicOQyO0W2P3QJlBkiuIVnXgB7U6/Nh8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2952, "LastWriteTime": "2025-06-24T00:41:15.1076039+00:00"}, "SMSnV/B+xDSOvt2vJ4Ju1VktUNibrWZyxPpbHiYFYZ0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8801tn65l9-9c7ym8z6z0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxdot1nk1o", "Integrity": "f63be/hihAtZ17hZpOPi6kiNztHMkZyzgQOztyfKpvQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2529, "LastWriteTime": "2025-06-24T00:41:15.109603+00:00"}, "YwaDgLsUbJ9UasZ9Bzx5gfuHR+jfOf60VM2prrSd1C8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\v8iorw1ao4-74uvd51634.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dgxvrqhl7c", "Integrity": "41GOXv3/61q6PAei8t3AYDrEns2K97f8ymoD3ZV1qyI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2295, "LastWriteTime": "2025-06-24T00:41:15.1411279+00:00"}, "PWwqh0rlHNSIfTGiMdmHa9AUFpIfm3OTAPWIjsxDv98=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f6wx6gd1gt-su7r23l034.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0rv36z1ex9", "Integrity": "hHojCn0xiRCwWyLrslxq6PClbL7a/Q2omlr8wJOxKcc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 514592, "LastWriteTime": "2025-06-24T00:41:15.248056+00:00"}, "3AK8R+x8WdZtyHgDa0ukNghbI1XMy2D9zePzRH/jFuU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7x27t74nhm-1yccpcq7id.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zjhdfezxp8", "Integrity": "H3FtXAVgj7swGReRVg1moh35L+hmMgm+2VXR9Lkelvs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2255, "LastWriteTime": "2025-06-24T00:41:15.2622524+00:00"}, "FgdirFWxPnpQodns6UlZefVjrFNjc+efYNIebm03pmM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ppfdnigb7q-55ue31v3pl.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00652p439q", "Integrity": "oPNYwXcRoWl7uya/HXXYKERu94fKm0CZJ8hFL0CQFZk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2228, "LastWriteTime": "2025-06-24T00:41:15.288767+00:00"}, "XYch4ziQC5mojSk7rl6f7irum057S/OHAaYb3++xZLE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\qdvfj0qa9d-akbtg6mu9j.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j4oh12472a", "Integrity": "nYIailQEVdO/2dsRaY1NdIhXFXX7uJcdj6Ucd719EiI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23478, "LastWriteTime": "2025-06-24T00:41:15.3079241+00:00"}, "0KPhhy3efW770EuTbZygACyc9Fwo5yKBcrxFBg5N5BY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cn4p7z7pyl-c5gx1pj8rz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b1rbk16ps0", "Integrity": "YcohLbWIfGPVOKk2RB5lRX3xuPV9jkO8yA9rXZfUDBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 200887, "LastWriteTime": "2025-06-24T00:41:15.4000172+00:00"}, "gHcbE81AEQ04gkp4JBFU6S7xsl5TN5eSMn8VZ5vgpTw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ipm780nllg-l1kk2a4a0t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qd7nlb140s", "Integrity": "4il47+VZ7DWYRBb7ei/QcLUSSnRYRpW6GrgUevs64i0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 149516, "LastWriteTime": "2025-06-24T00:41:15.4501478+00:00"}, "I+M+Z0NCJ7KnBTo8wOVOYO36UZrlsh8sTPNPnOA1ovM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\jef7gsrqq7-1qhdretbuh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hphvgmocym", "Integrity": "m+yGXO8YqCr6OeBaPgpxUw5KkhVZfiu/UFDLmIz62Lk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 19241, "LastWriteTime": "2025-06-24T00:41:15.4706501+00:00"}, "Q3xfcY2GWViFQYqPgklVzdTIp8ySuhrMo3psMtRyDFQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\laazswwohk-609xepspin.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8j4xfz7b8", "Integrity": "b4ces5UKtHhTT4focWQsQQ4peWgUs91fjW2pXqwd5qw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2300, "LastWriteTime": "2025-06-24T00:41:15.5034525+00:00"}, "9Gf0BgJNKO4J2UN9+JYJxiEnMQEqU7ihw9tzGMFRX50=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\n4x5zi1hy5-emm1p2vibe.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1o27p37ksp", "Integrity": "v+EzzPbHp6+i/owi3b7X51qyihZHGtRtET6End+DclI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 73778, "LastWriteTime": "2025-06-24T00:41:15.5842763+00:00"}, "bDMdfKeY3WL8OO5cpWqLvOV6VEMpR5/4FqmzB76qUOw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\awerrgejpl-68n5xxnez7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o0e1d0n0id", "Integrity": "OJpuywD4IWo13VL/M7DVr0mfCvWbnrbURkle2C5SC9E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2285, "LastWriteTime": "2025-06-24T00:41:15.6017389+00:00"}, "31MqCwaUjAjpzrDdiznKzT0u5XuLhoUaWr6vh3EsNjg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\8nxszxol5d-zchzosoc8t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u4udsecrro", "Integrity": "zu3s3rgiI/1CU8rvuvxPstyBDrDdSOOmmY5JscBhKIY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21575, "LastWriteTime": "2025-06-24T00:41:15.6107333+00:00"}, "9q3bMGEJ47SyNBnjP4k+mFmOHytNlDt1/cWkTMkm7es=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ezx0izcezf-mzcae7sc1w.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zt10t3qa5g", "Integrity": "R1Ji6MJf1BoMC8mughzPcjuSJd2ofWbxqP3nMQIfnhE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2547, "LastWriteTime": "2025-06-24T00:41:15.6265692+00:00"}, "BP4c9f6W9UB5wJ8NnREpWnrhfofAADd4Q3iXLdPyb2o=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\x959zs3735-av82mpp65m.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1wbw7xae8", "Integrity": "+KKEcm6UbopA25E3V1r0Hz9bpPELXokumhb07RvsNAg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2324, "LastWriteTime": "2025-06-24T00:41:15.6295706+00:00"}, "KR1CjrOidRr3nX8dJNtkduNHnFuuywJLUfySput5Oaw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\4pab78fge1-03z9epkgeu.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8685rjzp8e", "Integrity": "YLD6OG8G5k/hY1CmycBl2TwwBXlGeAf4pkos3ZLQ/xI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2249, "LastWriteTime": "2025-06-24T00:41:15.6334768+00:00"}, "FEEzngVHGNRM1DGcPgXRCcy2LyfgNUzroZcUPLEbGs0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\hlv81p94us-tcb84raxfr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ir72gb1chy", "Integrity": "zr8zlBl43Vc05ZggODe9cCOPjq/dkWM58HYX7ofISKQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2122, "LastWriteTime": "2025-06-24T00:41:15.64785+00:00"}, "t057lzgAYxaVRPD7oAL+4mg0dZrsszXh9MAaMlKh2dg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\lx4q04c6vo-j0x2amkbr8.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azdx7qdg1d", "Integrity": "+lvTsQQjzQqjIL0b1dDp2L70w9zLut+pm9Xgk05SGto=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14271, "LastWriteTime": "2025-06-24T00:41:15.658438+00:00"}, "KB+HNd8LJNeYMHbl6DDgaYlE7w0E1AS0Ue5JI+PFosA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ym2sc23egi-3mr0k4ql8l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "07vh6x72bn", "Integrity": "LJISaxlzzsg2/QrGJ9sXmDlBOSkXiCgKIZYTyakiYxU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 51105, "LastWriteTime": "2025-06-24T00:41:15.707118+00:00"}, "UkljKT0qutOxGm7KTh2QD/P6wF+OO1AZcoWrhsVGCHA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\e0p52hsb7u-tebc67toix.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7o4w1c655", "Integrity": "d6jaxiV4B/7ivYnqIJEugtxkYC0Ke2ySkqb530zduCc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2360, "LastWriteTime": "2025-06-24T00:41:14.9519588+00:00"}, "T6jaYascYmoFvzTPaczv+fxSHz7NFbPXj4vQ8cElSas=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\axs689n286-mylfx8o0as.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gb3ss7nrty", "Integrity": "zEfMTLs8gexhvPxrVpWgS4sJWpHVlQvdO3NhEX6Apdw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2168, "LastWriteTime": "2025-06-24T00:41:14.954249+00:00"}, "Rmc9FJQFBz1JNR+Oao6tHxTa8EcVRj64ZCO/GjX7G2w=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\z9l7ueuc70-wj73nmpklo.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3pvi7gcbsb", "Integrity": "CWjbsZdyuHfxzuNNN3TW5gU+J85Ei81pEUkRqOOaZjw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 8370, "LastWriteTime": "2025-06-24T00:41:14.9572652+00:00"}, "q1wZe8N1bQVgKgWYMXpQ97PYiYiXpxQk7wjECLVDOqY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\cajl2o1e7z-fxengc3wyj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m0kte91qtt", "Integrity": "lix+/U0hL690/ReuuOA+NURVSh0IVSPJh0gK2WN+aj0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2111, "LastWriteTime": "2025-06-24T00:41:14.9795918+00:00"}, "N55gVNNcEN4zwYYtarw61mThAfzZQqTKRVfNPykLIb0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\sadg8p8gec-nphd9f66p0.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zzh8nxx6oi", "Integrity": "HzgJnoptUX805KkbZZpCJGQ1DPanHrSj/0of3xNCaYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2266, "LastWriteTime": "2025-06-24T00:41:14.9827253+00:00"}, "Xxc9i0Q/yRJgQ2pWWDGBopOaGGx3lvrhpHmOTgt1AMg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\pzgeww6nnf-8cl47m4jkh.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yoebce8vme", "Integrity": "IuOAYSA7zT4MekVsU+fb6GR/9hV5tJSlCUI8cf4HB74=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2197, "LastWriteTime": "2025-06-24T00:41:14.9911063+00:00"}, "BiIF2EUqHDwyOin6+ObQPYTVjONVWY8toRzD9Sh7jWo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bdjfymnqrw-3yhl940msb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sfnr940nm4", "Integrity": "aKii++1t3eIFbfJ8kxXNztrAmbDf5lCnEWFPG2OiWPE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4009, "LastWriteTime": "2025-06-24T00:41:15.0079579+00:00"}, "x3qRt9XilO0hqkImWvOGahw1pveiWAIWbb7nt33GSoM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\gwugds5sw2-75yss9zhoy.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u69fk6s3z8", "Integrity": "ujcxkCNExrbgBUktEcT8sIcBnXCAqDQpFYtDsYr/DkQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2228, "LastWriteTime": "2025-06-24T00:41:15.0235019+00:00"}, "XL0mzy355ysHKLeRd67hfKJzuaf9FHC0YSU4NHu3Oeo=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\3kjfjnpc9x-i6esjxgs2i.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8k<PERSON><PERSON><PERSON><PERSON>", "Integrity": "OP+3WHGSLQ714XTMeRxcZYvBQFKxupqkANRGFfci+hI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2385, "LastWriteTime": "2025-06-24T00:41:15.0297517+00:00"}, "0I+h9wCVBLTfC12syq+UkDF7jLQF32oG6rBfGLOa/VE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\ddncr1chdl-5xzb5f7wvr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3q3icw7s5t", "Integrity": "W1+mdvDOSqtF5vDo7RbvpKKHcGBwhHhtZyebziHx+/U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2482, "LastWriteTime": "2025-06-24T00:41:15.0349348+00:00"}, "XMCbhombPJ/g9ve/zdmCI/UvBewblpuze3npAdS938A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tb1rghhwn6-uk4ktf4545.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkoq6vbpco", "Integrity": "7ZB8I+L31+JpElgTdmIsmbYiHU8Hq1NNWq3E2hpxfnQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2308, "LastWriteTime": "2025-06-24T00:41:15.0383448+00:00"}, "mxNFGoA6fOTgONgE+NazQ2Vh1QLFG4ncXGcriNWpjIU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\uh9y7ps724-ngnkh62a7t.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qipu9hrpsw", "Integrity": "sln492p7YGgUlXl75mNzNha0rQHV+hKkNEaF22Mv3Ro=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2348, "LastWriteTime": "2025-06-24T00:41:15.0440152+00:00"}, "unMnHLPKJbkSDapNmUlAAtSqyjeXF43GTtqyZjqiLak=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\dc9ab4adpy-tyrk4ywyu5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqur2gucz9", "Integrity": "azbHFvj0G2FOt+zpbeKcqhQJDzyG/31RNStqOIkWSHk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2843, "LastWriteTime": "2025-06-24T00:41:15.0539881+00:00"}, "P6+k62q+OzPj6n3cu83fQlVmfmYJxai+DdIENy+BPoQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7qt3udra8g-ekfbtxon3o.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mazk4w0jj", "Integrity": "f0TlS7a0kYBeQmpINNfz0Ay5ZCIVQoqSbfhAaFIHInY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4201, "LastWriteTime": "2025-06-24T00:41:15.0607252+00:00"}, "6vddWOaQcoAChm6wlA6GaQUI55vspznU8KDS1iwhOzg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\7ycekvj0yv-4isf5pcuol.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wecjxff8x6", "Integrity": "aO2H3P36KeeCoaSb5fPLtgdR3F6Si+/dCry8yVAOyAc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11741, "LastWriteTime": "2025-06-24T00:41:15.0672447+00:00"}, "y+YiysIKWubX67dNZ6vriM4PJYMGoJSZLv1gmo/a/p4=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xw3xqrs1gn-u7plvjpova.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0t6qsm<PERSON><PERSON>", "Integrity": "9BjRdvYM2FEgBAfyV2JzkFm8EfyJfyI6Rpdt2w1ZHmg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2513, "LastWriteTime": "2025-06-24T00:41:15.0789709+00:00"}, "tusun9F36tduSqW33KG1tqQFam6vn6ZEZDmQ8OZHRdQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xv769p21qu-epnagkv91l.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4kcqoa988", "Integrity": "5IpB6j9TP4oWcspGDVI/FYwRJvK44Ca0Sglh/sypQz4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14744, "LastWriteTime": "2025-06-24T00:41:15.0949196+00:00"}, "NZrZ9cqx3S0IR5SqpV6omER0zEP9VhqdUK6AdaDSq64=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iqwuxofz82-u6wau0ktb7.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b12xqtnz2p", "Integrity": "cVkmRyxFNqeMxwYJp1BTXbZkqgQXQz6TfXtHtPjyJkA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 25996, "LastWriteTime": "2025-06-24T00:41:15.109603+00:00"}, "9rirKIzHB30SO1udX/zPQL+UvOYN2oZrviAcmcooyl8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0tq3ubgpl3-710onjtcga.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wu8<PERSON><PERSON><PERSON><PERSON>", "Integrity": "WCtnSrphKRoxTEOreGgMgEpNFHYkj/yE2QhdPZPtHmU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1394567, "LastWriteTime": "2025-06-24T00:41:15.7710736+00:00"}, "VzriRgE9Q3E+G+CV/R5BII2ySrp0yhwrrBofTjvu/x0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\12gy8c80sd-4rejzitfsn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wcqmrrwexh", "Integrity": "EApuGNk8AvmCwl07TLFJG222JaFOdNOcFoqhIUQeBzM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 11445, "LastWriteTime": "2025-06-24T00:41:15.7769809+00:00"}, "UahY0lNSDucOdsNrZPw/zhPWJKK2GK1ZxJK6GB2iY5g=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mrmnme9fy7-3gypxjnyzv.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hllvpd1438", "Integrity": "62MDqadOj+/GIGmhftV1peTz0tId0vCBCXCHa38OKb4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 18726, "LastWriteTime": "2025-06-24T00:41:15.7884769+00:00"}, "wWtaf0KRZhy/c0jGrLJqqStRRD+td7RPmF8r0pXdTaU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\f2nmvc6iyo-vpgbraawsm.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2hpdksa4ob", "Integrity": "NuanXv5ObHQUgmzahq06eVrXcG+WoMunixO8gmQKGMQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 36160, "LastWriteTime": "2025-06-24T00:41:15.8073253+00:00"}, "s1g1ycBT3OaGAQTXuuhvRANNoFixkmuAS6QEDgF334Q=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\92k36dsjrc-61e7sdubtr.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ahan8gc0vy", "Integrity": "zpxDddN4c/cNCws/EZSTYxW//AVvfrNfBVrupVC31kc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1154460, "LastWriteTime": "2025-06-24T00:41:16.1541973+00:00"}, "nuL7tdpYSlytqQf0w6YCy6+C8PlzU2z2WIOQbRitptk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\mgqvc88zci-ywccyuk0ea.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvhsn92mqz", "Integrity": "e9swblY/HDrM4H+jYQUqr8xKrdMNLra5T0+d7BOz+2c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 64973, "LastWriteTime": "2025-06-24T00:41:16.1792568+00:00"}, "fWd1DTWVaC/OKTlnFMU7G8RlaB+CO9R6FqyaSaZOfqM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\v9ucdunf1s-nmlfvbueei.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilzgw5gy6t", "Integrity": "yhOL83W6Mgf0jJLwHzMZGA4NkEfwQoUJmCpJRya8LBw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 100294, "LastWriteTime": "2025-06-24T00:41:16.2065303+00:00"}, "ONb7N/fJ84AonrokZAeih5vYjGbEEJB5Q8i9CJI/6jY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\90hp2p71hu-tjcz0u77k5.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "su9h2nea1m", "Integrity": "JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 333110, "LastWriteTime": "2025-06-24T00:41:16.279764+00:00"}, "DsXYSll46VUxVSmvNAztr1TSS9a+GdLgHh7YQRX5vQg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\iolod2it8d-tptq2av103.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnxfkgr4e8", "Integrity": "G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 196037, "LastWriteTime": "2025-06-24T00:41:16.317454+00:00"}, "HUExLFLCX0kLL1f/Z70I7iOyRquxDbwJczrm7xD3xD8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\j19hqecu3k-lfu7j35m59.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v385ycndre", "Integrity": "S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 317618, "LastWriteTime": "2025-06-24T00:41:15.0383448+00:00"}, "XdHRty8FjAxG8O8hRsAeLhiblJJDuvOKxMiRZyH7oaw=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\i4ozfjtw4i-oyz0vx2fzb.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/emcc-props.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\emcc-props.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6rw5k6cf8e", "Integrity": "7t6AVk6lvrWEqY7hRavzlgS107PQ4doQEFxFK3dDtRQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\emcc-props.json", "FileLength": 592, "LastWriteTime": "2025-06-24T00:41:15.0470293+00:00"}, "1bT2qZyfUNe5wcMubErOGst9STuyXUMVsJ62AHircYQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\0qonx4ygs9-5ji7zhsm1z.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicCommon.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8s8fu3oh94", "Integrity": "yJufC6pZheO1sBa0bhbPLiAFBvvL1CFNvHiK/Hzk80E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.wasm", "FileLength": 5164, "LastWriteTime": "2025-06-24T00:41:15.0622056+00:00"}, "9fOP4NM+Xm7yAFFbAVlJWBifgeq7Z9PEv6DwX8za+6U=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\esi1du385w-qccb051paj.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicCommon.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jm2bhh0vo3", "Integrity": "9chB0S0Ie0/BPmmOacWyOl6kx8dSqlOPUvDkECs8txE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicCommon.pdb", "FileLength": 7470, "LastWriteTime": "2025-06-24T00:41:15.0789709+00:00"}, "APC+o8CyQU6YI8NUa8OLRBuiYWYU7u+b0u/hALM1YPQ=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\tga4xb2zqw-xhw5dycakn.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ptlq11l48z", "Integrity": "/YVAhV0zGzAzuXOA6ZMKCzFdPLKBzlL6Fk1z5KTZcA4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 9607, "LastWriteTime": "2025-06-24T00:41:15.1622001+00:00"}, "iUjCzA8zuCeQnNLlsQ/mGY64sH+w3cPXzrPkFDDJVXk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\bqkz6kxeo8-6dmq98h3oa.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicApp.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oxq8dto5co", "Integrity": "pERLkUtpq5W345pQttrcsFBKCsJ+1vFRXTmxNTAMfKI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.pdb", "FileLength": 26889, "LastWriteTime": "2025-06-24T00:41:15.1568726+00:00"}, "ZLGNONgfmTJ28+MpBnmOnErXlD6srSnqhdJjjL0BOxc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\xvf58668z8-4b6qh3humz.gz", "SourceId": "ShiningCMusicApp", "SourceType": "Computed", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\Debug\\net8.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ShiningCMusicApp.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "us5992d325", "Integrity": "ngYnbvKd7SIr0tF/wcGYydR1osv0ocs5MnzgjAfWfHo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\bin\\Debug\\net8.0\\wwwroot\\_framework\\ShiningCMusicApp.wasm", "FileLength": 22440, "LastWriteTime": "2025-06-24T00:41:15.109603+00:00"}}, "CachedCopyCandidates": {}}