{"format": 1, "restore": {"C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\ShiningCMusicApp.csproj": {}}, "projects": {"C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\ShiningCMusicApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\ShiningCMusicApp.csproj", "projectName": "ShiningCMusicApp", "projectPath": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\ShiningCMusicApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicCommon\\ShiningCMusicCommon.csproj": {"projectPath": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicCommon\\ShiningCMusicCommon.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer": {"suppressParent": "All", "target": "Package", "version": "[8.0.17, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.17, )", "autoReferenced": true}, "Microsoft.NET.Sdk.WebAssembly.Pack": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}, "Syncfusion.Blazor.Schedule": {"target": "Package", "version": "[29.2.11, )"}, "Syncfusion.Blazor.Themes": {"target": "Package", "version": "[29.2.11, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.browser-wasm", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"browser-wasm": {"#import": []}}}, "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicCommon\\ShiningCMusicCommon.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicCommon\\ShiningCMusicCommon.csproj", "projectName": "ShiningCMusicCommon", "projectPath": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicCommon\\ShiningCMusicCommon.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicCommon\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}