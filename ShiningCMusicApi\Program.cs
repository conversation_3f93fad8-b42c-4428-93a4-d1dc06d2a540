using IdentityServer4.Models;
using ShiningCMusicApi.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// Add connection string
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);

// Add our services
builder.Services.AddScoped<ILessonService, LessonService>();

// Add IdentityServer4
builder.Services.AddIdentityServer()
    .AddDeveloperSigningCredential()
    .AddInMemoryClients(GetClients())
    .AddInMemoryApiScopes(GetApiScopes())
    .AddInMemoryApiResources(GetApiResources());

var apiBaseUrl = Environment.GetEnvironmentVariable("API_BASE_URL") ?? builder.Configuration["ApiBaseUrl"];

// Add Authentication
builder.Services.AddAuthentication("Bearer")
    .AddIdentityServerAuthentication("Bearer", options =>
    {
        options.Authority = apiBaseUrl;
        options.RequireHttpsMetadata = false;
        options.ApiName = "ShiningCMusicApi";
    });

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorApp", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseBlazorFrameworkFiles();
app.UseStaticFiles();
app.UseCors("AllowBlazorApp");
app.UseIdentityServer();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();

// IdentityServer4 Configuration Methods
static IEnumerable<Client> GetClients()
{
    return new List<Client>
    {
        new Client
        {
            ClientId = "wasm_client",
            ClientSecrets = { new Secret("AE6qbzhQ08kW".Sha256()) },
            AllowedGrantTypes = GrantTypes.ClientCredentials,
            AllowedScopes = { "ShiningCMusicApi" },
            AccessTokenLifetime = 3600,
            AllowedCorsOrigins = { "http://localhost:5243", "https://localhost:7243" }
        },
        new Client
        {
            ClientId = "admin",
            ClientSecrets = { new Secret("WpujhG4r3h0K".Sha256()) },
            AllowedGrantTypes = GrantTypes.ClientCredentials,
            AllowedScopes = { "ShiningCMusicApi" },
            AccessTokenLifetime = 3600,
            AllowedCorsOrigins = { "http://localhost:5243", "https://localhost:7243" }
        }
    };
}

static IEnumerable<ApiScope> GetApiScopes()
{
    return new List<ApiScope>
    {
        new ApiScope("ShiningCMusicApi", "Shining C Music API")
    };
}

static IEnumerable<ApiResource> GetApiResources()
{
    return new List<ApiResource>
    {
        new ApiResource("ShiningCMusicApi", "Shining C Music API")
        {
            Scopes = { "ShiningCMusicApi" }
        }
    };
}
