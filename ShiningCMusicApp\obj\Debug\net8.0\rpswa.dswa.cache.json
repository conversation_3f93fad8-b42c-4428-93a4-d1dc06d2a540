{"GlobalPropertiesHash": "oWTDTBNB38PWByVBdHYyLvM6WMPVdR5kNUPXu6Gnffw=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["itWdeLns0+ji8izDCWLN13qjIOvK1GcSfD0G0/qN/K0=", "3Vo2ktjYAGElu8U7EaF6t+pZ6amNesEiuO2eZmMWTrk=", "Fpr6IWCLPnPE7oOQfKNqQxWy6FzCrt9lajHv2x2Cpd8=", "Yr2fSertLNUZYqzW3vJDrmMP01guTgEaFs0JTWhGdLY=", "0q/FRpOkBnctVbgYDE12OXDmTMnO665vu0gcbHfgqOA=", "hKHUrd3vT42qxfibrO9MVPf5WCqHx5kqpOwkobqTvPk=", "tJQpRxUPOehWLdNdr6d8pEo4fsZWZuIgIMrFtdljPcc=", "jAxJsfHMDdlohqis8AV2zA8VRmt9C91E76rxbrCSymM=", "FY7UIOVR9UJBjg8Rd/pSjCyFEa5X/ONFEbT5yO4WzVQ=", "9x9mRCnpEWNBnI62l5NAcmx202WF4blLgLOlqZbpcOA=", "Pkx4rG11rUuAfSsODyYm7TqHnH/Xig88NBpWiixvt4w=", "CLq+Gtflgvl5VZaO7Tpv3uc+RPa+jk3Xn1SXTbXzyuo=", "ImdQ4LjZ8UU9HH4G/FTZlBA3uXYcm1D2W8vJiSYc+ps=", "nRJ3JKeBC1QnctQNl2H9V3fvdaouyXk83f0+4u6IAZI=", "wUk5fOaUZMuOSXJ2lvHwk7EAqXmwO0h9JL5BP/+nmsE="], "CachedAssets": {"itWdeLns0+ji8izDCWLN13qjIOvK1GcSfD0G0/qN/K0=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\app.css", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "de2vttob9b", "Integrity": "G9EyFWGhgOO430qYGCrvTwdO3i0i4yZXDr9HEmT+krU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 5358, "LastWriteTime": "2025-06-24T01:39:56.652973+00:00"}, "3Vo2ktjYAGElu8U7EaF6t+pZ6amNesEiuO2eZmMWTrk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-23T22:15:01.5706541+00:00"}, "Fpr6IWCLPnPE7oOQfKNqQxWy6FzCrt9lajHv2x2Cpd8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-23T22:15:01.5940332+00:00"}, "Yr2fSertLNUZYqzW3vJDrmMP01guTgEaFs0JTWhGdLY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\favicon.png", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-23T22:15:01.5058924+00:00"}, "0q/FRpOkBnctVbgYDE12OXDmTMnO665vu0gcbHfgqOA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\icon-192.png", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-06-22T04:27:02.4188623+00:00"}, "hKHUrd3vT42qxfibrO9MVPf5WCqHx5kqpOwkobqTvPk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\index.html", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zj8nc4ugz6", "Integrity": "8Xg+R8paTj+SUskBgrimNLD6aM3RkXwBUYjpvUsjOr8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1290, "LastWriteTime": "2025-06-24T01:38:09.9370002+00:00"}, "tJQpRxUPOehWLdNdr6d8pEo4fsZWZuIgIMrFtdljPcc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\sample-data\\weather.json", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-06-23T22:15:01.3955943+00:00"}}, "CachedCopyCandidates": {}}