{"GlobalPropertiesHash": "oWTDTBNB38PWByVBdHYyLvM6WMPVdR5kNUPXu6Gnffw=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["aEFNFtDlpsWJ4eTDMuOZYnaoyrEtyYSu2SQ0vDBQbWE=", "3Vo2ktjYAGElu8U7EaF6t+pZ6amNesEiuO2eZmMWTrk=", "Fpr6IWCLPnPE7oOQfKNqQxWy6FzCrt9lajHv2x2Cpd8=", "Yr2fSertLNUZYqzW3vJDrmMP01guTgEaFs0JTWhGdLY=", "0q/FRpOkBnctVbgYDE12OXDmTMnO665vu0gcbHfgqOA=", "XXS+WH4yW1e5kuIqCaYI3UNIb1cjaX3WRum3Db+2cQg=", "tJQpRxUPOehWLdNdr6d8pEo4fsZWZuIgIMrFtdljPcc=", "jAxJsfHMDdlohqis8AV2zA8VRmt9C91E76rxbrCSymM=", "FY7UIOVR9UJBjg8Rd/pSjCyFEa5X/ONFEbT5yO4WzVQ=", "9x9mRCnpEWNBnI62l5NAcmx202WF4blLgLOlqZbpcOA=", "Pkx4rG11rUuAfSsODyYm7TqHnH/Xig88NBpWiixvt4w=", "cIo7UflKM8QJYIiSTTX/TwzZGY3kvCkbYAwxKN7qrsg=", "ImdQ4LjZ8UU9HH4G/FTZlBA3uXYcm1D2W8vJiSYc+ps=", "nRJ3JKeBC1QnctQNl2H9V3fvdaouyXk83f0+4u6IAZI=", "wUk5fOaUZMuOSXJ2lvHwk7EAqXmwO0h9JL5BP/+nmsE="], "CachedAssets": {"aEFNFtDlpsWJ4eTDMuOZYnaoyrEtyYSu2SQ0vDBQbWE=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\app.css", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v6sr9w28wq", "Integrity": "dxBHlege6oNX4Y54C/LsDN9gKk2LsLHom9dm2jNjyIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 5358, "LastWriteTime": "2025-06-24T01:10:27.685801+00:00"}, "3Vo2ktjYAGElu8U7EaF6t+pZ6amNesEiuO2eZmMWTrk=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-23T22:15:01.5706541+00:00"}, "Fpr6IWCLPnPE7oOQfKNqQxWy6FzCrt9lajHv2x2Cpd8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-23T22:15:01.5940332+00:00"}, "Yr2fSertLNUZYqzW3vJDrmMP01guTgEaFs0JTWhGdLY=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\favicon.png", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-23T22:15:01.5058924+00:00"}, "0q/FRpOkBnctVbgYDE12OXDmTMnO665vu0gcbHfgqOA=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\icon-192.png", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-06-22T04:27:02.4188623+00:00"}, "XXS+WH4yW1e5kuIqCaYI3UNIb1cjaX3WRum3Db+2cQg=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\index.html", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mq9466ctzo", "Integrity": "Tqjx4mmQF2NUnCDBDmxXjDpASgLAUs1w0je5qLrMZoA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1175, "LastWriteTime": "2025-06-24T00:30:51.9041769+00:00"}, "tJQpRxUPOehWLdNdr6d8pEo4fsZWZuIgIMrFtdljPcc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\sample-data\\weather.json", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-06-23T22:15:01.3955943+00:00"}}, "CachedCopyCandidates": {}}