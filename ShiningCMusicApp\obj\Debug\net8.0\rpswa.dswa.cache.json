{"GlobalPropertiesHash": "oWTDTBNB38PWByVBdHYyLvM6WMPVdR5kNUPXu6Gnffw=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["U/XYf2DHe/B0Vq38n0z4Jg9K8Hj2f0dQgG+MGSd8i18=", "rD9xu3HcugtXJQ7LnNqpU3fHYFQRFvaZKLVkj/KXVco=", "2zbqlT17l5ezFimhpj8bZOUNJ4P3P+DDbom8Mj54Nfc=", "VhtuQxPBDn0FZqq3oAaE1A7ZFLTblhRfW8jNqQUQqgU=", "rGENPbBEAQ7+HLRE+19D0EE/OIQFujdKJma+g9ioiI8=", "uJMlOVSSn5SiCIL2fJuWF+Vn3ZPaznfNGnqfPGVvv2A=", "MO0x/wcx+lkoRf86wcwksSNQnRpDkueYcL+Z9pp4UtM=", "xi6D3kcc2OakEbNxgEXujw4JrAlq5F6S8VcN5NgyMh4=", "gV9eokyRp6ovYSSmtnalT00toX09XS2vwH6mJ0cuZU8=", "5aACzrphxzj2CH33rshRopUrwhyZTXRc8cZZE3/BDVE=", "brjlWdfiorVEh6oHurf4G0VP74cRPqriUbban4pFYU4=", "cHBCeXt5nwGis++n0hqrM/C7uuaOFY/efeuHM8cR8dI=", "k4TSsYRZKtSHdumG9GSirDQzpbx94fXwa2Hs2201gCg=", "YvRLqABo16sLTuIotUwfWQp5ptse8JBckRY4lzt2Sfc=", "fw1II2BPYzOSwoOEFyJsS1ubUt6VxkxKP8Me5qfpZPc="], "CachedAssets": {"MO0x/wcx+lkoRf86wcwksSNQnRpDkueYcL+Z9pp4UtM=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\sample-data\\weather.json", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-06-23T22:15:01.3955943+00:00"}, "uJMlOVSSn5SiCIL2fJuWF+Vn3ZPaznfNGnqfPGVvv2A=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\index.html", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zj8nc4ugz6", "Integrity": "8Xg+R8paTj+SUskBgrimNLD6aM3RkXwBUYjpvUsjOr8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1290, "LastWriteTime": "2025-06-24T01:22:22.4652711+00:00"}, "rGENPbBEAQ7+HLRE+19D0EE/OIQFujdKJma+g9ioiI8=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\icon-192.png", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-06-22T04:27:02.4188623+00:00"}, "VhtuQxPBDn0FZqq3oAaE1A7ZFLTblhRfW8jNqQUQqgU=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\favicon.png", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-23T22:15:01.5058924+00:00"}, "2zbqlT17l5ezFimhpj8bZOUNJ4P3P+DDbom8Mj54Nfc=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-23T22:15:01.5940332+00:00"}, "rD9xu3HcugtXJQ7LnNqpU3fHYFQRFvaZKLVkj/KXVco=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-23T22:15:01.5706541+00:00"}, "U/XYf2DHe/B0Vq38n0z4Jg9K8Hj2f0dQgG+MGSd8i18=": {"Identity": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\css\\app.css", "SourceId": "ShiningCMusicApp", "SourceType": "Discovered", "ContentRoot": "C:\\DevOps\\repos\\ShiningCMusicApp\\ShiningCMusicApp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v6sr9w28wq", "Integrity": "dxBHlege6oNX4Y54C/LsDN9gKk2LsLHom9dm2jNjyIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 5358, "LastWriteTime": "2025-06-24T01:10:27.685801+00:00"}}, "CachedCopyCandidates": {}}