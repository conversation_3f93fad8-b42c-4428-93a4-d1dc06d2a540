{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ShiningCMusicApi/1.0.0": {"dependencies": {"Dapper": "2.1.35", "IdentityServer4": "4.1.2", "IdentityServer4.AccessTokenValidation": "3.0.1", "ShiningCMusicCommon": "1.0.0", "Swashbuckle.AspNetCore": "6.6.2", "System.Data.SqlClient": "4.8.6"}, "runtime": {"ShiningCMusicApi.dll": {}}}, "Dapper/2.1.35": {"runtime": {"lib/net7.0/Dapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.35.13827"}}}, "IdentityModel/4.4.0": {"dependencies": {"Newtonsoft.Json": "12.0.2", "System.Text.Encodings.Web": "4.7.0"}, "runtime": {"lib/netstandard2.0/IdentityModel.dll": {"assemblyVersion": "4.4.0.0", "fileVersion": "4.4.0.0"}}}, "IdentityModel.AspNetCore.OAuth2Introspection/4.0.1": {"dependencies": {"IdentityModel": "4.4.0"}, "runtime": {"lib/netcoreapp3.0/IdentityModel.AspNetCore.OAuth2Introspection.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.1.0"}}}, "IdentityServer4/4.1.2": {"dependencies": {"IdentityModel": "4.4.0", "IdentityServer4.Storage": "4.1.2", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "3.1.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.6.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netcoreapp3.1/IdentityServer4.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "IdentityServer4.AccessTokenValidation/3.0.1": {"dependencies": {"IdentityModel.AspNetCore.OAuth2Introspection": "4.0.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "3.0.0"}, "runtime": {"lib/netcoreapp3.0/IdentityServer4.AccessTokenValidation.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.1.0"}}}, "IdentityServer4.Storage/4.1.2": {"dependencies": {"IdentityModel": "4.4.0"}, "runtime": {"lib/netstandard2.0/IdentityServer4.Storage.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/3.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.6.0"}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.19.46502"}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/3.1.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.6.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56601"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.IdentityModel.JsonWebTokens/5.6.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.6.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "5.6.0.61018"}}}, "Microsoft.IdentityModel.Logging/5.6.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "5.6.0.61018"}}}, "Microsoft.IdentityModel.Protocols/5.6.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.6.0", "Microsoft.IdentityModel.Tokens": "5.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "5.6.0.61018"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.6.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "5.6.0", "Newtonsoft.Json": "12.0.2", "System.IdentityModel.Tokens.Jwt": "5.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "5.6.0.61018"}}}, "Microsoft.IdentityModel.Tokens/5.6.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.6.0", "Newtonsoft.Json": "12.0.2", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "5.6.0.61018"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Newtonsoft.Json/12.0.2": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "12.0.2.23222"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Swashbuckle.AspNetCore/6.6.2": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "Swashbuckle.AspNetCore.SwaggerGen": "6.6.2", "Swashbuckle.AspNetCore.SwaggerUI": "6.6.2"}}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.6.2.401"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.6.2"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.6.2.401"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.6.2.401"}}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.IdentityModel.Tokens.Jwt/5.6.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "5.6.0", "Microsoft.IdentityModel.Tokens": "5.6.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "5.6.0.61018"}}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encodings.Web/4.7.0": {}, "ShiningCMusicCommon/1.0.0": {"runtime": {"ShiningCMusicCommon.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"ShiningCMusicApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dapper/2.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-YKRwjVfrG7GYOovlGyQoMvr1/IJdn+7QzNXJxyMh0YfFF5yvDmTYaJOVYWsckreNjGsGSEtrMTpnzxTUq/tZQw==", "path": "dapper/2.1.35", "hashPath": "dapper.2.1.35.nupkg.sha512"}, "IdentityModel/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-b18wrIx5wnZlMxAX7oVsE+nDtAJ4hajYlH0xPlaRvo4r/fz08K6pPeZvbiqS9nfNbzfIgLFmNX+FL9qR9ZR5PA==", "path": "identitymodel/4.4.0", "hashPath": "identitymodel.4.4.0.nupkg.sha512"}, "IdentityModel.AspNetCore.OAuth2Introspection/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZNdMZMaj9fqR3j50vYsu+1U3QGd6n8+fqwf+a8mCTcmXGor+HgFDfdq0mM34bsmD6uEgAQup7sv2ZW5kR36dbA==", "path": "identitymodel.aspnetcore.oauth2introspection/4.0.1", "hashPath": "identitymodel.aspnetcore.oauth2introspection.4.0.1.nupkg.sha512"}, "IdentityServer4/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-blaxxGuOA7v/w1q+fxn97wZ+x2ecG1ZD4mc/N/ZOXMNeFZZhqv+4LF26Gecyik3nWrJPmbMEtQbLmRsKG8k61w==", "path": "identityserver4/4.1.2", "hashPath": "identityserver4.4.1.2.nupkg.sha512"}, "IdentityServer4.AccessTokenValidation/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qu/M6UyN4o9NVep7q545Ms7hYAnsQqSdLbN1Fjjrn4m35lyBfeQPSSNzDryAKHbodyWOQfHaOqKEyMEJQ5Rpgw==", "path": "identityserver4.accesstokenvalidation/3.0.1", "hashPath": "identityserver4.accesstokenvalidation.3.0.1.nupkg.sha512"}, "IdentityServer4.Storage/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-KoSffyZyyeCNTIyJiZnCuPakJ1QbCHlpty6gbWUj/7yl+w0PXIchgmmJnJSvddzBb8iZ2xew/vGlxWUIP17P2g==", "path": "identityserver4.storage/4.1.2", "hashPath": "identityserver4.storage.4.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FrlxgBG6+FKlYr1IVxXNXSK7jpxiIYf036VNR1UgopKSIUNjaK2zvkhfVgFZVavagitQ/+UZ8Snnm6axyAVRNg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/3.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.3.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-O1cAQYUTU8EfRqwc5/rfTns4E4hKlFlg59fuKRrST+PzsxI6H07KqRN/JjdYhAuVYxF8jPnIGbj+zuc5paOWUw==", "path": "microsoft.aspnetcore.authentication.openidconnect/3.1.0", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-0q0U1W+gX1jmfmv7uU7GXFGB518atmSwucxsVwPGpuaGS3jwd2tUi+Gau+ezxR6oAFEBFKG9lz/fxRZzGMeDXg==", "path": "microsoft.identitymodel.jsonwebtokens/5.6.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.5.6.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-zEDrfEVW5x5w2hbTV94WwAcWvtue5hNTXYqoPh3ypF6U8csm09JazEYy+VPp2RtczkyMfcsvWY9Fea17e+isYQ==", "path": "microsoft.identitymodel.logging/5.6.0", "hashPath": "microsoft.identitymodel.logging.5.6.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-ei7YqYx0pIFL6JjK8ZnPK0MXZRWUNHtJPUl3KqSvj9+2f5CMa6GRSEC+BMDHr17tP6yujYUg0IQOcKzmC7qN5g==", "path": "microsoft.identitymodel.protocols/5.6.0", "hashPath": "microsoft.identitymodel.protocols.5.6.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-yh3n+uXiwpBy/5+t67tYcmRxb9kwQdaKRyG/DNipRMF37bg5Jr0vENOo1BQz6OySMl5WIK544SzPjtr7/KkucA==", "path": "microsoft.identitymodel.protocols.openidconnect/5.6.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.5.6.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-C3OqR3QfBQ7wcC7yAsdMQqay87OsV6yWPYG/Ai3n7dvmWIGkouQhXoVxRP0xz3cAFL4hxZBXyw4aLTC421PaMg==", "path": "microsoft.identitymodel.tokens/5.6.0", "hashPath": "microsoft.identitymodel.tokens.5.6.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Newtonsoft.Json/12.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-rTK0s2EKlfHsQsH6Yx2smvcTCeyoDNgCW7FEYyV01drPlh2T243PR2DiDXqtC5N4GDm4Ma/lkxfW5a/4793vbA==", "path": "newtonsoft.json/12.0.2", "hashPath": "newtonsoft.json.12.0.2.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-+NB4UYVYN6AhDSjW0IJAd1AGD8V33gemFNLPaxKTtPkHB+HaKAKf9MGAEUPivEWvqeQfcKIw8lJaHq6LHljRuw==", "path": "swashbuckle.aspnetcore/6.6.2", "hashPath": "swashbuckle.aspnetcore.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-ovgPTSYX83UrQUWiS5vzDcJ8TEX1MAxBgDFMK45rC24MorHEPQlZAHlaXj/yth4Zf6xcktpUgTEBvffRQVwDKA==", "path": "swashbuckle.aspnetcore.swagger/6.6.2", "hashPath": "swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-zv4ikn4AT1VYuOsDCpktLq4QDq08e7Utzbir86M5/ZkRaLXbCPF11E1/vTmOiDzRTl0zTZINQU2qLKwTcHgfrA==", "path": "swashbuckle.aspnetcore.swaggergen/6.6.2", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-mBBb+/8Hm2Q3Wygag+hu2jj69tZW5psuv0vMRXY07Wy+Rrj40vRP8ZTbKBhs91r45/HXT4aY4z0iSBYx1h6JvA==", "path": "swashbuckle.aspnetcore.swaggerui/6.6.2", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/5.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMvPpX4exs2fe7Upq5zHMSR4yupc+jy8WG8yjucZL0XvT+r/T0hRvLIe9fP/SeN8/UVxFYBRAkRI5k1zbRGqmA==", "path": "system.identitymodel.tokens.jwt/5.6.0", "hashPath": "system.identitymodel.tokens.jwt.5.6.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-IJanJWPQvya2sbGStt3Fkdy4IaomUBSadAfYWeJDQw0zclMk9ixSvMeei6cSmTTQ6ZkGIIAbhHZVCoLR7GgX7Q==", "path": "system.text.encodings.web/4.7.0", "hashPath": "system.text.encodings.web.4.7.0.nupkg.sha512"}, "ShiningCMusicCommon/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}