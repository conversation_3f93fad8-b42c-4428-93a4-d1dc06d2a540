@page "/lessons"
@using ShiningCMusicCommon.Models
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Schedule
@inject ILessonApiService LessonApi
@inject IJSRuntime JSRuntime

<PageTitle>Lesson Time Table</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">🎵 Lesson Time Table</h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading lessons...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Schedule (@scheduleEvents.Count lessons loaded)</h5>
                        @if (scheduleEvents.Any())
                        {
                            <small class="text-muted">
                                Next lesson: @scheduleEvents.FirstOrDefault()?.Subject at @scheduleEvents.FirstOrDefault()?.StartTime.ToString("g")
                            </small>
                        }
                        <div>
                            <button class="btn btn-primary btn-sm" @onclick="RefreshData">
                                <i class="fas fa-refresh"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <SfSchedule TValue="ScheduleEvent"
                                   @bind-SelectedDate="@selectedDate"
                                   Height="800px"
                                   @bind-CurrentView="@currentView"
                                   FirstDayOfWeek="1"
                                   StartHour="06:00"
                                   EndHour="22:00"
                                   @ref="scheduleRef">

                            <ScheduleEvents TValue="ScheduleEvent"
                                          OnActionBegin="OnActionBegin"
                                          ActionCompleted="OnActionComplete"
                                          EventRendered="OnEventRendered"></ScheduleEvents>

                            <ScheduleTimeScale Enable="true" Interval="30" SlotCount="1"></ScheduleTimeScale>

                            <ScheduleEventSettings DataSource="@scheduleEvents"
                                                 TValue="ScheduleEvent">
                                <Template>
                                    @{
                                        var eventData = context as ScheduleEvent;
                                    }
                                    <div class="template-wrap">
                                        <div class="subject">@(eventData?.Subject)</div>
                                        @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                        {
                                            <div class="tutor">
                                                <i class="bi bi-person-fill"></i>@(eventData.TutorName)
                                            </div>
                                        }
                                        @if (!string.IsNullOrEmpty(eventData?.StudentName))
                                        {
                                            <div class="student">
                                                <i class="bi bi-person"></i>@(eventData.StudentName)
                                            </div>
                                        }
                                    </div>
                                </Template>
                            </ScheduleEventSettings>

                            <ScheduleTemplates>
                                <EditorTemplate Context="eventData">
                                    @{
                                        var scheduleEvent = eventData as ScheduleEvent ?? new ScheduleEvent();
                                    }
                                    <div class="custom-event-editor">
                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label">Subject</label>
                                                <input type="text" class="form-control" @bind="scheduleEvent.Subject" placeholder="Enter lesson subject" />
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Tutor</label>
                                                <select class="form-select" @bind="scheduleEvent.TutorId" @bind:after="OnTutorChanged">
                                                    <option value="0">Select Tutor</option>
                                                    @foreach (var tutor in tutors)
                                                    {
                                                        <option value="@tutor.TutorId">@tutor.TutorName</option>
                                                    }
                                                </select>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Student</label>
                                                <select class="form-select" @bind="scheduleEvent.StudentId">
                                                    <option value="0">Select Student</option>
                                                    @foreach (var student in students)
                                                    {
                                                        <option value="@student.StudentId">@student.StudentName</option>
                                                    }
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Event Color</label>
                                                <div class="d-flex align-items-center">
                                                    <input type="color" class="form-control form-control-color me-2"
                                                           @bind="scheduleEvent.CategoryColor"
                                                           style="width: 60px; height: 38px;" />
                                                    <small class="text-muted">
                                                        @if (scheduleEvent.TutorId > 0)
                                                        {
                                                            var tutorName = tutors.FirstOrDefault(t => t.TutorId == scheduleEvent.TutorId)?.TutorName;
                                                            <span>@tutorName's color</span>
                                                        }
                                                        else
                                                        {
                                                            <span>Select tutor to auto-assign color</span>
                                                        }
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Location</label>
                                                <input type="text" class="form-control" @bind="scheduleEvent.Location" placeholder="Enter location" />
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Start Time</label>
                                                <input type="datetime-local" class="form-control" @bind="scheduleEvent.StartTime" />
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">End Time</label>
                                                <input type="datetime-local" class="form-control" @bind="scheduleEvent.EndTime" />
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Location</label>
                                                <input type="text" class="form-control" @bind="scheduleEvent.Location" placeholder="Enter location" />
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">All Day</label>
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" @bind="scheduleEvent.IsAllDay" />
                                                    <label class="form-check-label">All Day Event</label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" rows="3" @bind="scheduleEvent.Description" placeholder="Enter lesson description"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </EditorTemplate>
                            </ScheduleTemplates>

                            
                            <ScheduleViews>
                                <ScheduleView Option="View.Day"></ScheduleView>
                                <ScheduleView Option="View.Week"></ScheduleView>
                                <ScheduleView Option="View.Month"></ScheduleView>
                            </ScheduleViews>

                            <ScheduleQuickInfoTemplates TemplateType="TemplateType.Event">
                                <ContentTemplate>
                                    @{
                                        var eventData = context as ScheduleEvent;
                                    }
                                    <div class="quick-info">
                                        <div class="event-title">@(eventData?.Subject)</div>
                                        <div class="event-details">
                                            <p>
                                                <i class="bi bi-clock"></i>
                                                @(eventData?.StartTime.ToString("MMM dd, yyyy h:mm tt")) - @(eventData?.EndTime.ToString("h:mm tt"))
                                            </p>
                                            @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                            {
                                                <p>
                                                    <i class="bi bi-person-fill"></i>
                                                    <strong>Tutor:</strong> @(eventData.TutorName)
                                                </p>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.StudentName))
                                            {
                                                <p>
                                                    <i class="bi bi-person"></i>
                                                    <strong>Student:</strong> @(eventData.StudentName)
                                                </p>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.Location))
                                            {
                                                <p>
                                                    <i class="bi bi-geo-alt"></i>
                                                    <strong>Location:</strong> @(eventData.Location)
                                                </p>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.Description))
                                            {
                                                <p>
                                                    <i class="bi bi-card-text"></i>
                                                    <strong>Description:</strong> @(eventData.Description)
                                                </p>
                                            }
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </ScheduleQuickInfoTemplates>

                        </SfSchedule>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<ScheduleEvent> scheduleEvents = new();
    private List<Tutor> tutors = new();
    private List<Student> students = new();
    private DateTime selectedDate = DateTime.Today;
    private View currentView = View.Week;
    private bool isLoading = true;
    private SfSchedule<ScheduleEvent>? scheduleRef;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load data from API
            var lessonsTask = LessonApi.GetLessonsAsync();
            var tutorsTask = LessonApi.GetTutorsAsync();
            var studentsTask = LessonApi.GetStudentsAsync();

            await Task.WhenAll(lessonsTask, tutorsTask, studentsTask);

            scheduleEvents = await lessonsTask;
            tutors = await tutorsTask;
            students = await studentsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {scheduleEvents.Count} lessons, {tutors.Count} tutors, {students.Count} students");

            // Debug: Log the actual lesson data and ensure proper DateTime format
            foreach (var lesson in scheduleEvents)
            {
                await JSRuntime.InvokeVoidAsync("console.log", $"Lesson: {lesson.Subject}, Start: {lesson.StartTime}, End: {lesson.EndTime}");

                // Ensure dates are in local time and properly formatted
                if (lesson.StartTime.Kind == DateTimeKind.Utc)
                {
                    lesson.StartTime = lesson.StartTime.ToLocalTime();
                }
                if (lesson.EndTime.Kind == DateTimeKind.Utc)
                {
                    lesson.EndTime = lesson.EndTime.ToLocalTime();
                }

                // Ensure dates have DateTimeKind.Local
                lesson.StartTime = DateTime.SpecifyKind(lesson.StartTime, DateTimeKind.Local);
                lesson.EndTime = DateTime.SpecifyKind(lesson.EndTime, DateTimeKind.Local);

                // Assign color based on tutor if not already set
                if (string.IsNullOrEmpty(lesson.CategoryColor) && lesson.TutorId > 0)
                {
                    lesson.CategoryColor = GetTutorColor(lesson.TutorId);
                }
            }

            // Force UI update
            // StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");

            // Fallback to sample data if API fails
            await LoadSampleData();
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadSampleData()
    {
        // Sample tutors
        tutors = new List<Tutor>
        {
            new Tutor { TutorId = 1, TutorName = "Sarah Johnson", Email = "<EMAIL>" },
            new Tutor { TutorId = 2, TutorName = "Michael Chen", Email = "<EMAIL>" }
        };

        // Sample students
        students = new List<Student>
        {
            new Student { StudentId = 1, StudentName = "Alice Smith", Email = "<EMAIL>" },
            new Student { StudentId = 2, StudentName = "Bob Brown", Email = "<EMAIL>" },
            new Student { StudentId = 3, StudentName = "Charlie Davis", Email = "<EMAIL>" }
        };

        // Sample schedule events - using specific dates that are visible
        var monday = new DateTime(2025, 6, 23); // June 23, 2025 (Monday)
        scheduleEvents = new List<ScheduleEvent>
        {
            new ScheduleEvent
            {
                Id = 1,
                Subject = "Piano Lesson - Alice",
                StartTime = monday.AddHours(10), // Monday 10:00 AM
                EndTime = monday.AddHours(11),   // Monday 11:00 AM
                Description = "Beginner piano lesson focusing on scales",
                Location = "Room A",
                TutorId = 1,
                StudentId = 1,
                TutorName = "Sarah Johnson",
                StudentName = "Alice Smith",
                CategoryColor = "#FF6B6B" // Red for Sarah Johnson
            },
            new ScheduleEvent
            {
                Id = 2,
                Subject = "Guitar Lesson - Bob",
                StartTime = monday.AddHours(14), // Monday 2:00 PM
                EndTime = monday.AddHours(15),   // Monday 3:00 PM
                Description = "Intermediate guitar lesson",
                Location = "Room B",
                TutorId = 1,
                StudentId = 2,
                TutorName = "Sarah Johnson",
                StudentName = "Bob Brown",
                CategoryColor = "#FF6B6B" // Red for Sarah Johnson
            },
            new ScheduleEvent
            {
                Id = 3,
                Subject = "Violin Lesson - Charlie",
                StartTime = monday.AddDays(1).AddHours(9),      // Tuesday 9:00 AM
                EndTime = monday.AddDays(1).AddHours(10).AddMinutes(30), // Tuesday 10:30 AM
                Description = "Advanced violin technique",
                Location = "Room C",
                TutorId = 2,
                StudentId = 3,
                TutorName = "Michael Chen",
                StudentName = "Charlie Davis",
                CategoryColor = "#4ECDC4" // Teal for Michael Chen
            },
            new ScheduleEvent
            {
                Id = 4,
                Subject = "Voice Lesson - Diana",
                StartTime = monday.AddDays(2).AddHours(11), // Wednesday 11:00 AM
                EndTime = monday.AddDays(2).AddHours(12),   // Wednesday 12:00 PM
                Description = "Vocal technique and breathing exercises",
                Location = "Room D",
                TutorId = 2,
                StudentId = 1,
                TutorName = "Michael Chen",
                StudentName = "Alice Smith",
                CategoryColor = "#4ECDC4" // Teal for Michael Chen
            },
            new ScheduleEvent
            {
                Id = 5,
                Subject = "Advanced Piano - Bob",
                StartTime = monday.AddDays(3).AddHours(16), // Thursday 4:00 PM
                EndTime = monday.AddDays(3).AddHours(17),   // Thursday 5:00 PM
                Description = "Advanced piano techniques",
                Location = "Room A",
                TutorId = 1,
                StudentId = 2,
                TutorName = "Sarah Johnson",
                StudentName = "Bob Brown",
                CategoryColor = "#FF6B6B" // Red for Sarah Johnson
            }
        };

        await Task.Delay(500); // Simulate loading time

        // Force UI update
        StateHasChanged();
    }

    private async Task AddNewLesson()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Add new lesson functionality will be implemented in the next step!");
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private List<ScheduleEvent> GetScheduleEvents()
    {
        return scheduleEvents ?? new List<ScheduleEvent>();
    }

    private void OnTutorChanged()
    {
        // This method will be called when tutor selection changes
        // Auto-assign color based on selected tutor
        // Note: This method is called in the context of the editor template
        // The actual color assignment will happen in the CRUD event handlers
        StateHasChanged();
    }

    private string GetTutorColor(int tutorId)
    {
        // Define a set of colors for tutors
        var tutorColors = new Dictionary<int, string>
        {
            { 1, "#FF6B6B" }, // Red for first tutor
            { 2, "#4ECDC4" }, // Teal for second tutor
            { 3, "#45B7D1" }, // Blue for third tutor
            { 4, "#96CEB4" }, // Green for fourth tutor
            { 5, "#FFEAA7" }, // Yellow for fifth tutor
            { 6, "#DDA0DD" }, // Plum for sixth tutor
            { 7, "#98D8C8" }, // Mint for seventh tutor
            { 8, "#F7DC6F" }  // Light yellow for eighth tutor
        };

        return tutorColors.ContainsKey(tutorId) ? tutorColors[tutorId] : "#6C757D"; // Default gray
    }

    private string GetTutorColorByName(string tutorName)
    {
        var tutor = tutors.FirstOrDefault(t => t.TutorName == tutorName);
        return tutor != null ? GetTutorColor(tutor.TutorId) : "#6C757D";
    }

    private async Task OnActionBegin(ActionEventArgs<ScheduleEvent> args)
    {
        try
        {
            switch (args.ActionType)
            {
                case ActionType.EventCreate:
                    await HandleCreateEvent(args);
                    break;
                case ActionType.EventChange:
                    await HandleUpdateEvent(args);
                    break;
                case ActionType.EventRemove:
                    await HandleDeleteEvent(args);
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnActionBegin: {ex.Message}");
            args.Cancel = true;
            await JSRuntime.InvokeVoidAsync("alert", $"Error: {ex.Message}");
        }
    }

    private async Task OnActionComplete(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.ActionType == ActionType.EventCreate ||
            args.ActionType == ActionType.EventChange ||
            args.ActionType == ActionType.EventRemove)
        {
            // Refresh the data after successful CRUD operation
            await LoadData();
            await JSRuntime.InvokeVoidAsync("console.log", "Data refreshed after CRUD operation");
        }
    }

    private async Task HandleCreateEvent(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.AddedRecords?.Any() == true)
        {
            foreach (var newEvent in args.AddedRecords)
            {
                // Ensure required fields are set
                if (newEvent.TutorId == 0 || newEvent.StudentId == 0)
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please select both a Tutor and Student for the lesson.");
                    return;
                }

                // Validate subject is not empty
                if (string.IsNullOrWhiteSpace(newEvent.Subject))
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please enter a subject for the lesson.");
                    return;
                }

                // Set TutorName and StudentName from the loaded data
                var tutor = tutors.FirstOrDefault(t => t.TutorId == newEvent.TutorId);
                var student = students.FirstOrDefault(s => s.StudentId == newEvent.StudentId);

                if (tutor != null) newEvent.TutorName = tutor.TutorName;
                if (student != null) newEvent.StudentName = student.StudentName;

                // Set color based on tutor if not already set
                if (string.IsNullOrEmpty(newEvent.CategoryColor))
                {
                    newEvent.CategoryColor = GetTutorColor(newEvent.TutorId);
                }

                // Call API to create the lesson
                var createdEvent = await LessonApi.CreateLessonAsync(newEvent);
                if (createdEvent != null)
                {
                    newEvent.Id = createdEvent.Id;
                    await JSRuntime.InvokeVoidAsync("console.log", $"Created lesson with ID: {createdEvent.Id}");
                    await JSRuntime.InvokeVoidAsync("alert", "Lesson created successfully!");
                }
                else
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to create lesson. Please try again.");
                    return;
                }
            }
        }
    }

    private async Task HandleUpdateEvent(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.ChangedRecords?.Any() == true)
        {
            foreach (var updatedEvent in args.ChangedRecords)
            {
                // Ensure required fields are set
                if (updatedEvent.TutorId == 0 || updatedEvent.StudentId == 0)
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please select both a Tutor and Student for the lesson.");
                    return;
                }

                // Validate subject is not empty
                if (string.IsNullOrWhiteSpace(updatedEvent.Subject))
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please enter a subject for the lesson.");
                    return;
                }

                // Set TutorName and StudentName from the loaded data
                var tutor = tutors.FirstOrDefault(t => t.TutorId == updatedEvent.TutorId);
                var student = students.FirstOrDefault(s => s.StudentId == updatedEvent.StudentId);

                if (tutor != null) updatedEvent.TutorName = tutor.TutorName;
                if (student != null) updatedEvent.StudentName = student.StudentName;

                // Update color based on tutor if not manually set
                if (string.IsNullOrEmpty(updatedEvent.CategoryColor))
                {
                    updatedEvent.CategoryColor = GetTutorColor(updatedEvent.TutorId);
                }

                // Call API to update the lesson
                var success = await LessonApi.UpdateLessonAsync(updatedEvent);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Updated lesson with ID: {updatedEvent.Id}");
                    await JSRuntime.InvokeVoidAsync("alert", "Lesson updated successfully!");
                }
                else
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to update lesson. Please try again.");
                    return;
                }
            }
        }
    }

    private async Task HandleDeleteEvent(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.DeletedRecords?.Any() == true)
        {
            // Ask for confirmation before deleting
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this lesson?");
            if (!confirmed)
            {
                args.Cancel = true;
                return;
            }

            foreach (var deletedEvent in args.DeletedRecords)
            {
                // Call API to delete the lesson
                var success = await LessonApi.DeleteLessonAsync(deletedEvent.Id);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Deleted lesson with ID: {deletedEvent.Id}");
                    await JSRuntime.InvokeVoidAsync("alert", "Lesson deleted successfully!");
                }
                else
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete lesson. Please try again.");
                    return;
                }
            }
        }
    }
}
