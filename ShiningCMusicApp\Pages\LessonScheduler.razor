@page "/lessons"
@using ShiningCMusicCommon.Models
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Schedule
@inject ILessonApiService LessonApi
@inject IJSRuntime JSRuntime

<PageTitle>Lesson Time Table</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">🎵 Lesson Time Table</h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading lessons...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Schedule (@scheduleEvents.Count lessons loaded)</h5>
                        @if (scheduleEvents.Any())
                        {
                            <small class="text-muted">
                                Next lesson: @scheduleEvents.FirstOrDefault()?.Subject at @scheduleEvents.FirstOrDefault()?.StartTime.ToString("g")
                            </small>
                        }
                        <div>
                            <button class="btn btn-primary btn-sm me-2" @onclick="AddNewLesson">
                                <i class="fas fa-plus"></i> Add Lesson
                            </button>
                            <button class="btn btn-secondary btn-sm" @onclick="RefreshData">
                                <i class="fas fa-refresh"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <SfSchedule TValue="ScheduleEvent"
                                   @bind-SelectedDate="@selectedDate"
                                   Height="800px"
                                   @bind-CurrentView="@currentView"
                                   FirstDayOfWeek="1"
                                   StartHour="06:00"
                                   EndHour="22:00"
                                   @ref="scheduleRef">

                            <ScheduleWorkHours Highlight="true" Start="06:00" End="22:00"></ScheduleWorkHours>

                            <ScheduleTimeScale Enable="true" Interval="60" SlotCount="1"></ScheduleTimeScale>

                            <ScheduleEventSettings DataSource="@scheduleEvents"
                                                 TValue="ScheduleEvent">
                                <Template>
                                    @{
                                        var eventData = context as ScheduleEvent;
                                    }
                                    <div class="template-wrap">
                                        <div class="subject">@(eventData?.Subject)</div>
                                        @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                        {
                                            <div class="tutor">
                                                <i class="bi bi-person-fill"></i>@(eventData.TutorName)
                                            </div>
                                        }
                                        @if (!string.IsNullOrEmpty(eventData?.StudentName))
                                        {
                                            <div class="student">
                                                <i class="bi bi-person"></i>@(eventData.StudentName)
                                            </div>
                                        }
                                    </div>
                                </Template>
                            </ScheduleEventSettings>
                            
                            <ScheduleViews>
                                <ScheduleView Option="View.Day"></ScheduleView>
                                <ScheduleView Option="View.Week"></ScheduleView>
                                <ScheduleView Option="View.WorkWeek"></ScheduleView>
                                <ScheduleView Option="View.Month"></ScheduleView>
                            </ScheduleViews>

                            <ScheduleQuickInfoTemplates>
                                <EventTemplate>
                                    @{
                                        var eventData = context as ScheduleEvent;
                                    }
                                    <div class="quick-info">
                                        <div class="event-title">@(eventData?.Subject)</div>
                                        <div class="event-details">
                                            <p>
                                                <i class="bi bi-clock"></i>
                                                @(eventData?.StartTime.ToString("MMM dd, yyyy h:mm tt")) - @(eventData?.EndTime.ToString("h:mm tt"))
                                            </p>
                                            @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                            {
                                                <p>
                                                    <i class="bi bi-person-fill"></i>
                                                    <strong>Tutor:</strong> @(eventData.TutorName)
                                                </p>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.StudentName))
                                            {
                                                <p>
                                                    <i class="bi bi-person"></i>
                                                    <strong>Student:</strong> @(eventData.StudentName)
                                                </p>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.Location))
                                            {
                                                <p>
                                                    <i class="bi bi-geo-alt"></i>
                                                    <strong>Location:</strong> @(eventData.Location)
                                                </p>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.Description))
                                            {
                                                <p>
                                                    <i class="bi bi-card-text"></i>
                                                    <strong>Description:</strong> @(eventData.Description)
                                                </p>
                                            }
                                        </div>
                                    </div>
                                </EventTemplate>
                            </ScheduleQuickInfoTemplates>
                        </SfSchedule>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<ScheduleEvent> scheduleEvents = new();
    private List<Tutor> tutors = new();
    private List<Student> students = new();
    private DateTime selectedDate = DateTime.Today;
    private View currentView = View.Week;
    private bool isLoading = true;
    private SfSchedule<ScheduleEvent>? scheduleRef;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load data from API
            var lessonsTask = LessonApi.GetLessonsAsync();
            var tutorsTask = LessonApi.GetTutorsAsync();
            var studentsTask = LessonApi.GetStudentsAsync();

            await Task.WhenAll(lessonsTask, tutorsTask, studentsTask);

            scheduleEvents = await lessonsTask;
            tutors = await tutorsTask;
            students = await studentsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {scheduleEvents.Count} lessons, {tutors.Count} tutors, {students.Count} students");

            // Debug: Log the actual lesson data and ensure proper DateTime format
            foreach (var lesson in scheduleEvents)
            {
                await JSRuntime.InvokeVoidAsync("console.log", $"Lesson: {lesson.Subject}, Start: {lesson.StartTime}, End: {lesson.EndTime}");

                // Ensure dates are in local time and properly formatted
                if (lesson.StartTime.Kind == DateTimeKind.Utc)
                {
                    lesson.StartTime = lesson.StartTime.ToLocalTime();
                }
                if (lesson.EndTime.Kind == DateTimeKind.Utc)
                {
                    lesson.EndTime = lesson.EndTime.ToLocalTime();
                }

                // Ensure dates have DateTimeKind.Local
                lesson.StartTime = DateTime.SpecifyKind(lesson.StartTime, DateTimeKind.Local);
                lesson.EndTime = DateTime.SpecifyKind(lesson.EndTime, DateTimeKind.Local);
            }

            // Force UI update
            // StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");

            // Fallback to sample data if API fails
            await LoadSampleData();
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadSampleData()
    {
        // Sample tutors
        tutors = new List<Tutor>
        {
            new Tutor { TutorId = 1, TutorName = "Sarah Johnson", Email = "<EMAIL>" },
            new Tutor { TutorId = 2, TutorName = "Michael Chen", Email = "<EMAIL>" }
        };

        // Sample students
        students = new List<Student>
        {
            new Student { StudentId = 1, StudentName = "Alice Smith", Email = "<EMAIL>" },
            new Student { StudentId = 2, StudentName = "Bob Brown", Email = "<EMAIL>" },
            new Student { StudentId = 3, StudentName = "Charlie Davis", Email = "<EMAIL>" }
        };

        // Sample schedule events - using specific dates that are visible
        var monday = new DateTime(2025, 6, 23); // June 23, 2025 (Monday)
        scheduleEvents = new List<ScheduleEvent>
        {
            new ScheduleEvent
            {
                Id = 1,
                Subject = "Piano Lesson - Alice",
                StartTime = monday.AddHours(10), // Monday 10:00 AM
                EndTime = monday.AddHours(11),   // Monday 11:00 AM
                Description = "Beginner piano lesson focusing on scales",
                Location = "Room A",
                TutorId = 1,
                StudentId = 1,
                TutorName = "Sarah Johnson",
                StudentName = "Alice Smith"
            },
            new ScheduleEvent
            {
                Id = 2,
                Subject = "Guitar Lesson - Bob",
                StartTime = monday.AddHours(14), // Monday 2:00 PM
                EndTime = monday.AddHours(15),   // Monday 3:00 PM
                Description = "Intermediate guitar lesson",
                Location = "Room B",
                TutorId = 1,
                StudentId = 2,
                TutorName = "Sarah Johnson",
                StudentName = "Bob Brown"
            },
            new ScheduleEvent
            {
                Id = 3,
                Subject = "Violin Lesson - Charlie",
                StartTime = monday.AddDays(1).AddHours(9),      // Tuesday 9:00 AM
                EndTime = monday.AddDays(1).AddHours(10).AddMinutes(30), // Tuesday 10:30 AM
                Description = "Advanced violin technique",
                Location = "Room C",
                TutorId = 2,
                StudentId = 3,
                TutorName = "Michael Chen",
                StudentName = "Charlie Davis"
            },
            new ScheduleEvent
            {
                Id = 4,
                Subject = "Voice Lesson - Diana",
                StartTime = monday.AddDays(2).AddHours(11), // Wednesday 11:00 AM
                EndTime = monday.AddDays(2).AddHours(12),   // Wednesday 12:00 PM
                Description = "Vocal technique and breathing exercises",
                Location = "Room D",
                TutorId = 2,
                StudentId = 1,
                TutorName = "Michael Chen",
                StudentName = "Alice Smith"
            },
            new ScheduleEvent
            {
                Id = 5,
                Subject = "Advanced Piano - Bob",
                StartTime = monday.AddDays(3).AddHours(16), // Thursday 4:00 PM
                EndTime = monday.AddDays(3).AddHours(17),   // Thursday 5:00 PM
                Description = "Advanced piano techniques",
                Location = "Room A",
                TutorId = 1,
                StudentId = 2,
                TutorName = "Sarah Johnson",
                StudentName = "Bob Brown"
            }
        };

        await Task.Delay(500); // Simulate loading time

        // Force UI update
        StateHasChanged();
    }

    private async Task AddNewLesson()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Add new lesson functionality will be implemented in the next step!");
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private List<ScheduleEvent> GetScheduleEvents()
    {
        return scheduleEvents ?? new List<ScheduleEvent>();
    }
}
