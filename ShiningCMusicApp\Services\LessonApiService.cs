using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public interface ILessonApiService
    {
        Task<List<ScheduleEvent>> GetLessonsAsync();
        Task<List<Tutor>> GetTutorsAsync();
        Task<List<Student>> GetStudentsAsync();
        Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);
    }

    public class LessonApiService : ILessonApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthenticationService _authService;
        private readonly string _baseUrl = "https://localhost:7268/api";

        public LessonApiService(HttpClient httpClient, IAuthenticationService authService)
        {
            _httpClient = httpClient;
            _authService = authService;
        }

        private async Task<bool> SetAuthorizationHeaderAsync()
        {
            var token = await _authService.GetAccessTokenAsync();
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            return true;
        }

        public async Task<List<ScheduleEvent>> GetLessonsAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    Console.WriteLine("Failed to get authentication token");
                    return new List<ScheduleEvent>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/lessons");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var lessons = JsonSerializer.Deserialize<List<ScheduleEvent>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return lessons ?? new List<ScheduleEvent>();
                }
                else
                {
                    Console.WriteLine($"Failed to get lessons: {response.StatusCode}");
                    return new List<ScheduleEvent>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting lessons: {ex.Message}");
                return new List<ScheduleEvent>();
            }
        }

        public async Task<List<Tutor>> GetTutorsAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<Tutor>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/tutors");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var tutors = JsonSerializer.Deserialize<List<Tutor>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return tutors ?? new List<Tutor>();
                }
                
                return new List<Tutor>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting tutors: {ex.Message}");
                return new List<Tutor>();
            }
        }

        public async Task<List<Student>> GetStudentsAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<Student>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/students");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var students = JsonSerializer.Deserialize<List<Student>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return students ?? new List<Student>();
                }
                
                return new List<Student>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting students: {ex.Message}");
                return new List<Student>();
            }
        }

        public async Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var json = JsonSerializer.Serialize(lesson);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync($"{_baseUrl}/lessons", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<ScheduleEvent>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
                
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating lesson: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UpdateLessonAsync(ScheduleEvent lesson)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var json = JsonSerializer.Serialize(lesson);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync($"{_baseUrl}/lessons/{lesson.Id}", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating lesson: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/lessons/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting lesson: {ex.Message}");
                return false;
            }
        }
    }
}
