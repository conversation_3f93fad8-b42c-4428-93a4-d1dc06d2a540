{"mainAssemblyName": "ShiningCMusicApp", "resources": {"hash": "sha256-P8E0tqPtH8Lo5ItRkMvsCH8ANz6BfFAmzT4QuAnhcnI=", "jsModuleNative": {"dotnet.native.js": "sha256-YyxJV6dv935qHoQAmjB+2qRjHeGpm4SyWxE0T/KvsIE="}, "jsModuleRuntime": {"dotnet.runtime.js": "sha256-nlm9ZX+9Z8SHWuCfVhHT5pkK9QzTgBn3opfL6mMas20="}, "wasmNative": {"dotnet.native.wasm": "sha256-Ni0Zae8QxYDdca2Vx+9v/wRw4wb3jSGA6suhCmG04n8="}, "icu": {"icudt_CJK.dat": "sha256-SZLtQnRc0JkwqHab0VUVP7T3uBPSeYzxzDnpxPpUnHk=", "icudt_EFIGS.dat": "sha256-8fItetYY8kQ0ww6oxwTLiT3oXlBwHKumbeP2pRF4yTc=", "icudt_no_CJK.dat": "sha256-L7sV7NEYP37/Qr2FPCePo5cJqRgTXRwGHuwF5Q+0Nfs="}, "coreAssembly": {}, "assembly": {"Microsoft.AspNetCore.Authorization.wasm": "sha256-5a7/RdhGgJYmku/9sroYrsn1T46a5Hi/fIg49VoQnJQ=", "Microsoft.AspNetCore.Components.wasm": "sha256-RXNLtwbclPPqHjZ5WE/aBFbGFbrMv8bsCzF5YnPCujo=", "Microsoft.AspNetCore.Components.Forms.wasm": "sha256-LhGZoIRq6DWYdRr6cN7rLQwQMW1nW2JSmItcWCgWv28=", "Microsoft.AspNetCore.Components.Web.wasm": "sha256-KuiqrLy2OLvrgS0kAYPA9boDOUoW8/VDadusmY1fXA8=", "Microsoft.AspNetCore.Components.WebAssembly.wasm": "sha256-WzN/M/0H6BRTC3zoqHCzvHBTv/FK2C+rthdZQrpuDm4=", "Microsoft.AspNetCore.Metadata.wasm": "sha256-oSvNMvCR2Lw+iehNHO0dEqtjDkhM5o7jrZyE/xtjqIQ=", "Microsoft.Extensions.Configuration.wasm": "sha256-UDGEQR7J3WTfzYMgOzxVIBwFQtKEeJvO8UgrWagypdE=", "Microsoft.Extensions.Configuration.Abstractions.wasm": "sha256-yNdqbqDWGiJo943D7LPak5xryCBEsNH0wtdiuU1R9VE=", "Microsoft.Extensions.Configuration.Binder.wasm": "sha256-fNNlyyW44y8Gp6dvOtsvHoVNzpT8lrKmPleg5CDhRAo=", "Microsoft.Extensions.Configuration.FileExtensions.wasm": "sha256-SrAtaND/3+ZSfueBc9meuKzyFgcamyVA581L4R7Amzs=", "Microsoft.Extensions.Configuration.Json.wasm": "sha256-CtLsl9w5UTJXGXTbp+SQxMzt9f/gjQ4EkywrtuCscvU=", "Microsoft.Extensions.DependencyInjection.wasm": "sha256-CS+QrPLaogl32aUg+ES1Ef5QGKoMyjt3V+wGoiG+tpw=", "Microsoft.Extensions.DependencyInjection.Abstractions.wasm": "sha256-NICKEw2hjoBhl1lvxOBh8cxOIN8kkrGHz3pvV1eLLbY=", "Microsoft.Extensions.FileProviders.Abstractions.wasm": "sha256-IUopixuV8E09mP+TIgRp/lXEbeacXbUTW7cu0v5B9Tc=", "Microsoft.Extensions.FileProviders.Physical.wasm": "sha256-MS+zB0xkKhKk/QdE32ZwKtdlrLMLI/y2NAKPUWhcBVg=", "Microsoft.Extensions.FileSystemGlobbing.wasm": "sha256-AnWUKd0qJ8ZNKkRC0AFK2cjjszB7reXiP+bXdIi8bic=", "Microsoft.Extensions.Logging.wasm": "sha256-pa0M3exxNvk6g5anUwlaPC99Afawsi1GZvJeU1q/ZGM=", "Microsoft.Extensions.Logging.Abstractions.wasm": "sha256-uf1KJp0fVm0d3Bs2JFD1oxo857dVH+MA9AF/tlH5YEw=", "Microsoft.Extensions.Options.wasm": "sha256-5/m+yVFGRuY+N4jQnD+QETKH0AfhAsnVze5dJ5ogIVM=", "Microsoft.Extensions.Primitives.wasm": "sha256-pVrYOTfjb2ITls3LKIByW1t8jwOAWFhmkIVDewtJ1GE=", "Microsoft.JSInterop.wasm": "sha256-khFRp6Mm9HUzYjx9SKp5rwzASaAj/N1CCYlVdBmCaiA=", "Microsoft.JSInterop.WebAssembly.wasm": "sha256-qfDs93QsoVEf1mb8zPoNRfw4CCgCfz3Ub7DOHUE8xt0=", "Syncfusion.Blazor.Buttons.wasm": "sha256-2Vt0fsfRFbwdsIUh4TI7ilk+2vlmPIKOQMc5j4rBeNw=", "Syncfusion.Blazor.Calendars.wasm": "sha256-zx1ZYem62FUp7ljQMOozpwp4OtHHBpaV0MQjg93ZTGQ=", "Syncfusion.Blazor.Core.wasm": "sha256-l0HLdIEnRgOxyC51Pq0SXy+CC4bJBKaHsBBmutYrr9o=", "Syncfusion.Blazor.Data.wasm": "sha256-x4Kh3Rewm1GssfUB5dzsbDaq0/OXL81SCT49IuCOxD4=", "Syncfusion.Blazor.DropDowns.wasm": "sha256-f5fuC3v/nj1eHuXdUmD7vHQN5H+ZT5mNASLUBsZnTHY=", "Syncfusion.Blazor.Inputs.wasm": "sha256-6RXpwDAl8XoxkyxF4PZ61zw2+5Plc/JzG9/iNfib0Tk=", "Syncfusion.Blazor.Lists.wasm": "sha256-d0IDfJycLGsht1WIQJ8shdIxDzXwd0tqyQ6VUgkvoec=", "Syncfusion.Blazor.Navigations.wasm": "sha256-n/LZky/S5hvNxcYN6MDl2lAbiM2Mj4b0b1nM5+RAk7Q=", "Syncfusion.Blazor.Notifications.wasm": "sha256-esDPGHi120IZu3n6KYRs+N8yw03JuhQylTz3BaWuSTc=", "Syncfusion.Blazor.Popups.wasm": "sha256-6bg+SSqTT3bu7OPZslobMAq7McQr87Pkf0bfLsrEcSA=", "Syncfusion.Blazor.Schedule.wasm": "sha256-oTKQik3l283TjF3tXiyix4Vbhoi25bkPy1iQNK++1r8=", "Syncfusion.Blazor.Spinner.wasm": "sha256-l7q/vSWzZLfp4uPfezJlmJiA8AYdxXX6tNXwOT6C8co=", "Syncfusion.Blazor.SplitButtons.wasm": "sha256-gB2Ahno4MNYzJPVCQhbhDCrLlR88+/cQx/bLVg9nyM0=", "Syncfusion.Blazor.Themes.wasm": "sha256-a1CdmVV1PMzbwqv8NQyy2WaR74QuogHc4VHTivorfxg=", "Syncfusion.ExcelExport.Net.wasm": "sha256-oKNL3Iq1zHPoYB/CRBRULqtBa3eikXHd35VSvfAfds0=", "Syncfusion.Licensing.wasm": "sha256-hGh6kQyxIrHA0MTFhDWBcUbgKNh9/+3kwaRHglRQQv0=", "System.IO.Pipelines.wasm": "sha256-JqVBy6SV+3qk+IK3VJiyIDt9CsVk6b1/ytTXmrI4mTM=", "Microsoft.CSharp.wasm": "sha256-awmKktUGZlePmPKHwJ0Et68xmx9hbrsrrV9FhpWf8s4=", "Microsoft.VisualBasic.Core.wasm": "sha256-B2gPkOdF45hQetjLKrR/SZaMNEP74vXsfvb9LpLksdU=", "Microsoft.VisualBasic.wasm": "sha256-QnkY5gz+dUPiOqsHVUbz8uZesf2i9HCU11m6+GgxtEU=", "Microsoft.Win32.Primitives.wasm": "sha256-dsAwxBJqCTx8Z4WLIrlZ7i9vCvlqF9UCO5oQpPBsbNY=", "Microsoft.Win32.Registry.wasm": "sha256-070SkVVAKs4sgE2wt+3Jw1xX5TG/ny1L3Tz6WyFav/Y=", "System.AppContext.wasm": "sha256-V6fbq0wFPDZPOr0kOeRR9drFFi7d8WIFzkypzuNGOBU=", "System.Buffers.wasm": "sha256-5z8+mZvHjpVcx24qds3EYULhFPCEBsxmFuKBJbCwOJ4=", "System.Collections.Concurrent.wasm": "sha256-61VRroFTFAuATpSpKp8Y43yz52WCPfE+/m93FbIu9tM=", "System.Collections.Immutable.wasm": "sha256-tFy/BraliCKvffGTVfGT4yRJa24eMQbR3zpbpyqRSqg=", "System.Collections.NonGeneric.wasm": "sha256-126aqphjXygMGgGgky5l44dTQ6ycxE7KR94++fOiKeo=", "System.Collections.Specialized.wasm": "sha256-wk9qyRpterBjm2mbVADTKGlMstPKMBxRGOIprBA2mLI=", "System.Collections.wasm": "sha256-fimIz+kpU7QNLBCJzfhwGHYlZ7GeSHpNr62QK2NQyWA=", "System.ComponentModel.Annotations.wasm": "sha256-nX6KXardGdXMfU8Gcjp9+jmUyNXe2IuodrScY9LSMYM=", "System.ComponentModel.DataAnnotations.wasm": "sha256-9iYiDL5EB8vadYn82ttDz0FOtZCRZeXbytbIRiYfzUo=", "System.ComponentModel.EventBasedAsync.wasm": "sha256-ccRKj3xi484PXWSL6+6ZV/b3Dh6A+DPwPUL0Kdw8iSE=", "System.ComponentModel.Primitives.wasm": "sha256-Nn1JEcDcSUswO/8ie9Twu396mrW20WOkXHwxWhn9qTk=", "System.ComponentModel.TypeConverter.wasm": "sha256-+tEmLvWxHNptR9f08cNAsxeARr3CoU33Ic5EGip4EGg=", "System.ComponentModel.wasm": "sha256-1pvq4ztv7zHsW1VRMNHt4nBX12FYDl3Q62BdjTc32lI=", "System.Configuration.wasm": "sha256-DUY0/y3paD5M1SbDuPo9TBTzetw8dPmhgJWEb7Rjcic=", "System.Console.wasm": "sha256-rRRyFjFAUQDN05wcR0TYtcvRzx2rKxA1TzM/RsF3Fu8=", "System.Core.wasm": "sha256-UsdDPO6WpoBI6lupiS6C9QcbxA1OSodk8FSAoG7umgI=", "System.Data.Common.wasm": "sha256-w8/2lSzD0BmuTWu3+8hIw9qdo9YlcV3cJk1oW2JS+So=", "System.Data.DataSetExtensions.wasm": "sha256-9rYfb7Rq3xaDeu9KZiESju2ZMy1nH/6KWmTH6z95r3I=", "System.Data.wasm": "sha256-ZLh5xZggDg2ckeFzLkGRwD3MI12z7h9f7jF6qw06BzI=", "System.Diagnostics.Contracts.wasm": "sha256-PyfarX7lhpwvysOhoo+W8wt0Oo9gRtFNWruzrV7LDvE=", "System.Diagnostics.Debug.wasm": "sha256-1QFVTz9Esqdcdp9gF5ZXrIHqdqkDeuPVvrXQpd5+TJ4=", "System.Diagnostics.DiagnosticSource.wasm": "sha256-hlS/suiVEE6UGiaoelMazhjFR6DShbvhUaujfc5dnvU=", "System.Diagnostics.FileVersionInfo.wasm": "sha256-aNKbKU2aTcb0Rs3pQF+dgdq7A7uk8pTnn7m/y3h98f4=", "System.Diagnostics.Process.wasm": "sha256-Or1prZxWahEMw4C+Auc4D+Nbm3VYNZLA3lU5YxuEqGI=", "System.Diagnostics.StackTrace.wasm": "sha256-F+DDX+sB0gzjBiox5EfzqCLqMnv43XbXIJvXn07hD70=", "System.Diagnostics.TextWriterTraceListener.wasm": "sha256-3wtvXVb+rVb9dQiFiIWajw8htm/KjSFNmLLt/cmalEM=", "System.Diagnostics.Tools.wasm": "sha256-TK5bWnhabsOnTG8q8q/I2LSXbxyi/MX8v4yp1NseuqE=", "System.Diagnostics.TraceSource.wasm": "sha256-6Y5GIjO1r5NnyCdcv4ctm9scI2csPo1JgWHULCPH8kE=", "System.Diagnostics.Tracing.wasm": "sha256-LW0Z4+WxlnO6gQgzIef8LPdCgzK2Dftg3a+DnUJzlLc=", "System.Drawing.Primitives.wasm": "sha256-KfBEOyZajSVTgaRiaZtatMcaF7YAUiBC9vqU6Zfuiss=", "System.Drawing.wasm": "sha256-er9NY9KGEhxfRi0IHRzAK7D8CTWH+up5QcgucroYs/s=", "System.Dynamic.Runtime.wasm": "sha256-+VSoAO7WPOHPGZvWv2YTeZe1/HggmyVsgj/3u6aM6Ig=", "System.Formats.Asn1.wasm": "sha256-Q0vmxz8P5dnWUCwr0ErHSACayir0nzZFTTXfec0RiG0=", "System.Formats.Tar.wasm": "sha256-pEVWOT6ChhrneUi2pxF4mI8ztoUN0sOJRsNG8hiw2nk=", "System.Globalization.Calendars.wasm": "sha256-uFJbTiEHdcuo6fVSeThYiC1X1NaSt+rYcSMtW/mJFI4=", "System.Globalization.Extensions.wasm": "sha256-WiEXiEMIyR1cPItYbAarC8lD65ElFPMQa9QwEEv91ac=", "System.Globalization.wasm": "sha256-jHWSqySCtwSMT2WW8FjVx6QqcSUyUUVAkGD5Kc1SKsw=", "System.IO.Compression.Brotli.wasm": "sha256-xSukoBGpqLFamcv/Mqu+7nGpvRoQE7IWbBJRVt4q2R8=", "System.IO.Compression.FileSystem.wasm": "sha256-rLIBeCeJYlOga1dAxnWOwZb8FPe54hIgOhjidglqbbs=", "System.IO.Compression.ZipFile.wasm": "sha256-PO1HZUD3CbwG0r/gHag+4wpg9Fr7CR3ghVcNW3ZvGx0=", "System.IO.Compression.wasm": "sha256-dIgM23P8+rkHRZ3tYoqoJyqEwGZxDEIGFrLxLLfmHI0=", "System.IO.FileSystem.AccessControl.wasm": "sha256-xbjYm9b3qg8rHWFt/dXpF1ld7PE91zd4EkQ0JPsN9r0=", "System.IO.FileSystem.DriveInfo.wasm": "sha256-f4H+JCrLBRbEQHZOYSl8qyuapH2h+cJgzQlaJESn4Wo=", "System.IO.FileSystem.Primitives.wasm": "sha256-v68EiN40QwXCE7By0MOy4p+5kBRByIdol11rku0ivsE=", "System.IO.FileSystem.Watcher.wasm": "sha256-2vrA5XJ/ZapEF26G1iqNLcKIcskHyWWIpbkl6FjucUI=", "System.IO.FileSystem.wasm": "sha256-gzzpNfQQYUHMby6ZEzAZIenm9IcZGnSj4bwPOCzjLEw=", "System.IO.IsolatedStorage.wasm": "sha256-62GG/WZs/xVOk6zK+RzmFGV+kvME9YXmh2F61ShANzc=", "System.IO.MemoryMappedFiles.wasm": "sha256-tGv2Oi7nYU83TrciHxzuiD7Fou0xmfGb6v2jeYisqLs=", "System.IO.Pipes.AccessControl.wasm": "sha256-0nIaMm1C6GlgV1kxLV/+2G3z6nABRHO677ZriW50uyA=", "System.IO.Pipes.wasm": "sha256-uXqaWoiwDVSoBwCLcwO+ecpa3TlAAHwlpPIODVRBIeg=", "System.IO.UnmanagedMemoryStream.wasm": "sha256-45JWQBrUHIV9y+ur3LMfToBIhlcb3Nv2iCe0XyIEEyU=", "System.IO.wasm": "sha256-zbrUPGEPua4Pk6Q+GwNtKGiVs/7Gls0O56u1TtJ6kV4=", "System.Linq.Expressions.wasm": "sha256-0+MxboQWfKpgYXZQ/NUcGm6UPjR/eXYxqdYTnmaOLgE=", "System.Linq.Parallel.wasm": "sha256-rb+/AF2G4fGrfEqK1qoOs4ReLsfyTMx2gii3JjvIfiA=", "System.Linq.Queryable.wasm": "sha256-bjS8dIaCgr72aik74H9E92edEhluH6DdcltNdxUKwHA=", "System.Linq.wasm": "sha256-FzMiAVM8XqXIdg+kn5m1ZoJ03scBXtGXkVCLfpkQigE=", "System.Memory.wasm": "sha256-T9QZo3b8+pliTM8QKMkNaaPiQkEOx+1uHNjA6wDn/YY=", "System.Net.Http.Json.wasm": "sha256-G/HP8Bn7u4h79fVOVBPsOZbx2evyXncoDCOudL3G50I=", "System.Net.Http.wasm": "sha256-V8sHfBob9vzVvxr4Wg88KXlmBdvXAoLwGqLm5D2WpoQ=", "System.Net.HttpListener.wasm": "sha256-ho7KcLwDjTQUD08g3UprDqPxTzk14V6/AYIssotKdfg=", "System.Net.Mail.wasm": "sha256-Sh7uekgMvIG8eK0JmriW1dtN4lT/xSTMQQNXtM13wlE=", "System.Net.NameResolution.wasm": "sha256-jMNinJrjdVhUK/hgTHBCRqO84QkJMigUfj/jeqIBpzQ=", "System.Net.NetworkInformation.wasm": "sha256-hhWRVGEgEZG3tWuzfCSsQel1Mi8Jhx2xkDrbet2ENgc=", "System.Net.Ping.wasm": "sha256-mZ/BnuCvstmnszC2UFH4EcrtCLEntAFeHY1J1p/6ckk=", "System.Net.Primitives.wasm": "sha256-xVLW8QDb+zAFYqSrZNehSHNfmfIwrLy5BCMvormnAzM=", "System.Net.Quic.wasm": "sha256-DXzpVwUBL3WnMDEPVaZofnZrqHrixgNPyhS/6aZmlqE=", "System.Net.Requests.wasm": "sha256-ubpGbS8rQZQqyRIB8SL7xOGj1zUrCM2D2Vo4WExHrtU=", "System.Net.Security.wasm": "sha256-GLzRHLVQLPZ7q5+XsvW0VJD4EiHJNB79o3YL1GpBq04=", "System.Net.ServicePoint.wasm": "sha256-MVocKmYB62yQEW6Cd0rDbJuWWtQ03b2VjzwhmlFwWOc=", "System.Net.Sockets.wasm": "sha256-/Q9Mo1DlLf2E3z87GxGRliRsenxROLdFlokNPUHY5MQ=", "System.Net.WebClient.wasm": "sha256-9G8UQ2B65FXJh1aKbLT4Z5Vl2A86LeLhdUrLykSFIJM=", "System.Net.WebHeaderCollection.wasm": "sha256-OuUb3mciM1amLDtFdOGLwoKwshAtZMTe8tSREGJLM0Y=", "System.Net.WebProxy.wasm": "sha256-RJ4mqYpQ5BrpxXARSOp62A2+GSdvAkFhg/bTleK5fms=", "System.Net.WebSockets.Client.wasm": "sha256-12jOP5uxhLXPV+uypca+ZWfk05I/L0tUahF6ju/8p1k=", "System.Net.WebSockets.wasm": "sha256-YWPCkZWdVFQQWe+vGOnfhQCRE3WJmEK4nTuedlVSFUk=", "System.Net.wasm": "sha256-jKKs1sAOemNQYHSsvM0PRtFr/zFiCGoR1RR81Suvvs4=", "System.Numerics.Vectors.wasm": "sha256-/kh23awu01JqYtKxBOch54QRW70vZAFW7MV9nWGJM0M=", "System.Numerics.wasm": "sha256-bE5/FZLg3pwsGmfXXv4W2W1n8jU5HURKtgwC/ioFWc0=", "System.ObjectModel.wasm": "sha256-KLAc/CRUvmn7QrYDrv0zWxSskcNfgIInTYvQH40GtZ0=", "System.Private.DataContractSerialization.wasm": "sha256-S1UL2fQ9jZenKSERmmp2sf+MWO7tG4L0kOSCoLn8vfk=", "System.Private.Uri.wasm": "sha256-tc3hYfX6+JCeI1liX5tq3gZcW7f3euYndrU6TTeReE4=", "System.Private.Xml.Linq.wasm": "sha256-34ElFZbomT4hBLIJE4l9H2oEjwT7ONtnupggH7lcV8w=", "System.Private.Xml.wasm": "sha256-CREcuHx0dnNE+bMicTZ+DVjjyYcjkHLv16eRR2AYzGI=", "System.Reflection.DispatchProxy.wasm": "sha256-8BJHlNw0MgjaG3vbDSWtwzvY6TMXGeuk9Eyu3LuGOr4=", "System.Reflection.Emit.ILGeneration.wasm": "sha256-Y0fDWXajqDvJKgCc4uT4+ovk/gR01KsgrXt7zCMgOTM=", "System.Reflection.Emit.Lightweight.wasm": "sha256-dXoT4nm33I/qCv6o0N8tsf9HIgGmx0p4nZzaWa+bBm0=", "System.Reflection.Emit.wasm": "sha256-R+PVElEjQAhePca4ZYrXIsvh52wUOEYckLGksXRgU5o=", "System.Reflection.Extensions.wasm": "sha256-aY5/35Ew5af1a2Q50MivMSOVCsOdrG81r20JCc0d9js=", "System.Reflection.Metadata.wasm": "sha256-w7QaCYWHn8q/M93IhIEmT+yjEvD0jqMA1cZZiuKap7o=", "System.Reflection.Primitives.wasm": "sha256-TOuI5L6p6/G1L0I5MdI8/8Aa7HYLS1t/JmVaFtLSoGk=", "System.Reflection.TypeExtensions.wasm": "sha256-FJdCjJNxVlpS6baHDVNnq/os47+3/B52eVWv/gRI6eU=", "System.Reflection.wasm": "sha256-erYB4wvzF331oJhbsGesYiwmm1QceFuzniDDIc3QIAI=", "System.Resources.Reader.wasm": "sha256-i+AE2+ELJQACtEERrYHqM1GkI+ywm8w8wzyi6xmUEBQ=", "System.Resources.ResourceManager.wasm": "sha256-jWMnKBgLuJ5S7Lze75+gKqdJi7EbljVnRJA0hhwF/SU=", "System.Resources.Writer.wasm": "sha256-ry7l6sLkgu4Kqg9iKLIlTkeG6UO5yfWk6hk6p8gKnmg=", "System.Runtime.CompilerServices.Unsafe.wasm": "sha256-m+mZab7AGVn44XtAhXsCX7/fi5z/tpEfiTNNoNX7SJY=", "System.Runtime.CompilerServices.VisualC.wasm": "sha256-7IloKzFv9NqcuV/r5hcNTPooQn7Oy10S/WwUIs93x4Y=", "System.Runtime.Extensions.wasm": "sha256-8x3NEjx+sdY3RPQxq9C4o7uV1vJfla81jSwp3p1sYNU=", "System.Runtime.Handles.wasm": "sha256-4l8Pj3CaGdPzJSxZmys/Zrkb8Wid3LeuELF0r6TnRso=", "System.Runtime.InteropServices.JavaScript.wasm": "sha256-wY6R5BUfbde4sXwbipVYzelDoW7DxvB0Fy8SHL1hS8A=", "System.Runtime.InteropServices.RuntimeInformation.wasm": "sha256-osqce1XXJjY9SLkE/cx2L8npxB7PGXiSsofabq6fSME=", "System.Runtime.InteropServices.wasm": "sha256-BizCvF380OSX6s9hVPJlz+FCG5HxodvomK6zQ0QZoxU=", "System.Runtime.Intrinsics.wasm": "sha256-U9+4JuOknyfwgCIutCPGe5Ya3o5ClCgyTF1w9Eh2Z/g=", "System.Runtime.Loader.wasm": "sha256-BXeI+3McX0Wa7LFnyByGXlxrHnsU7fsrMlcx+pzrIj4=", "System.Runtime.Numerics.wasm": "sha256-4s9GEM02xEAL5V8BgmGsGakOUvF29jcJNUMiHeJKQ/o=", "System.Runtime.Serialization.Formatters.wasm": "sha256-n67VoxvPqslJpWoLL8TLWCRKcXhkqw60lyYKESx5N5w=", "System.Runtime.Serialization.Json.wasm": "sha256-bA90jJM4oylakMX0ED86ADV2zneTwSnbjLjL2P9R8C8=", "System.Runtime.Serialization.Primitives.wasm": "sha256-R41RB/Z/T496yf4Li1/7IqRoXGTijbjpMy2NNLgNycw=", "System.Runtime.Serialization.Xml.wasm": "sha256-YUf130pb5q/nYS0HkRPrgiAR3J1DB4oTX+MJK/5mH7c=", "System.Runtime.Serialization.wasm": "sha256-uQNmzL9UC+SVkqBMqfVWg6/gjhbwoRU1dxeUJ827Cr0=", "System.Runtime.wasm": "sha256-2SD16MZJ/n9QZOwcBwKFBOPzX4wDJ1HHQ//dmq0/jow=", "System.Security.AccessControl.wasm": "sha256-xRnk5cCD4KBIITq59xnOGdo7MQAb2HVh/Hg3RxvTAsY=", "System.Security.Claims.wasm": "sha256-IpHlxS0iAYVLMHNdk7uJD8KzRwqQJRWfAz2dSS7+R4Q=", "System.Security.Cryptography.Algorithms.wasm": "sha256-COFK9voWz4hDdr8dBfONNEwZIiX+Q3mdtaxzCr0cOis=", "System.Security.Cryptography.Cng.wasm": "sha256-8ywOF7hvEoP/OjLk9yG52OMUmltmjg70pVaeby/G+U8=", "System.Security.Cryptography.Csp.wasm": "sha256-Hr7+4GF2Dex0gAL8p2lIX6CA6qD0uiGgt/W1mMBXZvs=", "System.Security.Cryptography.Encoding.wasm": "sha256-VUyQ+mp335+64vC6VsWFrq57i4Lf3wodJczSEWpZveg=", "System.Security.Cryptography.OpenSsl.wasm": "sha256-DXouNNKta2VQjmHFwJ+FoeBe1Esvt5bY6hrpADLvIhU=", "System.Security.Cryptography.Primitives.wasm": "sha256-1ZVK2hBc4zBiPYrJ03xgMde+hiLDCTxR+AXD9sNQHdY=", "System.Security.Cryptography.X509Certificates.wasm": "sha256-smn3z7+MFuSe12X9etz7g4GIdd+IXPlZW4b9kUlxV6U=", "System.Security.Cryptography.wasm": "sha256-RtShlR6PNRaQgn9oBI58tx7g/GS/IC1DsxDw060+A8I=", "System.Security.Principal.Windows.wasm": "sha256-MZRMrHmmbQLXIEkrjGgt8H1KvfS2JaCR3hjilQOn2MA=", "System.Security.Principal.wasm": "sha256-9lmayHlLxKzXfn2VpMErqeKKcr1oUCsZ7oAYB0qWoEs=", "System.Security.SecureString.wasm": "sha256-9GnzuOJOG+gBq+hpuIAPNxna0vnk3w8beQpFnnYP6Qc=", "System.Security.wasm": "sha256-nyFYrGOQf3HEghJByP3RBraG+6gHGncZ7mx7ATumhUg=", "System.ServiceModel.Web.wasm": "sha256-VwC37ovnJtOYU5123KKCUypWW12D78BswmhyBuDX7Zo=", "System.ServiceProcess.wasm": "sha256-yWQfElLYUO+nkqIsGSOW30j9kDlbeULBnfE0OOZ5oQ0=", "System.Text.Encoding.CodePages.wasm": "sha256-fCHK1kqBJi3pZwdO6UBAccKFMTxYcmGz0fibEXqIqrw=", "System.Text.Encoding.Extensions.wasm": "sha256-d2oFP8LdqMvHx7o5uSUkCvPychZU961HAXe6haiv72g=", "System.Text.Encoding.wasm": "sha256-Gccs8HZ8uDM0uylyygpS9tNiXIV/Ni1HFCkxkQoOSgA=", "System.Text.Encodings.Web.wasm": "sha256-yqe6wCfZtZolwZlZ2CsnQnd7bOI8A94V7W1ggtg7CGo=", "System.Text.Json.wasm": "sha256-AFuNoTvFueTxHB7oi0DYCKtjHXz5+NpPTi2KuU4oV+8=", "System.Text.RegularExpressions.wasm": "sha256-h/M0aNpbfkWOYvtO9kAYIdTF7Pmhgyb2qc4eWSF0O7o=", "System.Threading.Channels.wasm": "sha256-+R22vqqoL445SNCPwrsS9KALvRO/L3/FfHJ/m3+t3k4=", "System.Threading.Overlapped.wasm": "sha256-1n1g5dfvlZUtJKvZ6hKsguH/R9EFsgFgoUItHSg6DNM=", "System.Threading.Tasks.Dataflow.wasm": "sha256-OmbSFD6tHEzyjXSbXNouYoJHXBHcf3idsOY80ZKbdkU=", "System.Threading.Tasks.Extensions.wasm": "sha256-KiY+eT4rXjasJ72FL2nclX4OzbgCbsuqyXOaEbKMOcU=", "System.Threading.Tasks.Parallel.wasm": "sha256-o8q6+e1bM3lVNH+48z+k3o1ID/AnHBvlkYAm9R9Yc6I=", "System.Threading.Tasks.wasm": "sha256-QmpKQqcK1PBW6RezhySfkuGW8xG3hDtdc3sfK2/mA00=", "System.Threading.Thread.wasm": "sha256-mnz1nJ7tLTKdORqj/SAET7qVMHsZpwnKeMfo1xaoDoI=", "System.Threading.ThreadPool.wasm": "sha256-3Cu/aaNom3FzEMspq65D1g6j/td1mik7enIDDbOYU14=", "System.Threading.Timer.wasm": "sha256-g2Q8yfhdyAzi5GO0VHdtfrShruyfHkFZecpfpUXRMsU=", "System.Threading.wasm": "sha256-XYq+VRYbJlXtCttioVJuDS8W3z0569m7ZBGJJoYRZX8=", "System.Transactions.Local.wasm": "sha256-y49q7mtLu306d38hw3GKThLmOiddw1gbfioNKVeehYs=", "System.Transactions.wasm": "sha256-xda9UG9UJO5EuRKl5tN6D5Vpikd4uDrCRJaVtIviPWo=", "System.ValueTuple.wasm": "sha256-EoI+7HrkECrxgcnvXweYXmWPwQRzlJ9VC3/xm4ikgas=", "System.Web.HttpUtility.wasm": "sha256-/GekeHFu2+MFz55/SQ2WaA/QIW6IZ/goPzBwR60oM70=", "System.Web.wasm": "sha256-rWQiijNy4sXqgX7t6QJiBH4wZkPBRJPUrqdwGuVUIAM=", "System.Windows.wasm": "sha256-FUC3qyiZ1M+4Zn398IhoBpItJsnVaT2zyizBRg33xvA=", "System.Xml.Linq.wasm": "sha256-+Du87F5UDXnejSWfyc66xHxw3AD6TQ1xxNjXCPc2PX0=", "System.Xml.ReaderWriter.wasm": "sha256-GweyWiZAneUXk5P4VJn2nbbi9e1QvFXyUghKob4vGEY=", "System.Xml.Serialization.wasm": "sha256-22uDmDwUTTI6sxwOrkKzEwayaF4jvZ+SJS1Wmi5z3WI=", "System.Xml.XDocument.wasm": "sha256-yk0EKQLV4NtfOMST+msiaN8N1oTj9PK736ZjKkY3WdM=", "System.Xml.XPath.XDocument.wasm": "sha256-mbt0rKHoxuQcHbhlXUV16XaemWZyZjFgGsV6nxYrdFI=", "System.Xml.XPath.wasm": "sha256-Lq0gK0+UPGkf8ru+Va2+ijfaa6mzw22wgd1V7Up7dWs=", "System.Xml.XmlDocument.wasm": "sha256-OSNGt0bQy+Ots6a4vr3t5pm+6O4c4ZUCdXvthmw2TZM=", "System.Xml.XmlSerializer.wasm": "sha256-a2n4NljHCk+cAlqGRzYXQOmfyVE+F0MxrtVa+8QHAE0=", "System.Xml.wasm": "sha256-jsSyUqg8sLgHjZCqEswH97pSyBVXZW9H09dCf8zzrmY=", "System.wasm": "sha256-9FhufxDkRDCXqUvAMeL+jblKzPmWrt8iucsTv+WErcQ=", "WindowsBase.wasm": "sha256-FlJ2nafNrWiJjHfIM3jHmENrSQE2LuaJULuhcZgE9Qs=", "mscorlib.wasm": "sha256-flmBXVpp8uDpWBNEJuRkqo3Qnir1KADo5a1rCQISkPs=", "netstandard.wasm": "sha256-iuCVrIBmiGbXpNVvYkBmSt16BNEjbbD82vYrtHimftc=", "System.Private.CoreLib.wasm": "sha256-1Xa/xaUoA/iRxpDqZPUUzL8QaTawkoJLhGmxtRv9ZJI=", "ShiningCMusicCommon.wasm": "sha256-OscVeWfZwLV0GEN4vloAsowmmzirFGJrTbVJxOLmuos=", "ShiningCMusicApp.wasm": "sha256-6crOu+woGZ02E2gV3NHaugUUljYIU7pLzPlXD1/7mAM="}, "pdb": {"ShiningCMusicCommon.pdb": "sha256-qyV662OQdOaL2ey6NZh97ipgF19ZcMQhWyPsXHgy2b8=", "ShiningCMusicApp.pdb": "sha256-bOFuVCHoHmy6hBKe79fw8VuvFeKgwMj15+O6H09N1uc="}}, "cacheBootResources": true, "debugLevel": -1, "globalizationMode": "sharded", "extensions": {"blazor": {}}}